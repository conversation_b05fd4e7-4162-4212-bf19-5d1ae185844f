const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
require('dotenv').config();

const logger = require('./utils/logger');
const { validateConnection, validateSignalingMessage } = require('./utils/validation');
const { authenticateSocket } = require('./middleware/auth');
const ConnectionManager = require('./managers/ConnectionManager');
const CallManager = require('./managers/CallManager');
const MetricsManager = require('./managers/MetricsManager');

// Initialize Express app
const app = express();
const server = http.createServer(app);

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true
}));

app.use(express.json());

// Initialize Socket.IO
const io = socketIo(server, {
  cors: {
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
    methods: ['GET', 'POST'],
    credentials: true
  },
  pingTimeout: 60000,
  pingInterval: 25000,
  transports: ['websocket', 'polling']
});

// Initialize managers
const connectionManager = new ConnectionManager();
const callManager = new CallManager();
const metricsManager = new MetricsManager();

// Health check endpoint
app.get('/health', (req, res) => {
  const stats = {
    status: 'healthy',
    uptime: process.uptime(),
    connections: connectionManager.getConnectionCount(),
    activeCalls: callManager.getActiveCallCount(),
    memory: process.memoryUsage(),
    timestamp: new Date().toISOString()
  };
  res.json(stats);
});

// Metrics endpoint
app.get('/metrics', (req, res) => {
  res.json(metricsManager.getMetrics());
});

// Socket.IO connection handling
io.use(authenticateSocket);

io.on('connection', (socket) => {
  logger.info('New socket connection', { 
    socketId: socket.id,
    userId: socket.userId,
    userAgent: socket.handshake.headers['user-agent']
  });

  // Register user connection
  connectionManager.addConnection(socket.userId, socket.id, socket);
  metricsManager.incrementConnections();

  // Handle user joining a call
  socket.on('join-call', async (data) => {
    try {
      const validation = validateSignalingMessage(data, ['call_id']);
      if (!validation.isValid) {
        socket.emit('error', { 
          type: 'validation_error', 
          message: validation.error,
          event: 'join-call'
        });
        return;
      }

      const { call_id } = data;
      
      // Join the call room
      await socket.join(`call:${call_id}`);
      
      // Register the call participation
      callManager.addParticipant(call_id, socket.userId, socket.id);
      
      logger.info('User joined call', { 
        userId: socket.userId, 
        callId: call_id,
        socketId: socket.id
      });

      // Notify other participants
      socket.to(`call:${call_id}`).emit('user-joined', {
        user_id: socket.userId,
        call_id: call_id,
        timestamp: new Date().toISOString()
      });

      // Send confirmation to the user
      socket.emit('call-joined', {
        call_id: call_id,
        participants: callManager.getCallParticipants(call_id),
        timestamp: new Date().toISOString()
      });

      metricsManager.incrementCallJoins();

    } catch (error) {
      logger.error('Error joining call', { 
        error: error.message, 
        userId: socket.userId,
        data 
      });
      socket.emit('error', { 
        type: 'join_call_error', 
        message: 'Failed to join call',
        event: 'join-call'
      });
    }
  });

  // Handle WebRTC offer
  socket.on('webrtc-offer', (data) => {
    try {
      const validation = validateSignalingMessage(data, ['call_id', 'target_user_id', 'offer']);
      if (!validation.isValid) {
        socket.emit('error', { 
          type: 'validation_error', 
          message: validation.error,
          event: 'webrtc-offer'
        });
        return;
      }

      const { call_id, target_user_id, offer } = data;

      // Verify the sender is part of the call
      if (!callManager.isParticipant(call_id, socket.userId)) {
        socket.emit('error', { 
          type: 'unauthorized', 
          message: 'You are not a participant in this call',
          event: 'webrtc-offer'
        });
        return;
      }

      // Get target user's socket
      const targetSocket = connectionManager.getUserSocket(target_user_id);
      if (!targetSocket) {
        socket.emit('error', { 
          type: 'user_not_found', 
          message: 'Target user is not connected',
          event: 'webrtc-offer'
        });
        return;
      }

      // Forward the offer to the target user
      targetSocket.emit('webrtc-offer-received', {
        call_id: call_id,
        from_user_id: socket.userId,
        offer: offer,
        timestamp: new Date().toISOString()
      });

      logger.info('WebRTC offer forwarded', { 
        fromUserId: socket.userId, 
        toUserId: target_user_id, 
        callId: call_id 
      });

      metricsManager.incrementSignalingMessages('offer');

    } catch (error) {
      logger.error('Error handling WebRTC offer', { 
        error: error.message, 
        userId: socket.userId,
        data 
      });
      socket.emit('error', { 
        type: 'offer_error', 
        message: 'Failed to send offer',
        event: 'webrtc-offer'
      });
    }
  });

  // Handle WebRTC answer
  socket.on('webrtc-answer', (data) => {
    try {
      const validation = validateSignalingMessage(data, ['call_id', 'target_user_id', 'answer']);
      if (!validation.isValid) {
        socket.emit('error', { 
          type: 'validation_error', 
          message: validation.error,
          event: 'webrtc-answer'
        });
        return;
      }

      const { call_id, target_user_id, answer } = data;

      // Verify the sender is part of the call
      if (!callManager.isParticipant(call_id, socket.userId)) {
        socket.emit('error', { 
          type: 'unauthorized', 
          message: 'You are not a participant in this call',
          event: 'webrtc-answer'
        });
        return;
      }

      // Get target user's socket
      const targetSocket = connectionManager.getUserSocket(target_user_id);
      if (!targetSocket) {
        socket.emit('error', { 
          type: 'user_not_found', 
          message: 'Target user is not connected',
          event: 'webrtc-answer'
        });
        return;
      }

      // Forward the answer to the target user
      targetSocket.emit('webrtc-answer-received', {
        call_id: call_id,
        from_user_id: socket.userId,
        answer: answer,
        timestamp: new Date().toISOString()
      });

      logger.info('WebRTC answer forwarded', { 
        fromUserId: socket.userId, 
        toUserId: target_user_id, 
        callId: call_id 
      });

      metricsManager.incrementSignalingMessages('answer');

    } catch (error) {
      logger.error('Error handling WebRTC answer', { 
        error: error.message, 
        userId: socket.userId,
        data 
      });
      socket.emit('error', { 
        type: 'answer_error', 
        message: 'Failed to send answer',
        event: 'webrtc-answer'
      });
    }
  });

  // Handle ICE candidates
  socket.on('ice-candidate', (data) => {
    try {
      const validation = validateSignalingMessage(data, ['call_id', 'target_user_id', 'candidate']);
      if (!validation.isValid) {
        socket.emit('error', { 
          type: 'validation_error', 
          message: validation.error,
          event: 'ice-candidate'
        });
        return;
      }

      const { call_id, target_user_id, candidate } = data;

      // Verify the sender is part of the call
      if (!callManager.isParticipant(call_id, socket.userId)) {
        socket.emit('error', { 
          type: 'unauthorized', 
          message: 'You are not a participant in this call',
          event: 'ice-candidate'
        });
        return;
      }

      // Get target user's socket
      const targetSocket = connectionManager.getUserSocket(target_user_id);
      if (!targetSocket) {
        // ICE candidates can be sent even if target is temporarily disconnected
        logger.warn('Target user not connected for ICE candidate', { 
          fromUserId: socket.userId, 
          toUserId: target_user_id, 
          callId: call_id 
        });
        return;
      }

      // Forward the ICE candidate to the target user
      targetSocket.emit('ice-candidate-received', {
        call_id: call_id,
        from_user_id: socket.userId,
        candidate: candidate,
        timestamp: new Date().toISOString()
      });

      logger.debug('ICE candidate forwarded', { 
        fromUserId: socket.userId, 
        toUserId: target_user_id, 
        callId: call_id 
      });

      metricsManager.incrementSignalingMessages('ice-candidate');

    } catch (error) {
      logger.error('Error handling ICE candidate', { 
        error: error.message, 
        userId: socket.userId,
        data 
      });
    }
  });

  // Handle leaving a call
  socket.on('leave-call', (data) => {
    try {
      const validation = validateSignalingMessage(data, ['call_id']);
      if (!validation.isValid) {
        socket.emit('error', { 
          type: 'validation_error', 
          message: validation.error,
          event: 'leave-call'
        });
        return;
      }

      const { call_id } = data;
      
      handleUserLeaveCall(socket, call_id);

    } catch (error) {
      logger.error('Error leaving call', { 
        error: error.message, 
        userId: socket.userId,
        data 
      });
    }
  });

  // Handle connection state updates
  socket.on('connection-state', (data) => {
    try {
      const validation = validateSignalingMessage(data, ['call_id', 'state']);
      if (!validation.isValid) {
        socket.emit('error', { 
          type: 'validation_error', 
          message: validation.error,
          event: 'connection-state'
        });
        return;
      }

      const { call_id, state, target_user_id } = data;

      // Verify the sender is part of the call
      if (!callManager.isParticipant(call_id, socket.userId)) {
        return;
      }

      // Broadcast to all participants in the call or specific target
      if (target_user_id) {
        const targetSocket = connectionManager.getUserSocket(target_user_id);
        if (targetSocket) {
          targetSocket.emit('connection-state-update', {
            call_id: call_id,
            from_user_id: socket.userId,
            state: state,
            timestamp: new Date().toISOString()
          });
        }
      } else {
        socket.to(`call:${call_id}`).emit('connection-state-update', {
          call_id: call_id,
          from_user_id: socket.userId,
          state: state,
          timestamp: new Date().toISOString()
        });
      }

      logger.debug('Connection state updated', { 
        userId: socket.userId, 
        callId: call_id, 
        state: state 
      });

    } catch (error) {
      logger.error('Error updating connection state', { 
        error: error.message, 
        userId: socket.userId,
        data 
      });
    }
  });

  // Handle disconnection
  socket.on('disconnect', (reason) => {
    logger.info('Socket disconnected', { 
      socketId: socket.id,
      userId: socket.userId,
      reason: reason
    });

    // Remove user from all calls
    const userCalls = callManager.getUserCalls(socket.userId);
    userCalls.forEach(callId => {
      handleUserLeaveCall(socket, callId);
    });

    // Remove connection
    connectionManager.removeConnection(socket.userId, socket.id);
    metricsManager.decrementConnections();
  });

  // Handle errors
  socket.on('error', (error) => {
    logger.error('Socket error', { 
      socketId: socket.id,
      userId: socket.userId,
      error: error.message 
    });
  });
});

// Helper function to handle user leaving a call
function handleUserLeaveCall(socket, callId) {
  // Leave the socket room
  socket.leave(`call:${callId}`);
  
  // Remove from call manager
  callManager.removeParticipant(callId, socket.userId);
  
  // Notify other participants
  socket.to(`call:${callId}`).emit('user-left', {
    user_id: socket.userId,
    call_id: callId,
    timestamp: new Date().toISOString()
  });

  logger.info('User left call', { 
    userId: socket.userId, 
    callId: callId 
  });

  metricsManager.incrementCallLeaves();
}

// Error handling
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', { reason, promise });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

// Start server
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  logger.info(`WebRTC Signaling Server running on port ${PORT}`);
  logger.info('Environment:', process.env.NODE_ENV || 'development');
});

module.exports = { app, server, io };
