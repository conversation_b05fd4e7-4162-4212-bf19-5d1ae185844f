// Mocks generated by Mockito 5.4.5 from annotations
// in fuodz/test/services/call_manager_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:flutter_webrtc/flutter_webrtc.dart' as _i2;
import 'package:fuodz/services/api_service.dart' as _i6;
import 'package:fuodz/services/webrtc_service.dart' as _i3;
import 'package:fuodz/services/websocket_service.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i7;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeMediaStream_0 extends _i1.SmartFake implements _i2.MediaStream {
  _FakeMediaStream_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRTCSessionDescription_1 extends _i1.SmartFake
    implements _i2.RTCSessionDescription {
  _FakeRTCSessionDescription_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [WebRTCService].
///
/// See the documentation for Mockito's code generation for more information.
class MockWebRTCService extends _i1.Mock implements _i3.WebRTCService {
  MockWebRTCService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set onLocalStream(dynamic Function(_i2.MediaStream)? _onLocalStream) =>
      super.noSuchMethod(
        Invocation.setter(
          #onLocalStream,
          _onLocalStream,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onRemoteStream(dynamic Function(_i2.MediaStream)? _onRemoteStream) =>
      super.noSuchMethod(
        Invocation.setter(
          #onRemoteStream,
          _onRemoteStream,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onConnectionStateChange(
          dynamic Function(_i2.RTCPeerConnectionState)?
              _onConnectionStateChange) =>
      super.noSuchMethod(
        Invocation.setter(
          #onConnectionStateChange,
          _onConnectionStateChange,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onIceCandidate(dynamic Function(_i2.RTCIceCandidate)? _onIceCandidate) =>
      super.noSuchMethod(
        Invocation.setter(
          #onIceCandidate,
          _onIceCandidate,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onError(dynamic Function(String)? _onError) => super.noSuchMethod(
        Invocation.setter(
          #onError,
          _onError,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get isInitialized => (super.noSuchMethod(
        Invocation.getter(#isInitialized),
        returnValue: false,
      ) as bool);

  @override
  bool get hasLocalStream => (super.noSuchMethod(
        Invocation.getter(#hasLocalStream),
        returnValue: false,
      ) as bool);

  @override
  bool get hasRemoteStream => (super.noSuchMethod(
        Invocation.getter(#hasRemoteStream),
        returnValue: false,
      ) as bool);

  @override
  bool get isVideoEnabled => (super.noSuchMethod(
        Invocation.getter(#isVideoEnabled),
        returnValue: false,
      ) as bool);

  @override
  bool get isAudioEnabled => (super.noSuchMethod(
        Invocation.getter(#isAudioEnabled),
        returnValue: false,
      ) as bool);

  @override
  _i4.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<_i2.MediaStream> getUserMedia({
    bool? video = true,
    bool? audio = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getUserMedia,
          [],
          {
            #video: video,
            #audio: audio,
          },
        ),
        returnValue: _i4.Future<_i2.MediaStream>.value(_FakeMediaStream_0(
          this,
          Invocation.method(
            #getUserMedia,
            [],
            {
              #video: video,
              #audio: audio,
            },
          ),
        )),
      ) as _i4.Future<_i2.MediaStream>);

  @override
  _i4.Future<_i2.RTCSessionDescription> createOffer() => (super.noSuchMethod(
        Invocation.method(
          #createOffer,
          [],
        ),
        returnValue: _i4.Future<_i2.RTCSessionDescription>.value(
            _FakeRTCSessionDescription_1(
          this,
          Invocation.method(
            #createOffer,
            [],
          ),
        )),
      ) as _i4.Future<_i2.RTCSessionDescription>);

  @override
  _i4.Future<_i2.RTCSessionDescription> createAnswer() => (super.noSuchMethod(
        Invocation.method(
          #createAnswer,
          [],
        ),
        returnValue: _i4.Future<_i2.RTCSessionDescription>.value(
            _FakeRTCSessionDescription_1(
          this,
          Invocation.method(
            #createAnswer,
            [],
          ),
        )),
      ) as _i4.Future<_i2.RTCSessionDescription>);

  @override
  _i4.Future<void> setRemoteDescription(
          _i2.RTCSessionDescription? description) =>
      (super.noSuchMethod(
        Invocation.method(
          #setRemoteDescription,
          [description],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> addIceCandidate(_i2.RTCIceCandidate? candidate) =>
      (super.noSuchMethod(
        Invocation.method(
          #addIceCandidate,
          [candidate],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> toggleVideo() => (super.noSuchMethod(
        Invocation.method(
          #toggleVideo,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> toggleAudio() => (super.noSuchMethod(
        Invocation.method(
          #toggleAudio,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> switchCamera() => (super.noSuchMethod(
        Invocation.method(
          #switchCamera,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}

/// A class which mocks [WebSocketService].
///
/// See the documentation for Mockito's code generation for more information.
class MockWebSocketService extends _i1.Mock implements _i5.WebSocketService {
  MockWebSocketService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i5.WebSocketState> get stateStream => (super.noSuchMethod(
        Invocation.getter(#stateStream),
        returnValue: _i4.Stream<_i5.WebSocketState>.empty(),
      ) as _i4.Stream<_i5.WebSocketState>);

  @override
  _i4.Stream<Map<String, dynamic>> get messageStream => (super.noSuchMethod(
        Invocation.getter(#messageStream),
        returnValue: _i4.Stream<Map<String, dynamic>>.empty(),
      ) as _i4.Stream<Map<String, dynamic>>);

  @override
  _i4.Stream<String> get errorStream => (super.noSuchMethod(
        Invocation.getter(#errorStream),
        returnValue: _i4.Stream<String>.empty(),
      ) as _i4.Stream<String>);

  @override
  bool get isConnected => (super.noSuchMethod(
        Invocation.getter(#isConnected),
        returnValue: false,
      ) as bool);

  @override
  _i5.WebSocketState get state => (super.noSuchMethod(
        Invocation.getter(#state),
        returnValue: _i5.WebSocketState.disconnected,
      ) as _i5.WebSocketState);

  @override
  _i4.Future<void> connect({
    required String? serverUrl,
    required String? authToken,
    required String? userId,
    Map<String, dynamic>? extraHeaders,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #connect,
          [],
          {
            #serverUrl: serverUrl,
            #authToken: authToken,
            #userId: userId,
            #extraHeaders: extraHeaders,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> joinCall(String? callId) => (super.noSuchMethod(
        Invocation.method(
          #joinCall,
          [callId],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> sendOffer({
    required String? callId,
    required String? vendorId,
    required _i2.RTCSessionDescription? offer,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendOffer,
          [],
          {
            #callId: callId,
            #vendorId: vendorId,
            #offer: offer,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> sendAnswer({
    required String? callId,
    required String? vendorId,
    required _i2.RTCSessionDescription? answer,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendAnswer,
          [],
          {
            #callId: callId,
            #vendorId: vendorId,
            #answer: answer,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> sendIceCandidate({
    required String? callId,
    required String? vendorId,
    required _i2.RTCIceCandidate? candidate,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendIceCandidate,
          [],
          {
            #callId: callId,
            #vendorId: vendorId,
            #candidate: candidate,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> leaveCall(String? callId) => (super.noSuchMethod(
        Invocation.method(
          #leaveCall,
          [callId],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> updateConnectionState({
    required String? callId,
    required String? state,
    String? vendorId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateConnectionState,
          [],
          {
            #callId: callId,
            #state: state,
            #vendorId: vendorId,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> disconnect() => (super.noSuchMethod(
        Invocation.method(
          #disconnect,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [ApiService].
///
/// See the documentation for Mockito's code generation for more information.
class MockApiService extends _i1.Mock implements _i6.ApiService {
  MockApiService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isAuthenticated => (super.noSuchMethod(
        Invocation.getter(#isAuthenticated),
        returnValue: false,
      ) as bool);

  @override
  void initialize({
    required String? baseUrl,
    Duration? connectTimeout,
    Duration? receiveTimeout,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
          {
            #baseUrl: baseUrl,
            #connectTimeout: connectTimeout,
            #receiveTimeout: receiveTimeout,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<void> setAuthToken(String? token) => (super.noSuchMethod(
        Invocation.method(
          #setAuthToken,
          [token],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<Map<String, dynamic>> initiateCall({
    required String? vendorId,
    String? callType = 'video',
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #initiateCall,
          [],
          {
            #vendorId: vendorId,
            #callType: callType,
          },
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<String, dynamic>> acceptCall(String? callId) =>
      (super.noSuchMethod(
        Invocation.method(
          #acceptCall,
          [callId],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<String, dynamic>> rejectCall(String? callId) =>
      (super.noSuchMethod(
        Invocation.method(
          #rejectCall,
          [callId],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<String, dynamic>> endCall(String? callId) =>
      (super.noSuchMethod(
        Invocation.method(
          #endCall,
          [callId],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<String, dynamic>> getCallDetails(String? callId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCallDetails,
          [callId],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<List<Map<String, dynamic>>> getCallHistory({
    int? page = 1,
    int? perPage = 20,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCallHistory,
          [],
          {
            #page: page,
            #perPage: perPage,
          },
        ),
        returnValue: _i4.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i4.Future<List<Map<String, dynamic>>>);

  @override
  _i4.Future<List<Map<String, dynamic>>> getVendors({
    String? search,
    int? page = 1,
    int? perPage = 20,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getVendors,
          [],
          {
            #search: search,
            #page: page,
            #perPage: perPage,
          },
        ),
        returnValue: _i4.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i4.Future<List<Map<String, dynamic>>>);
}

/// A class which mocks [MediaStream].
///
/// See the documentation for Mockito's code generation for more information.
class MockMediaStream extends _i1.Mock implements _i2.MediaStream {
  MockMediaStream() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set onAddTrack(dynamic Function(_i2.MediaStreamTrack)? _onAddTrack) =>
      super.noSuchMethod(
        Invocation.setter(
          #onAddTrack,
          _onAddTrack,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onRemoveTrack(dynamic Function(_i2.MediaStreamTrack)? _onRemoveTrack) =>
      super.noSuchMethod(
        Invocation.setter(
          #onRemoveTrack,
          _onRemoveTrack,
        ),
        returnValueForMissingStub: null,
      );

  @override
  String get id => (super.noSuchMethod(
        Invocation.getter(#id),
        returnValue: _i7.dummyValue<String>(
          this,
          Invocation.getter(#id),
        ),
      ) as String);

  @override
  String get ownerTag => (super.noSuchMethod(
        Invocation.getter(#ownerTag),
        returnValue: _i7.dummyValue<String>(
          this,
          Invocation.getter(#ownerTag),
        ),
      ) as String);

  @override
  _i4.Future<void> getMediaTracks() => (super.noSuchMethod(
        Invocation.method(
          #getMediaTracks,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> addTrack(
    _i2.MediaStreamTrack? track, {
    bool? addToNative = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addTrack,
          [track],
          {#addToNative: addToNative},
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> removeTrack(
    _i2.MediaStreamTrack? track, {
    bool? removeFromNative = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeTrack,
          [track],
          {#removeFromNative: removeFromNative},
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  List<_i2.MediaStreamTrack> getTracks() => (super.noSuchMethod(
        Invocation.method(
          #getTracks,
          [],
        ),
        returnValue: <_i2.MediaStreamTrack>[],
      ) as List<_i2.MediaStreamTrack>);

  @override
  List<_i2.MediaStreamTrack> getAudioTracks() => (super.noSuchMethod(
        Invocation.method(
          #getAudioTracks,
          [],
        ),
        returnValue: <_i2.MediaStreamTrack>[],
      ) as List<_i2.MediaStreamTrack>);

  @override
  List<_i2.MediaStreamTrack> getVideoTracks() => (super.noSuchMethod(
        Invocation.method(
          #getVideoTracks,
          [],
        ),
        returnValue: <_i2.MediaStreamTrack>[],
      ) as List<_i2.MediaStreamTrack>);

  @override
  _i2.MediaStreamTrack? getTrackById(String? trackId) =>
      (super.noSuchMethod(Invocation.method(
        #getTrackById,
        [trackId],
      )) as _i2.MediaStreamTrack?);

  @override
  _i4.Future<_i2.MediaStream> clone() => (super.noSuchMethod(
        Invocation.method(
          #clone,
          [],
        ),
        returnValue: _i4.Future<_i2.MediaStream>.value(_FakeMediaStream_0(
          this,
          Invocation.method(
            #clone,
            [],
          ),
        )),
      ) as _i4.Future<_i2.MediaStream>);

  @override
  _i4.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}

/// A class which mocks [RTCSessionDescription].
///
/// See the documentation for Mockito's code generation for more information.
class MockRTCSessionDescription extends _i1.Mock
    implements _i2.RTCSessionDescription {
  MockRTCSessionDescription() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set sdp(String? _sdp) => super.noSuchMethod(
        Invocation.setter(
          #sdp,
          _sdp,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set type(String? _type) => super.noSuchMethod(
        Invocation.setter(
          #type,
          _type,
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [RTCIceCandidate].
///
/// See the documentation for Mockito's code generation for more information.
class MockRTCIceCandidate extends _i1.Mock implements _i2.RTCIceCandidate {
  MockRTCIceCandidate() {
    _i1.throwOnMissingStub(this);
  }
}
