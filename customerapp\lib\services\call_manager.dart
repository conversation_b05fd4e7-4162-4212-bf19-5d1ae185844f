import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:logger/logger.dart';
import 'dart:async';

import 'webrtc_service.dart';
import 'websocket_service.dart';
import 'api_service.dart';

enum CallState {
  idle,
  initiating,
  ringing,
  connecting,
  connected,
  ended,
  error,
}

enum CallType {
  outgoing,
  incoming,
}

class CallManager {
  static final CallManager _instance = CallManager._internal();
  factory CallManager() => _instance;
  CallManager._internal();

  final Logger _logger = Logger();
  final WebRTCService _webrtcService = WebRTCService();
  final WebSocketService _webSocketService = WebSocketService();
  final ApiService _apiService = ApiService();

  CallState _callState = CallState.idle;
  CallType? _callType;
  String? _currentCallId;
  String? _vendorId;
  String? _customerId;

  // Callbacks
  Function(CallState state)? onCallStateChanged;
  Function(MediaStream stream)? onLocalStream;
  Function(MediaStream stream)? onRemoteStream;
  Function(String error)? onError;
  Function(String callId, String vendorId, Map<String, dynamic> vendorInfo)? onIncomingCall;

  /// Initialize call manager
  Future<void> initialize({
    required String signalingServerUrl,
    required String apiBaseUrl,
    required String authToken,
    required String userId,
  }) async {
    try {
      _customerId = userId;
      
      // Initialize services
      _apiService.initialize(baseUrl: apiBaseUrl);
      await _apiService.setAuthToken(authToken);
      
      await _webrtcService.initialize();
      await _webSocketService.connect(
        serverUrl: signalingServerUrl,
        authToken: authToken,
        userId: userId,
      );

      _setupWebRTCCallbacks();
      _setupSignalingCallbacks();

      _logger.i('Customer call manager initialized');
    } catch (e) {
      _logger.e('Failed to initialize customer call manager: $e');
      _updateCallState(CallState.error);
      onError?.call('Failed to initialize: $e');
      rethrow;
    }
  }

  /// Set up WebRTC service callbacks
  void _setupWebRTCCallbacks() {
    _webrtcService.onLocalStream = (stream) {
      _logger.i('Customer local stream received');
      onLocalStream?.call(stream);
    };

    _webrtcService.onRemoteStream = (stream) {
      _logger.i('Vendor stream received');
      onRemoteStream?.call(stream);
      _updateCallState(CallState.connected);
    };

    _webrtcService.onConnectionStateChange = (state) {
      _logger.i('Customer WebRTC connection state: $state');
      
      switch (state) {
        case RTCPeerConnectionState.RTCPeerConnectionStateConnecting:
          _updateCallState(CallState.connecting);
          break;
        case RTCPeerConnectionState.RTCPeerConnectionStateConnected:
          _updateCallState(CallState.connected);
          break;
        case RTCPeerConnectionState.RTCPeerConnectionStateFailed:
        case RTCPeerConnectionState.RTCPeerConnectionStateDisconnected:
          _updateCallState(CallState.error);
          onError?.call('Connection with vendor failed');
          break;
        case RTCPeerConnectionState.RTCPeerConnectionStateClosed:
          _updateCallState(CallState.ended);
          break;
        default:
          break;
      }
    };

    _webrtcService.onIceCandidate = (candidate) {
      if (_currentCallId != null && _vendorId != null) {
        _webSocketService.sendIceCandidate(
          callId: _currentCallId!,
          vendorId: _vendorId!,
          candidate: candidate,
        );
      }
    };

    _webrtcService.onError = (error) {
      _logger.e('Customer WebRTC error: $error');
      _updateCallState(CallState.error);
      onError?.call(error);
    };
  }

  /// Set up signaling service callbacks
  void _setupSignalingCallbacks() {
    _webSocketService.messageStream.listen((message) {
      _handleSignalingMessage(message);
    });

    _webSocketService.errorStream.listen((error) {
      _logger.e('Customer signaling error: $error');
      onError?.call('Signaling error: $error');
    });
  }

  /// Handle incoming signaling messages
  void _handleSignalingMessage(Map<String, dynamic> message) async {
    final type = message['type'] as String;
    final data = message['data'] as Map<String, dynamic>;

    switch (type) {
      case 'webrtc-offer-received':
        await _handleIncomingOffer(data);
        break;
      case 'webrtc-answer-received':
        await _handleIncomingAnswer(data);
        break;
      case 'ice-candidate-received':
        await _handleIncomingIceCandidate(data);
        break;
      case 'user-joined':
        _handleUserJoined(data);
        break;
      case 'user-left':
        _handleUserLeft(data);
        break;
      case 'incoming-call':
        await _handleIncomingCallNotification(data);
        break;
    }
  }

  /// Start an outgoing call to vendor
  Future<void> startCall(String vendorId) async {
    try {
      if (_callState != CallState.idle) {
        throw Exception('Already in a call');
      }

      _updateCallState(CallState.initiating);
      _callType = CallType.outgoing;
      _vendorId = vendorId;

      // Step 1: Initiate call via Laravel API to get call_id
      final callData = await _apiService.initiateCall(vendorId: vendorId);
      _currentCallId = callData['call_id'];

      _logger.i('Customer initiated call with vendor: $vendorId, call_id: $_currentCallId');

      // Step 2: Join signaling room
      await _webSocketService.joinCall(_currentCallId!);

      // Step 3: Get user media
      await _webrtcService.getUserMedia();

      // Step 4: Create and send WebRTC offer via WebSocket
      final offer = await _webrtcService.createOffer();
      await _webSocketService.sendOffer(
        callId: _currentCallId!,
        vendorId: vendorId,
        offer: offer,
      );

      _updateCallState(CallState.connecting);
      _logger.i('Customer sent offer to vendor via WebSocket');

    } catch (e) {
      _logger.e('Failed to start customer call: $e');
      _updateCallState(CallState.error);
      onError?.call('Failed to start call: $e');
      await _cleanup();
      rethrow;
    }
  }

  /// Handle incoming call notification
  Future<void> _handleIncomingCallNotification(Map<String, dynamic> data) async {
    try {
      final callId = data['call_id'] as String;
      final fromVendorId = data['from_user_id'] as String;
      final vendorInfo = data['vendor_info'] as Map<String, dynamic>? ?? {};

      if (_callState == CallState.idle) {
        _currentCallId = callId;
        _vendorId = fromVendorId;
        _callType = CallType.incoming;
        _updateCallState(CallState.ringing);

        _logger.i('Customer received incoming call from vendor: $fromVendorId');
        onIncomingCall?.call(callId, fromVendorId, vendorInfo);
      }
    } catch (e) {
      _logger.e('Failed to handle incoming call notification: $e');
    }
  }

  /// Handle incoming WebRTC offer
  Future<void> _handleIncomingOffer(Map<String, dynamic> data) async {
    try {
      final callId = data['call_id'] as String;
      final fromVendorId = data['from_user_id'] as String;
      final offerData = data['offer'] as Map<String, dynamic>;
      
      final offer = RTCSessionDescription(
        offerData['sdp'] as String,
        offerData['type'] as String,
      );
      
      _logger.i('Customer received WebRTC offer from vendor: $fromVendorId');

      // Get user media
      await _webrtcService.getUserMedia();

      // Set remote description
      await _webrtcService.setRemoteDescription(offer);

      // Create and send answer
      final answer = await _webrtcService.createAnswer();
      await _webSocketService.sendAnswer(
        callId: callId,
        vendorId: fromVendorId,
        answer: answer,
      );

      _updateCallState(CallState.connecting);

    } catch (e) {
      _logger.e('Failed to handle vendor offer: $e');
      _updateCallState(CallState.error);
      onError?.call('Failed to answer call: $e');
      await _cleanup();
    }
  }

  /// Handle incoming WebRTC answer
  Future<void> _handleIncomingAnswer(Map<String, dynamic> data) async {
    try {
      final answerData = data['answer'] as Map<String, dynamic>;
      
      final answer = RTCSessionDescription(
        answerData['sdp'] as String,
        answerData['type'] as String,
      );
      
      _logger.i('Customer received WebRTC answer from vendor');
      await _webrtcService.setRemoteDescription(answer);

    } catch (e) {
      _logger.e('Failed to handle vendor answer: $e');
      onError?.call('Failed to process vendor response: $e');
    }
  }

  /// Handle incoming ICE candidate
  Future<void> _handleIncomingIceCandidate(Map<String, dynamic> data) async {
    try {
      final candidateData = data['candidate'] as Map<String, dynamic>;
      
      final candidate = RTCIceCandidate(
        candidateData['candidate'] as String,
        candidateData['sdpMid'] as String?,
        candidateData['sdpMLineIndex'] as int?,
      );
      
      _logger.d('Customer received ICE candidate from vendor');
      await _webrtcService.addIceCandidate(candidate);

    } catch (e) {
      _logger.e('Failed to handle ICE candidate: $e');
      // Don't show error for ICE candidate failures
    }
  }

  /// Handle user joined
  void _handleUserJoined(Map<String, dynamic> data) {
    final userId = data['user_id'] as String;
    _logger.i('Vendor joined call: $userId');
  }

  /// Handle user left
  void _handleUserLeft(Map<String, dynamic> data) {
    final userId = data['user_id'] as String;
    if (userId == _vendorId) {
      _logger.i('Vendor left call: $userId');
      _updateCallState(CallState.ended);
      _cleanup();
    }
  }

  /// Accept an incoming call
  Future<void> acceptCall() async {
    try {
      if (_callState != CallState.ringing || _callType != CallType.incoming) {
        throw Exception('No incoming call to accept');
      }

      // Accept call via Laravel API
      await _apiService.acceptCall(_currentCallId!);

      // Join signaling room
      await _webSocketService.joinCall(_currentCallId!);

      _logger.i('Customer accepted call: $_currentCallId');

    } catch (e) {
      _logger.e('Failed to accept call: $e');
      onError?.call('Failed to accept call: $e');
      await _cleanup();
    }
  }

  /// Reject an incoming call
  Future<void> rejectCall() async {
    try {
      if (_callState != CallState.ringing || _callType != CallType.incoming) {
        throw Exception('No incoming call to reject');
      }

      // Reject call via Laravel API
      await _apiService.rejectCall(_currentCallId!);

      _updateCallState(CallState.ended);
      await _cleanup();

      _logger.i('Customer rejected call: $_currentCallId');

    } catch (e) {
      _logger.e('Failed to reject call: $e');
      onError?.call('Failed to reject call: $e');
      await _cleanup();
    }
  }

  /// End the current call
  Future<void> endCall() async {
    try {
      if (_callState == CallState.idle || _callState == CallState.ended) {
        return;
      }

      // End call via Laravel API
      if (_currentCallId != null) {
        await _apiService.endCall(_currentCallId!);
      }

      // Send leave message via WebSocket
      if (_currentCallId != null) {
        await _webSocketService.leaveCall(_currentCallId!);
      }

      _updateCallState(CallState.ended);
      await _cleanup();

      _logger.i('Customer ended call: $_currentCallId');

    } catch (e) {
      _logger.e('Failed to end call: $e');
      onError?.call('Failed to end call: $e');
      await _cleanup();
    }
  }

  /// Toggle video
  Future<bool> toggleVideo() async {
    return await _webrtcService.toggleVideo();
  }

  /// Toggle audio
  Future<bool> toggleAudio() async {
    return await _webrtcService.toggleAudio();
  }

  /// Switch camera
  Future<void> switchCamera() async {
    await _webrtcService.switchCamera();
  }

  /// Clean up resources
  Future<void> _cleanup() async {
    try {
      await _webrtcService.dispose();
      
      _currentCallId = null;
      _vendorId = null;
      _callType = null;
      
      _logger.i('Customer call resources cleaned up');
    } catch (e) {
      _logger.e('Error during customer cleanup: $e');
    }
  }

  /// Update call state and notify listeners
  void _updateCallState(CallState newState) {
    if (_callState != newState) {
      _callState = newState;
      onCallStateChanged?.call(_callState);
      _logger.i('Customer call state changed to: $newState');
    }
  }

  /// Dispose call manager
  Future<void> dispose() async {
    await _cleanup();
    await _webSocketService.disconnect();
    _updateCallState(CallState.idle);
  }

  // Getters
  CallState get callState => _callState;
  CallType? get callType => _callType;
  String? get currentCallId => _currentCallId;
  String? get vendorId => _vendorId;
  String? get customerId => _customerId;
  bool get isInCall => _callState != CallState.idle && _callState != CallState.ended;
  bool get isVideoEnabled => _webrtcService.isVideoEnabled;
  bool get isAudioEnabled => _webrtcService.isAudioEnabled;
  MediaStream? get localStream => _webrtcService.localStream;
  MediaStream? get remoteStream => _webrtcService.remoteStream;
}
