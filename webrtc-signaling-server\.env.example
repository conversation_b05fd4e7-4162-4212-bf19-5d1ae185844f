# WebRTC Signaling Server Configuration

# Server Configuration
NODE_ENV=development
PORT=3001

# Logging Configuration
LOG_LEVEL=debug
ENABLE_FILE_LOGGING=false
LOG_DIR=./logs

# Security Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
JWT_SECRET=your-jwt-secret-key-here

# Laravel Integration
LARAVEL_API_URL=http://localhost:8000/api
LARAVEL_API_TIMEOUT=5000

# Authentication Configuration
# For development only - allows simple user ID tokens
ALLOW_DEV_TOKENS=true

# Rate Limiting
MAX_CONNECTIONS_PER_IP=10
MAX_REQUESTS_PER_MINUTE=100
MAX_MESSAGE_SIZE=100000

# Call Management
MAX_CALL_DURATION=3600000
CALL_CLEANUP_INTERVAL=300000
MAX_PARTICIPANTS_PER_CALL=10

# Monitoring and Metrics
ENABLE_METRICS=true
METRICS_INTERVAL=30000

# Socket.IO Configuration
SOCKET_PING_TIMEOUT=60000
SOCKET_PING_INTERVAL=25000

# Redis Configuration (optional, for scaling)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
ENABLE_HEALTH_ENDPOINT=true

# Development Configuration
DEBUG_MODE=false
ENABLE_CORS=true
TRUST_PROXY=false

# Production Configuration
CLUSTER_MODE=false
WORKER_PROCESSES=auto

# SSL Configuration (for production)
SSL_ENABLED=false
SSL_CERT_PATH=
SSL_KEY_PATH=

# External Services
STUN_SERVERS=stun:stun.l.google.com:19302,stun:stun1.l.google.com:19302
TURN_SERVERS=

# Notification Configuration
ENABLE_WEBHOOKS=false
WEBHOOK_URL=
WEBHOOK_SECRET=

# Database Configuration (if needed for persistence)
DB_ENABLED=false
DB_HOST=localhost
DB_PORT=5432
DB_NAME=webrtc_signaling
DB_USER=postgres
DB_PASSWORD=

# Cache Configuration
CACHE_ENABLED=true
CACHE_TTL=300000
CACHE_MAX_SIZE=1000

# Error Handling
ENABLE_ERROR_REPORTING=false
ERROR_REPORTING_URL=
SENTRY_DSN=

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=false
PERFORMANCE_SAMPLE_RATE=0.1

# Feature Flags
ENABLE_CALL_RECORDING=false
ENABLE_CALL_ANALYTICS=true
ENABLE_USER_PRESENCE=true
ENABLE_TYPING_INDICATORS=false

# API Keys (if needed)
API_KEY=
API_SECRET=

# Backup and Recovery
ENABLE_BACKUP=false
BACKUP_INTERVAL=86400000
BACKUP_RETENTION_DAYS=7
