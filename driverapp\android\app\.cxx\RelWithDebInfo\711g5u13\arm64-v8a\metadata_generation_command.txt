                        -H/Users/<USER>/fvm/versions/3.29.3/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.0.12077973/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/Dev/mobile-apps/Fuodz-delivery-boy/build/app/intermediates/cxx/RelWithDebInfo/711g5u13/obj/arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/Dev/mobile-apps/Fuodz-delivery-boy/build/app/intermediates/cxx/RelWithDebInfo/711g5u13/obj/arm64-v8a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-B/Users/<USER>/Desktop/Dev/mobile-apps/Fuodz-delivery-boy/android/app/.cxx/RelWithDebInfo/711g5u13/arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2