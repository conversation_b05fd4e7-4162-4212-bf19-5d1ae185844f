# WebRTC Signaling Architecture for Laravel + Flutter

This document defines the WebRTC signaling architecture options and the role of <PERSON><PERSON> in WebRTC communication.

## Architecture Overview

### Current Infrastructure
- **Laravel Backend**: Call management, authentication, FCM notifications
- **<PERSON><PERSON> Reverb**: WebSocket server for real-time events
- **Flutter Apps**: Direct WebRTC peer-to-peer communication
- **Database**: Persistent call records and metadata

## Signaling Architecture Options

### Option 1: <PERSON>vel as Temporary Signaling Store (RECOMMENDED)

```
Flutter A ←→ Laravel API ←→ Laravel Reverb ←→ Flutter B
    ↓                                           ↑
    └─────── Direct WebRTC P2P Connection ─────┘
```

**<PERSON><PERSON>'s Role:**
- ✅ Store initial offer/answer temporarily
- ✅ Manage call state and metadata
- ✅ Provide signaling data retrieval endpoints
- ✅ Handle ICE candidate exchange via WebSocket
- ✅ Clean up signaling data after connection

**Benefits:**
- Persistence for debugging and reliability
- Fallback mechanism if WebSocket fails
- Better error handling and logging
- Gradual migration capability

### Option 2: Direct WebSocket Signaling (ALTERNATIVE)

```
Flutter A ←→ Laravel Reverb ←→ Flutter B
    ↓                           ↑
    └── Direct WebRTC P2P ──────┘
```

**<PERSON><PERSON>'s Role:**
- ❌ No signaling data storage
- ✅ Call state management only
- ✅ Authentication and authorization
- ✅ FCM notifications

**Benefits:**
- Lower latency
- Reduced server storage
- Simpler architecture
- Real-time only approach

## Implementation Details

### Approach 1: Laravel Temporary Signaling Store

#### Database Schema Enhancement

The existing `video_calls` table already supports this with the `webrtc_data` JSON field:

```sql
-- Existing schema supports signaling data
webrtc_data JSON NULL -- Stores offers, answers, ICE candidates
```

#### Signaling Data Structure

```json
{
  "offer": {
    "type": "offer",
    "sdp": "v=0\r\no=...",
    "timestamp": "2024-01-15T10:30:00Z"
  },
  "answer": {
    "type": "answer", 
    "sdp": "v=0\r\no=...",
    "timestamp": "2024-01-15T10:30:30Z"
  },
  "ice_candidates": [
    {
      "candidate": "candidate:1 1 UDP 2130706431 ************* 54400 typ host",
      "sdpMid": "0",
      "sdpMLineIndex": 0,
      "timestamp": "2024-01-15T10:30:05Z"
    }
  ],
  "connection_state": "connecting",
  "last_updated": "2024-01-15T10:30:35Z"
}
```

#### Laravel API Endpoints for Signaling

**Store Offer/Answer:**
- `POST /api/calls/{call_id}/signaling/offer`
- `POST /api/calls/{call_id}/signaling/answer`

**ICE Candidate Management:**
- `POST /api/calls/{call_id}/signaling/ice-candidate`
- `GET /api/calls/{call_id}/signaling/ice-candidates`

**Retrieve Signaling Data:**
- `GET /api/calls/{call_id}/signaling`

#### WebSocket Events for Real-time

**Via Laravel Reverb:**
- `webrtc.offer.received`
- `webrtc.answer.received`
- `webrtc.ice-candidate.received`
- `webrtc.connection.state.changed`

### Approach 2: Direct WebSocket Signaling

#### WebSocket Events Only

**No Laravel API endpoints needed for signaling data**

**Direct WebSocket Events:**
- `call.{callId}.webrtc.offer`
- `call.{callId}.webrtc.answer`
- `call.{callId}.webrtc.ice-candidate`

**Laravel's Limited Role:**
- Call initiation triggers WebSocket room creation
- No signaling data persistence
- Real-time events only

## Recommended Implementation: Hybrid Approach

### Phase 1: Laravel Temporary Storage (Immediate)
1. Implement Laravel signaling endpoints
2. Store offers/answers temporarily
3. Use WebSocket for ICE candidates
4. Provide fallback mechanisms

### Phase 2: Migration to Direct WebSocket (Future)
1. Monitor connection success rates
2. Gradually reduce Laravel storage dependency
3. Move to pure WebSocket signaling
4. Keep Laravel for call management only

## Data Flow Examples

### Scenario 1: Successful Call with Laravel Storage

```
1. Caller initiates call via Laravel API
2. Laravel stores call record + FCM notification
3. Receiver gets notification, accepts call
4. Caller posts offer to Laravel API
5. Laravel stores offer + broadcasts via WebSocket
6. Receiver retrieves offer from Laravel API
7. Receiver posts answer to Laravel API
8. Laravel stores answer + broadcasts via WebSocket
9. ICE candidates exchanged via WebSocket only
10. Direct P2P connection established
11. Laravel cleans up signaling data after 5 minutes
```

### Scenario 2: WebSocket Failure Fallback

```
1. WebSocket connection fails during signaling
2. Flutter falls back to Laravel API polling
3. Retrieves latest signaling data via HTTP
4. Continues with connection establishment
5. Reports connection state back to Laravel
```

### Scenario 3: Pure WebSocket Signaling

```
1. Caller initiates call via Laravel API
2. Laravel creates WebSocket room + FCM notification
3. All signaling happens via WebSocket events
4. No persistence in Laravel database
5. Connection state reported to Laravel for call management
```

## Security Considerations

### Authentication
- All signaling endpoints require authentication
- WebSocket channels use call-specific authorization
- Signaling data access limited to call participants

### Data Privacy
- Signaling data automatically expires
- No permanent storage of SDP or ICE candidates
- Encrypted WebSocket connections only

### Rate Limiting
- ICE candidate submission rate limits
- Signaling data retrieval limits
- WebSocket connection limits per user

## Performance Considerations

### Laravel Storage Approach
- **Pros**: Reliability, debugging, fallback
- **Cons**: Database overhead, cleanup required

### Direct WebSocket Approach  
- **Pros**: Lower latency, no storage overhead
- **Cons**: No fallback, harder debugging

### Hybrid Approach
- **Pros**: Best of both worlds, gradual migration
- **Cons**: More complex implementation

## Configuration Options

### Environment Variables

```env
# WebRTC Signaling Configuration
WEBRTC_SIGNALING_MODE=hybrid  # hybrid|laravel|websocket
WEBRTC_SIGNALING_TTL=300      # 5 minutes
WEBRTC_ICE_CANDIDATE_LIMIT=50 # Per call
WEBRTC_CLEANUP_INTERVAL=60    # Cleanup every minute
```

### Feature Flags

```php
// config/webrtc.php
'signaling' => [
    'store_offers_answers' => env('WEBRTC_STORE_OFFERS', true),
    'store_ice_candidates' => env('WEBRTC_STORE_ICE', false),
    'websocket_fallback' => env('WEBRTC_WS_FALLBACK', true),
    'auto_cleanup' => env('WEBRTC_AUTO_CLEANUP', true),
]
```

## Monitoring and Debugging

### Metrics to Track
- Signaling success rates
- Connection establishment time
- WebSocket vs API usage
- Cleanup efficiency

### Logging
- All signaling events logged
- Connection state changes tracked
- Performance metrics collected
- Error rates monitored

## Migration Strategy

### Current State → Laravel Storage
1. Implement signaling endpoints
2. Update Flutter apps to use hybrid approach
3. Monitor connection success rates

### Laravel Storage → Direct WebSocket
1. Analyze connection patterns
2. Implement pure WebSocket signaling
3. A/B test both approaches
4. Gradually migrate users
5. Remove Laravel signaling endpoints

## Conclusion

**Recommended Approach: Start with Laravel Temporary Storage**

This provides:
- ✅ Immediate implementation with existing infrastructure
- ✅ Reliability and debugging capabilities
- ✅ Fallback mechanisms for poor network conditions
- ✅ Gradual migration path to pure WebSocket
- ✅ Better monitoring and analytics

**Future Migration: Move to Direct WebSocket**

Once connection patterns are understood and reliability is proven, migrate to direct WebSocket signaling for optimal performance.

The hybrid approach allows you to start immediately while keeping options open for future optimization.
