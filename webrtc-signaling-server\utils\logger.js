const winston = require('winston');
const path = require('path');

/**
 * Winston logger configuration for the WebRTC signaling server
 */

// Define log levels
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4
};

// Define log colors
const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'blue'
};

// Add colors to winston
winston.addColors(logColors);

// Create custom format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let logMessage = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      logMessage += ` ${JSON.stringify(meta)}`;
    }
    
    return logMessage;
  })
);

// Create console format for development
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'HH:mm:ss' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let logMessage = `${timestamp} ${level}: ${message}`;
    
    // Add metadata if present (formatted for readability)
    if (Object.keys(meta).length > 0) {
      const metaString = JSON.stringify(meta, null, 2);
      logMessage += `\n${metaString}`;
    }
    
    return logMessage;
  })
);

// Determine log level based on environment
const getLogLevel = () => {
  const env = process.env.NODE_ENV || 'development';
  const logLevel = process.env.LOG_LEVEL;
  
  if (logLevel) {
    return logLevel;
  }
  
  switch (env) {
    case 'production':
      return 'info';
    case 'test':
      return 'warn';
    default:
      return 'debug';
  }
};

// Create transports array
const transports = [];

// Console transport (always enabled)
transports.push(
  new winston.transports.Console({
    level: getLogLevel(),
    format: process.env.NODE_ENV === 'production' ? logFormat : consoleFormat,
    handleExceptions: true,
    handleRejections: true
  })
);

// File transports (only in production or when explicitly enabled)
if (process.env.NODE_ENV === 'production' || process.env.ENABLE_FILE_LOGGING === 'true') {
  const logDir = process.env.LOG_DIR || path.join(__dirname, '../logs');
  
  // Ensure log directory exists
  const fs = require('fs');
  if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
  }

  // Error log file
  transports.push(
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      format: logFormat,
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      handleExceptions: true,
      handleRejections: true
    })
  );

  // Combined log file
  transports.push(
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      level: getLogLevel(),
      format: logFormat,
      maxsize: 10485760, // 10MB
      maxFiles: 10
    })
  );

  // Access log file for HTTP requests
  transports.push(
    new winston.transports.File({
      filename: path.join(logDir, 'access.log'),
      level: 'http',
      format: logFormat,
      maxsize: 10485760, // 10MB
      maxFiles: 5
    })
  );
}

// Create the logger
const logger = winston.createLogger({
  levels: logLevels,
  level: getLogLevel(),
  format: logFormat,
  transports: transports,
  exitOnError: false
});

// Add custom methods for specific log types
logger.signaling = (message, meta = {}) => {
  logger.debug(`[SIGNALING] ${message}`, { ...meta, category: 'signaling' });
};

logger.connection = (message, meta = {}) => {
  logger.info(`[CONNECTION] ${message}`, { ...meta, category: 'connection' });
};

logger.call = (message, meta = {}) => {
  logger.info(`[CALL] ${message}`, { ...meta, category: 'call' });
};

logger.auth = (message, meta = {}) => {
  logger.info(`[AUTH] ${message}`, { ...meta, category: 'auth' });
};

logger.metrics = (message, meta = {}) => {
  logger.debug(`[METRICS] ${message}`, { ...meta, category: 'metrics' });
};

logger.security = (message, meta = {}) => {
  logger.warn(`[SECURITY] ${message}`, { ...meta, category: 'security' });
};

// Add performance logging
logger.performance = (operation, duration, meta = {}) => {
  logger.debug(`[PERFORMANCE] ${operation} completed in ${duration}ms`, {
    ...meta,
    category: 'performance',
    operation: operation,
    duration: duration
  });
};

// Add request logging middleware for Express
logger.requestMiddleware = () => {
  return (req, res, next) => {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      const logData = {
        method: req.method,
        url: req.url,
        status: res.statusCode,
        duration: duration,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent')
      };
      
      if (res.statusCode >= 400) {
        logger.warn('HTTP request completed with error', logData);
      } else {
        logger.http('HTTP request completed', logData);
      }
    });
    
    next();
  };
};

// Add socket logging helpers
logger.socketConnect = (socketId, userId, meta = {}) => {
  logger.connection('Socket connected', {
    socketId: socketId,
    userId: userId,
    ...meta
  });
};

logger.socketDisconnect = (socketId, userId, reason, meta = {}) => {
  logger.connection('Socket disconnected', {
    socketId: socketId,
    userId: userId,
    reason: reason,
    ...meta
  });
};

logger.socketError = (socketId, userId, error, meta = {}) => {
  logger.error('Socket error', {
    socketId: socketId,
    userId: userId,
    error: error.message || error,
    stack: error.stack,
    ...meta
  });
};

// Add call logging helpers
logger.callStarted = (callId, participants, meta = {}) => {
  logger.call('Call started', {
    callId: callId,
    participants: participants,
    participantCount: participants.length,
    ...meta
  });
};

logger.callEnded = (callId, duration, reason, meta = {}) => {
  logger.call('Call ended', {
    callId: callId,
    duration: duration,
    reason: reason,
    ...meta
  });
};

logger.signalingMessage = (type, fromUser, toUser, callId, meta = {}) => {
  logger.signaling(`${type} message`, {
    messageType: type,
    fromUser: fromUser,
    toUser: toUser,
    callId: callId,
    ...meta
  });
};

// Handle uncaught exceptions and rejections
logger.exceptions.handle(
  new winston.transports.Console({
    format: consoleFormat
  })
);

if (process.env.NODE_ENV === 'production' || process.env.ENABLE_FILE_LOGGING === 'true') {
  const logDir = process.env.LOG_DIR || path.join(__dirname, '../logs');
  
  logger.exceptions.handle(
    new winston.transports.File({
      filename: path.join(logDir, 'exceptions.log'),
      format: logFormat
    })
  );
}

// Export logger configuration for testing
logger.getConfig = () => ({
  level: getLogLevel(),
  transports: transports.length,
  environment: process.env.NODE_ENV || 'development'
});

module.exports = logger;
