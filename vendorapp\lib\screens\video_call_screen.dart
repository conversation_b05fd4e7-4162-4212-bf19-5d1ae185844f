import 'package:flutter/material.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:logger/logger.dart';

import '../services/webrtc_service.dart';
import '../services/websocket_service.dart';
import '../services/permission_service.dart';

enum CallState {
  idle,
  initiating,
  ringing,
  connecting,
  connected,
  ended,
  error,
}

class VideoCallScreen extends StatefulWidget {
  final String? customerId;
  final String? callId;
  final bool isIncomingCall;
  final Map<String, dynamic>? customerInfo;

  const VideoCallScreen({
    Key? key,
    this.customerId,
    this.callId,
    this.isIncomingCall = false,
    this.customerInfo,
  }) : super(key: key);

  @override
  State<VideoCallScreen> createState() => _VideoCallScreenState();
}

class _VideoCallScreenState extends State<VideoCallScreen> {
  final Logger _logger = Logger();
  final WebRTCService _webrtcService = WebRTCService();
  final WebSocketService _webSocketService = WebSocketService();
  
  // Video renderers
  final RTCVideoRenderer _localRenderer = RTCVideoRenderer();
  final RTCVideoRenderer _remoteRenderer = RTCVideoRenderer();

  // Call state
  CallState _callState = CallState.idle;
  String? _currentCallId;
  String? _customerId;
  String? _errorMessage;
  
  // Media controls
  bool _isVideoEnabled = true;
  bool _isAudioEnabled = true;
  bool _isSpeakerEnabled = true;
  bool _useBackCamera = true; // Vendors typically want to show products

  @override
  void initState() {
    super.initState();
    _initializeCall();
  }

  @override
  void dispose() {
    _cleanup();
    super.dispose();
  }

  Future<void> _initializeCall() async {
    try {
      // Keep screen awake during call
      await WakelockPlus.enable();

      // Initialize video renderers
      await _localRenderer.initialize();
      await _remoteRenderer.initialize();

      // Check permissions
      if (!await PermissionService.checkEssentialPermissions()) {
        final granted = await PermissionService.handlePermissionRequest(context);
        if (!granted) {
          _setError('Camera and microphone permissions are required for video calls');
          return;
        }
      }

      // Initialize WebRTC service
      await _webrtcService.initialize();
      _setupWebRTCCallbacks();

      // Setup WebSocket listeners
      _setupWebSocketListeners();

      // Set initial state based on call type
      if (widget.isIncomingCall) {
        _currentCallId = widget.callId;
        _customerId = widget.customerId;
        _updateCallState(CallState.ringing);
      } else {
        _customerId = widget.customerId;
        _updateCallState(CallState.initiating);
        await _startOutgoingCall();
      }

    } catch (e) {
      _logger.e('Failed to initialize vendor call: $e');
      _setError('Failed to initialize call: $e');
    }
  }

  void _setupWebRTCCallbacks() {
    _webrtcService.onLocalStream = (stream) {
      setState(() {
        _localRenderer.srcObject = stream;
      });
      _logger.i('Vendor local stream received');
    };

    _webrtcService.onRemoteStream = (stream) {
      setState(() {
        _remoteRenderer.srcObject = stream;
      });
      _updateCallState(CallState.connected);
      _logger.i('Customer stream received');
    };

    _webrtcService.onConnectionStateChange = (state) {
      _logger.i('Vendor WebRTC connection state: $state');
      
      switch (state) {
        case RTCPeerConnectionState.RTCPeerConnectionStateConnecting:
          _updateCallState(CallState.connecting);
          break;
        case RTCPeerConnectionState.RTCPeerConnectionStateConnected:
          _updateCallState(CallState.connected);
          break;
        case RTCPeerConnectionState.RTCPeerConnectionStateFailed:
        case RTCPeerConnectionState.RTCPeerConnectionStateDisconnected:
          _setError('Connection with customer failed');
          break;
        case RTCPeerConnectionState.RTCPeerConnectionStateClosed:
          _updateCallState(CallState.ended);
          break;
        default:
          break;
      }
    };

    _webrtcService.onIceCandidate = (candidate) {
      if (_currentCallId != null && _customerId != null) {
        _webSocketService.sendIceCandidate(
          callId: _currentCallId!,
          customerId: _customerId!,
          candidate: candidate,
        );
      }
    };

    _webrtcService.onError = (error) {
      _logger.e('Vendor WebRTC error: $error');
      _setError(error);
    };
  }

  void _setupWebSocketListeners() {
    _webSocketService.messageStream.listen((message) {
      _handleWebSocketMessage(message);
    });

    _webSocketService.errorStream.listen((error) {
      _logger.e('Vendor WebSocket error: $error');
      _setError('Connection error: $error');
    });
  }

  void _handleWebSocketMessage(Map<String, dynamic> message) async {
    final type = message['type'] as String;
    final data = message['data'] as Map<String, dynamic>;

    switch (type) {
      case 'webrtc-offer-received':
        await _handleIncomingOffer(data);
        break;
      case 'webrtc-answer-received':
        await _handleIncomingAnswer(data);
        break;
      case 'ice-candidate-received':
        await _handleIncomingIceCandidate(data);
        break;
      case 'user-left':
        _handleCustomerLeft(data);
        break;
    }
  }

  Future<void> _startOutgoingCall() async {
    try {
      // Get user media with back camera for showing products
      await _webrtcService.getUserMedia(useBackCamera: _useBackCamera);

      // Create offer
      final offer = await _webrtcService.createOffer();

      // Send offer via WebSocket
      await _webSocketService.sendOffer(
        callId: _currentCallId ?? 'temp_call_id',
        customerId: _customerId!,
        offer: offer,
      );

      _updateCallState(CallState.connecting);
    } catch (e) {
      _logger.e('Failed to start vendor outgoing call: $e');
      _setError('Failed to start call with customer: $e');
    }
  }

  Future<void> _handleIncomingOffer(Map<String, dynamic> data) async {
    try {
      final offerData = data['offer'] as Map<String, dynamic>;
      final offer = RTCSessionDescription(
        offerData['sdp'] as String,
        offerData['type'] as String,
      );

      // Get user media with back camera for showing products
      await _webrtcService.getUserMedia(useBackCamera: _useBackCamera);

      // Set remote description
      await _webrtcService.setRemoteDescription(offer);

      // Create and send answer
      final answer = await _webrtcService.createAnswer();
      await _webSocketService.sendAnswer(
        callId: _currentCallId!,
        customerId: _customerId!,
        answer: answer,
      );

      _updateCallState(CallState.connecting);
    } catch (e) {
      _logger.e('Failed to handle customer offer: $e');
      _setError('Failed to answer customer call: $e');
    }
  }

  Future<void> _handleIncomingAnswer(Map<String, dynamic> data) async {
    try {
      final answerData = data['answer'] as Map<String, dynamic>;
      final answer = RTCSessionDescription(
        answerData['sdp'] as String,
        answerData['type'] as String,
      );

      await _webrtcService.setRemoteDescription(answer);
    } catch (e) {
      _logger.e('Failed to handle customer answer: $e');
      _setError('Failed to process customer response: $e');
    }
  }

  Future<void> _handleIncomingIceCandidate(Map<String, dynamic> data) async {
    try {
      final candidateData = data['candidate'] as Map<String, dynamic>;
      final candidate = RTCIceCandidate(
        candidateData['candidate'] as String,
        candidateData['sdpMid'] as String?,
        candidateData['sdpMLineIndex'] as int?,
      );

      await _webrtcService.addIceCandidate(candidate);
    } catch (e) {
      _logger.e('Failed to handle ICE candidate: $e');
      // Don't show error for ICE candidate failures
    }
  }

  void _handleCustomerLeft(Map<String, dynamic> data) {
    final userId = data['user_id'] as String;
    if (userId == _customerId) {
      _updateCallState(CallState.ended);
      _endCall();
    }
  }

  Future<void> _acceptCall() async {
    try {
      await _webSocketService.joinCall(_currentCallId!);
      // The offer handling will be triggered by WebSocket message
    } catch (e) {
      _logger.e('Failed to accept customer call: $e');
      _setError('Failed to accept call: $e');
    }
  }

  Future<void> _rejectCall() async {
    try {
      await _webSocketService.leaveCall(_currentCallId!);
      _updateCallState(CallState.ended);
      _endCall();
    } catch (e) {
      _logger.e('Failed to reject customer call: $e');
      _endCall();
    }
  }

  Future<void> _endCall() async {
    try {
      if (_currentCallId != null) {
        await _webSocketService.leaveCall(_currentCallId!);
      }
      _updateCallState(CallState.ended);
      
      // Navigate back after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted) {
          Navigator.of(context).pop();
        }
      });
    } catch (e) {
      _logger.e('Failed to end call: $e');
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  Future<void> _toggleVideo() async {
    final enabled = await _webrtcService.toggleVideo();
    setState(() {
      _isVideoEnabled = enabled;
    });
  }

  Future<void> _toggleAudio() async {
    final enabled = await _webrtcService.toggleAudio();
    setState(() {
      _isAudioEnabled = enabled;
    });
  }

  Future<void> _switchCamera() async {
    await _webrtcService.switchCamera();
    setState(() {
      _useBackCamera = !_useBackCamera;
    });
  }

  void _toggleSpeaker() {
    setState(() {
      _isSpeakerEnabled = !_isSpeakerEnabled;
    });
    // Implement speaker toggle logic
  }

  void _updateCallState(CallState newState) {
    if (_callState != newState) {
      setState(() {
        _callState = newState;
      });
      _logger.i('Vendor call state changed to: $newState');
    }
  }

  void _setError(String error) {
    setState(() {
      _errorMessage = error;
      _callState = CallState.error;
    });
  }

  Future<void> _cleanup() async {
    try {
      await WakelockPlus.disable();
      await _localRenderer.dispose();
      await _remoteRenderer.dispose();
      await _webrtcService.dispose();
    } catch (e) {
      _logger.e('Error during vendor call cleanup: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          widget.customerInfo?['name'] ?? 'Video Call',
          style: const TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: Icon(
              _useBackCamera ? Icons.camera_rear : Icons.camera_front,
              color: Colors.white,
            ),
            onPressed: _switchCamera,
            tooltip: 'Switch Camera',
          ),
        ],
      ),
      body: Stack(
        children: [
          // Customer video (full screen)
          _buildRemoteVideo(),
          
          // Vendor video (picture-in-picture) - showing products
          _buildLocalVideo(),
          
          // Call status
          _buildCallStatus(),
          
          // Call controls
          _buildCallControls(),
          
          // Incoming call controls
          if (_callState == CallState.ringing && widget.isIncomingCall)
            _buildIncomingCallControls(),
          
          // Error message
          if (_errorMessage != null) _buildErrorMessage(),
        ],
      ),
    );
  }

  Widget _buildRemoteVideo() {
    return Positioned.fill(
      child: _remoteRenderer.srcObject != null
          ? RTCVideoView(
              _remoteRenderer,
              mirror: false,
              objectFit: RTCVideoViewObjectFit.RTCVideoViewObjectFitCover,
            )
          : Container(
              color: Colors.black,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircleAvatar(
                      radius: 50,
                      backgroundColor: Colors.grey,
                      backgroundImage: widget.customerInfo?['avatar'] != null
                          ? NetworkImage(widget.customerInfo!['avatar'])
                          : null,
                      child: widget.customerInfo?['avatar'] == null
                          ? const Icon(Icons.person, size: 50, color: Colors.white)
                          : null,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      widget.customerInfo?['name'] ?? 'Customer',
                      style: const TextStyle(color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _getCallStatusText(),
                      style: const TextStyle(color: Colors.white70, fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildLocalVideo() {
    return Positioned(
      top: 100,
      right: 20,
      width: 120,
      height: 160,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.white, width: 2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: _localRenderer.srcObject != null && _isVideoEnabled
              ? RTCVideoView(
                  _localRenderer,
                  mirror: !_useBackCamera, // Don't mirror back camera
                  objectFit: RTCVideoViewObjectFit.RTCVideoViewObjectFitCover,
                )
              : Container(
                  color: Colors.grey[800],
                  child: const Icon(
                    Icons.videocam_off,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildCallStatus() {
    return Positioned(
      top: 200,
      left: 0,
      right: 0,
      child: Center(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.5),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            _getCallStatusText(),
            style: const TextStyle(color: Colors.white, fontSize: 16),
          ),
        ),
      ),
    );
  }

  Widget _buildCallControls() {
    if (_callState == CallState.ringing && widget.isIncomingCall) {
      return const SizedBox.shrink();
    }

    return Positioned(
      bottom: 50,
      left: 0,
      right: 0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Audio toggle
          _buildControlButton(
            icon: _isAudioEnabled ? Icons.mic : Icons.mic_off,
            onPressed: _toggleAudio,
            backgroundColor: _isAudioEnabled ? Colors.white.withOpacity(0.2) : Colors.red,
          ),
          
          // Video toggle
          _buildControlButton(
            icon: _isVideoEnabled ? Icons.videocam : Icons.videocam_off,
            onPressed: _toggleVideo,
            backgroundColor: _isVideoEnabled ? Colors.white.withOpacity(0.2) : Colors.red,
          ),
          
          // End call
          _buildControlButton(
            icon: Icons.call_end,
            onPressed: _endCall,
            backgroundColor: Colors.red,
            size: 70,
          ),
          
          // Speaker toggle
          _buildControlButton(
            icon: _isSpeakerEnabled ? Icons.volume_up : Icons.volume_down,
            onPressed: _toggleSpeaker,
            backgroundColor: _isSpeakerEnabled ? Colors.white.withOpacity(0.2) : Colors.grey,
          ),
          
          // Camera info
          _buildControlButton(
            icon: _useBackCamera ? Icons.camera_rear : Icons.camera_front,
            onPressed: _switchCamera,
            backgroundColor: Colors.white.withOpacity(0.2),
          ),
        ],
      ),
    );
  }

  Widget _buildIncomingCallControls() {
    return Positioned(
      bottom: 100,
      left: 0,
      right: 0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Reject call
          _buildControlButton(
            icon: Icons.call_end,
            onPressed: _rejectCall,
            backgroundColor: Colors.red,
            size: 70,
          ),
          
          // Accept call
          _buildControlButton(
            icon: Icons.call,
            onPressed: _acceptCall,
            backgroundColor: Colors.green,
            size: 70,
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color backgroundColor,
    double size = 60,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor,
        shape: BoxShape.circle,
      ),
      child: IconButton(
        icon: Icon(icon, color: Colors.white, size: size * 0.4),
        onPressed: onPressed,
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Positioned(
      top: 150,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.8),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          _errorMessage!,
          style: const TextStyle(color: Colors.white),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  String _getCallStatusText() {
    switch (_callState) {
      case CallState.initiating:
        return 'Calling customer...';
      case CallState.ringing:
        return 'Incoming call from customer';
      case CallState.connecting:
        return 'Connecting...';
      case CallState.connected:
        return 'Connected - Show your products!';
      case CallState.ended:
        return 'Call ended';
      case CallState.error:
        return 'Call failed';
      default:
        return '';
    }
  }
}
