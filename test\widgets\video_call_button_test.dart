import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../lib/widgets/video_call_button.dart';
import '../../lib/services/call_manager.dart';

void main() {
  group('VideoCallButton Widget Tests', () {
    testWidgets('should display loading indicator when isLoading is true', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoCallButton(
              isLoading: true,
              isAvailable: true,
              callState: CallState.idle,
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.byIcon(Icons.videocam), findsNothing);
    });

    testWidgets('should display video icon when idle and available', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoCallButton(
              isLoading: false,
              isAvailable: true,
              callState: CallState.idle,
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.videocam), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsNothing);
      
      final iconButton = tester.widget<IconButton>(find.byType(IconButton));
      expect(iconButton.onPressed, isNotNull);
    });

    testWidgets('should display disabled video icon when unavailable', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoCallButton(
              isLoading: false,
              isAvailable: false,
              callState: CallState.idle,
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.videocam_off), findsOneWidget);
      
      final iconButton = tester.widget<IconButton>(find.byType(IconButton));
      expect(iconButton.onPressed, isNull);
    });

    testWidgets('should display connecting icon when connecting', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoCallButton(
              isLoading: false,
              isAvailable: true,
              callState: CallState.connecting,
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.phone_callback), findsOneWidget);
    });

    testWidgets('should display animated icon when ringing', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoCallButton(
              isLoading: false,
              isAvailable: true,
              callState: CallState.ringing,
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.phone_in_talk), findsOneWidget);
      
      // Test animation
      await tester.pump(Duration(milliseconds: 500));
      await tester.pump(Duration(milliseconds: 500));
      
      // Animation should be running
      expect(find.byType(AnimatedBuilder), findsOneWidget);
    });

    testWidgets('should display connected icon when connected', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoCallButton(
              isLoading: false,
              isAvailable: true,
              callState: CallState.connected,
              onPressed: () {},
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.videocam), findsOneWidget);
      
      // Should have green color for connected state
      final container = tester.widget<Container>(find.byType(Container));
      final decoration = container.decoration as BoxDecoration;
      expect(decoration.color, equals(Colors.green.withOpacity(0.1)));
    });

    testWidgets('should call onPressed when tapped', (WidgetTester tester) async {
      var wasPressed = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoCallButton(
              isLoading: false,
              isAvailable: true,
              callState: CallState.idle,
              onPressed: () {
                wasPressed = true;
              },
            ),
          ),
        ),
      );

      await tester.tap(find.byType(IconButton));
      
      expect(wasPressed, isTrue);
    });

    testWidgets('should not call onPressed when unavailable', (WidgetTester tester) async {
      var wasPressed = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoCallButton(
              isLoading: false,
              isAvailable: false,
              callState: CallState.idle,
              onPressed: () {
                wasPressed = true;
              },
            ),
          ),
        ),
      );

      // Try to tap the button
      await tester.tap(find.byType(IconButton));
      
      expect(wasPressed, isFalse);
    });

    testWidgets('should show correct tooltip for each state', (WidgetTester tester) async {
      // Test idle state tooltip
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoCallButton(
              isLoading: false,
              isAvailable: true,
              callState: CallState.idle,
              onPressed: () {},
            ),
          ),
        ),
      );

      final iconButton = tester.widget<IconButton>(find.byType(IconButton));
      expect(iconButton.tooltip, equals('Start video call'));

      // Test unavailable state tooltip
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoCallButton(
              isLoading: false,
              isAvailable: false,
              callState: CallState.idle,
              onPressed: () {},
            ),
          ),
        ),
      );

      final unavailableButton = tester.widget<IconButton>(find.byType(IconButton));
      expect(unavailableButton.tooltip, equals('Unavailable'));
    });

    testWidgets('should have correct accessibility properties', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: VideoCallButton(
              isLoading: false,
              isAvailable: true,
              callState: CallState.idle,
              onPressed: () {},
            ),
          ),
        ),
      );

      // Check that the button is accessible
      expect(find.byType(IconButton), findsOneWidget);
      
      final iconButton = tester.widget<IconButton>(find.byType(IconButton));
      expect(iconButton.tooltip, isNotNull);
    });

    testWidgets('should handle rapid state changes', (WidgetTester tester) async {
      var callState = CallState.idle;
      
      await tester.pumpWidget(
        MaterialApp(
          home: StatefulBuilder(
            builder: (context, setState) {
              return Scaffold(
                body: Column(
                  children: [
                    VideoCallButton(
                      isLoading: false,
                      isAvailable: true,
                      callState: callState,
                      onPressed: () {},
                    ),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          callState = CallState.connecting;
                        });
                      },
                      child: Text('Change State'),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      );

      // Initial state
      expect(find.byIcon(Icons.videocam), findsOneWidget);

      // Change state
      await tester.tap(find.text('Change State'));
      await tester.pump();

      // Should show connecting icon
      expect(find.byIcon(Icons.phone_callback), findsOneWidget);
    });

    testWidgets('should maintain consistent sizing across states', (WidgetTester tester) async {
      const buttonStates = [
        CallState.idle,
        CallState.connecting,
        CallState.ringing,
        CallState.connected,
      ];

      for (final state in buttonStates) {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: VideoCallButton(
                isLoading: false,
                isAvailable: true,
                callState: state,
                onPressed: () {},
              ),
            ),
          ),
        );

        final container = tester.widget<Container>(find.byType(Container));
        expect(container.constraints?.maxWidth, equals(40));
        expect(container.constraints?.maxHeight, equals(40));
      }
    });
  });
}
