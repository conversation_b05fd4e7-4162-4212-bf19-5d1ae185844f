import 'dart:async';
import 'dart:io';

import 'package:assets_audio_player_plus/assets_audio_player.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_luban/flutter_luban.dart';
import 'package:fuodz/models/order.dart';
import 'package:random_string/random_string.dart';
import 'package:rxdart/rxdart.dart';
import 'package:singleton/singleton.dart';
import 'package:path_provider/path_provider.dart' as path_provider;

class AppService {
  //

  /// Factory method that reuse same instance automatically
  factory AppService() => Singleton.lazy(() => AppService._());

  /// Private constructor
  AppService._() {}

  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  BehaviorSubject<int> homePageIndex = BehaviorSubject<int>();
  BehaviorSubject<bool> refreshAssignedOrders = BehaviorSubject<bool>();
  BehaviorSubject<Order> addToAssignedOrders = BehaviorSubject<Order>();
  bool driverIsOnline = false;
  StreamSubscription? actionStream;
  List<int> ignoredOrders = [];
  AssetsAudioPlayerPlus assetsAudioPlayer = AssetsAudioPlayerPlus();

  changeHomePageIndex({int index = 2}) async {
    print("Changed Home Page");
    homePageIndex.add(index);
  }

  //
  void playNotificationSound() {
    try {
      assetsAudioPlayer.stop();
    } catch (error) {
      print("Error stopping audio player");
    }

    //
    assetsAudioPlayer.open(
      Audio("assets/audio/alert.mp3"),
      loopMode: LoopMode.single,
      // notificationSettings: NotificationSettings(
      //   nextEnabled: false,
      //   prevEnabled: false,
      //   stopEnabled: false,
      //   seekBarEnabled: false,
      // ),
      showNotification: false,
      playInBackground: PlayInBackground.enabled,
    );
  }

  void stopNotificationSound() {
    try {
      assetsAudioPlayer.stop();
    } catch (error) {
      print("Error stopping audio player");
    }
  }

  Future<File?> compressFile(File file, {int quality = 50}) async {
    final dir = await path_provider.getTemporaryDirectory();
    final targetPath =
        dir.absolute.path + "/temp_" + randomAlphaNumeric(10) + ".jpg";
    //
    CompressObject compressObject = CompressObject(
      imageFile: file, //image
      targetPath: targetPath, //compress to path
      quality: quality, //first compress quality, default 80
      //compress quality step, The bigger the fast, Smaller is more accurate, default 6
      step: 5,
      mode: CompressMode.LARGE2SMALL, //default AUTO
    );
    final compressFilePath = await Luban.compressImage(compressObject);

    if (kDebugMode) {
      print("unCompress file size ==> ${file.lengthSync()}");
      if (compressFilePath != null) {
        print("Compress successful");
      } else {
        print("compress failed");
      }
    }

    if (compressFilePath != null) {
      try {
        return File.fromUri(Uri(path: compressFilePath));
      } catch (error) {
        return File(compressFilePath);
      }
    }

    return null;
  }
}
