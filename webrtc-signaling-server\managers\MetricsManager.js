const logger = require('../utils/logger');

/**
 * Manages metrics and analytics for the signaling server
 * Tracks performance, usage, and error statistics
 */
class MetricsManager {
  constructor() {
    this.metrics = {
      // Connection metrics
      connections: {
        total: 0,
        current: 0,
        peak: 0,
        today: 0,
        failed: 0
      },
      
      // Call metrics
      calls: {
        total: 0,
        active: 0,
        completed: 0,
        failed: 0,
        averageDuration: 0,
        peakConcurrent: 0
      },
      
      // Signaling message metrics
      signaling: {
        offers: 0,
        answers: 0,
        iceCandidates: 0,
        connectionStates: 0,
        errors: 0
      },
      
      // Performance metrics
      performance: {
        messageLatency: [],
        memoryUsage: [],
        cpuUsage: [],
        lastUpdated: new Date()
      },
      
      // Error metrics
      errors: {
        total: 0,
        byType: {},
        recent: []
      },
      
      // Server metrics
      server: {
        startTime: new Date(),
        uptime: 0,
        version: process.env.npm_package_version || '1.0.0'
      }
    };

    // Start periodic metrics collection
    this.startPeriodicCollection();
  }

  /**
   * Increment connection count
   */
  incrementConnections() {
    this.metrics.connections.total++;
    this.metrics.connections.current++;
    this.metrics.connections.today++;
    
    if (this.metrics.connections.current > this.metrics.connections.peak) {
      this.metrics.connections.peak = this.metrics.connections.current;
    }
  }

  /**
   * Decrement connection count
   */
  decrementConnections() {
    if (this.metrics.connections.current > 0) {
      this.metrics.connections.current--;
    }
  }

  /**
   * Record a failed connection
   */
  recordConnectionFailure() {
    this.metrics.connections.failed++;
  }

  /**
   * Increment call joins
   */
  incrementCallJoins() {
    this.metrics.calls.total++;
    this.metrics.calls.active++;
  }

  /**
   * Increment call leaves
   */
  incrementCallLeaves() {
    if (this.metrics.calls.active > 0) {
      this.metrics.calls.active--;
    }
    this.metrics.calls.completed++;
  }

  /**
   * Record a failed call
   */
  recordCallFailure() {
    this.metrics.calls.failed++;
    if (this.metrics.calls.active > 0) {
      this.metrics.calls.active--;
    }
  }

  /**
   * Update peak concurrent calls
   * @param {number} count - Current call count
   */
  updatePeakConcurrentCalls(count) {
    if (count > this.metrics.calls.peakConcurrent) {
      this.metrics.calls.peakConcurrent = count;
    }
  }

  /**
   * Record call duration
   * @param {number} duration - Call duration in milliseconds
   */
  recordCallDuration(duration) {
    const currentAvg = this.metrics.calls.averageDuration;
    const totalCalls = this.metrics.calls.completed;
    
    if (totalCalls === 1) {
      this.metrics.calls.averageDuration = duration;
    } else {
      this.metrics.calls.averageDuration = 
        ((currentAvg * (totalCalls - 1)) + duration) / totalCalls;
    }
  }

  /**
   * Increment signaling message count
   * @param {string} type - Message type (offer, answer, ice-candidate, etc.)
   */
  incrementSignalingMessages(type) {
    switch (type) {
      case 'offer':
        this.metrics.signaling.offers++;
        break;
      case 'answer':
        this.metrics.signaling.answers++;
        break;
      case 'ice-candidate':
        this.metrics.signaling.iceCandidates++;
        break;
      case 'connection-state':
        this.metrics.signaling.connectionStates++;
        break;
      default:
        logger.warn('Unknown signaling message type', { type });
    }
  }

  /**
   * Record signaling error
   * @param {string} type - Error type
   * @param {string} message - Error message
   */
  recordSignalingError(type, message) {
    this.metrics.signaling.errors++;
    this.recordError(type, message);
  }

  /**
   * Record message latency
   * @param {number} latency - Latency in milliseconds
   */
  recordMessageLatency(latency) {
    this.metrics.performance.messageLatency.push({
      value: latency,
      timestamp: new Date()
    });

    // Keep only last 100 measurements
    if (this.metrics.performance.messageLatency.length > 100) {
      this.metrics.performance.messageLatency.shift();
    }
  }

  /**
   * Record an error
   * @param {string} type - Error type
   * @param {string} message - Error message
   * @param {Object} context - Additional context
   */
  recordError(type, message, context = {}) {
    this.metrics.errors.total++;
    
    // Count by type
    if (!this.metrics.errors.byType[type]) {
      this.metrics.errors.byType[type] = 0;
    }
    this.metrics.errors.byType[type]++;

    // Store recent errors (last 50)
    this.metrics.errors.recent.push({
      type: type,
      message: message,
      context: context,
      timestamp: new Date()
    });

    if (this.metrics.errors.recent.length > 50) {
      this.metrics.errors.recent.shift();
    }
  }

  /**
   * Get current metrics
   * @returns {Object} Current metrics
   */
  getMetrics() {
    // Update uptime
    this.metrics.server.uptime = Date.now() - this.metrics.server.startTime.getTime();
    
    // Calculate average latency
    const latencies = this.metrics.performance.messageLatency;
    const avgLatency = latencies.length > 0 
      ? latencies.reduce((sum, item) => sum + item.value, 0) / latencies.length 
      : 0;

    return {
      ...this.metrics,
      performance: {
        ...this.metrics.performance,
        averageLatency: Math.round(avgLatency * 100) / 100,
        currentMemoryUsage: process.memoryUsage(),
        lastUpdated: new Date()
      },
      summary: {
        connectionsPerHour: this.calculateConnectionsPerHour(),
        callsPerHour: this.calculateCallsPerHour(),
        errorRate: this.calculateErrorRate(),
        averageCallDuration: Math.round(this.metrics.calls.averageDuration / 1000), // in seconds
        successRate: this.calculateSuccessRate()
      }
    };
  }

  /**
   * Get metrics summary for health checks
   * @returns {Object} Metrics summary
   */
  getHealthMetrics() {
    return {
      connections: {
        current: this.metrics.connections.current,
        peak: this.metrics.connections.peak,
        failed: this.metrics.connections.failed
      },
      calls: {
        active: this.metrics.calls.active,
        total: this.metrics.calls.total,
        failed: this.metrics.calls.failed
      },
      errors: {
        total: this.metrics.errors.total,
        recent: this.metrics.errors.recent.slice(-5) // Last 5 errors
      },
      uptime: Date.now() - this.metrics.server.startTime.getTime(),
      memoryUsage: process.memoryUsage()
    };
  }

  /**
   * Reset daily metrics
   */
  resetDailyMetrics() {
    this.metrics.connections.today = 0;
    logger.info('Daily metrics reset');
  }

  /**
   * Calculate connections per hour
   * @returns {number} Connections per hour
   * @private
   */
  calculateConnectionsPerHour() {
    const uptimeHours = (Date.now() - this.metrics.server.startTime.getTime()) / (1000 * 60 * 60);
    return uptimeHours > 0 ? Math.round(this.metrics.connections.total / uptimeHours) : 0;
  }

  /**
   * Calculate calls per hour
   * @returns {number} Calls per hour
   * @private
   */
  calculateCallsPerHour() {
    const uptimeHours = (Date.now() - this.metrics.server.startTime.getTime()) / (1000 * 60 * 60);
    return uptimeHours > 0 ? Math.round(this.metrics.calls.total / uptimeHours) : 0;
  }

  /**
   * Calculate error rate
   * @returns {number} Error rate as percentage
   * @private
   */
  calculateErrorRate() {
    const totalMessages = this.metrics.signaling.offers + 
                         this.metrics.signaling.answers + 
                         this.metrics.signaling.iceCandidates + 
                         this.metrics.signaling.connectionStates;
    
    return totalMessages > 0 
      ? Math.round((this.metrics.signaling.errors / totalMessages) * 100 * 100) / 100 
      : 0;
  }

  /**
   * Calculate success rate
   * @returns {number} Success rate as percentage
   * @private
   */
  calculateSuccessRate() {
    const totalCalls = this.metrics.calls.total;
    const successfulCalls = this.metrics.calls.completed;
    
    return totalCalls > 0 
      ? Math.round((successfulCalls / totalCalls) * 100 * 100) / 100 
      : 100;
  }

  /**
   * Start periodic metrics collection
   * @private
   */
  startPeriodicCollection() {
    // Collect memory usage every 30 seconds
    setInterval(() => {
      const memUsage = process.memoryUsage();
      this.metrics.performance.memoryUsage.push({
        ...memUsage,
        timestamp: new Date()
      });

      // Keep only last 100 measurements
      if (this.metrics.performance.memoryUsage.length > 100) {
        this.metrics.performance.memoryUsage.shift();
      }
    }, 30000);

    // Reset daily metrics at midnight
    setInterval(() => {
      const now = new Date();
      if (now.getHours() === 0 && now.getMinutes() === 0) {
        this.resetDailyMetrics();
      }
    }, 60000); // Check every minute
  }

  /**
   * Export metrics for external monitoring
   * @returns {Object} Formatted metrics for export
   */
  exportMetrics() {
    const metrics = this.getMetrics();
    
    return {
      timestamp: new Date().toISOString(),
      server: {
        uptime: metrics.server.uptime,
        version: metrics.server.version
      },
      connections: {
        current: metrics.connections.current,
        total: metrics.connections.total,
        peak: metrics.connections.peak,
        failed: metrics.connections.failed
      },
      calls: {
        active: metrics.calls.active,
        total: metrics.calls.total,
        completed: metrics.calls.completed,
        failed: metrics.calls.failed,
        averageDuration: metrics.summary.averageCallDuration
      },
      signaling: {
        totalMessages: metrics.signaling.offers + metrics.signaling.answers + 
                      metrics.signaling.iceCandidates + metrics.signaling.connectionStates,
        errors: metrics.signaling.errors,
        errorRate: metrics.summary.errorRate
      },
      performance: {
        averageLatency: metrics.performance.averageLatency,
        memoryUsage: metrics.performance.currentMemoryUsage
      }
    };
  }

  /**
   * Reset all metrics (for testing)
   */
  reset() {
    this.metrics = {
      connections: { total: 0, current: 0, peak: 0, today: 0, failed: 0 },
      calls: { total: 0, active: 0, completed: 0, failed: 0, averageDuration: 0, peakConcurrent: 0 },
      signaling: { offers: 0, answers: 0, iceCandidates: 0, connectionStates: 0, errors: 0 },
      performance: { messageLatency: [], memoryUsage: [], cpuUsage: [], lastUpdated: new Date() },
      errors: { total: 0, byType: {}, recent: [] },
      server: { startTime: new Date(), uptime: 0, version: process.env.npm_package_version || '1.0.0' }
    };
    
    logger.info('Metrics manager reset');
  }
}

module.exports = MetricsManager;
