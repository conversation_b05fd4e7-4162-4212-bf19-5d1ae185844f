const Joi = require('joi');
const logger = require('./logger');

/**
 * Validation utilities for WebRTC signaling messages
 */

// Common validation schemas
const schemas = {
  // User ID validation
  userId: Joi.alternatives().try(
    Joi.string().min(1).max(50),
    Joi.number().integer().positive()
  ).required(),

  // Call ID validation
  callId: Joi.string().min(1).max(100).required(),

  // WebRTC SDP validation
  sdp: Joi.string().min(10).max(50000).required(),

  // ICE candidate validation
  iceCandidate: Joi.object({
    candidate: Joi.string().required(),
    sdpMid: Joi.string().allow(null, ''),
    sdpMLineIndex: Joi.number().integer().min(0).allow(null)
  }).required(),

  // Connection state validation
  connectionState: Joi.string().valid(
    'new', 'connecting', 'connected', 'disconnected', 'failed', 'closed'
  ).required(),

  // ICE connection state validation
  iceConnectionState: Joi.string().valid(
    'new', 'checking', 'connected', 'completed', 'failed', 'disconnected', 'closed'
  ),

  // ICE gathering state validation
  iceGatheringState: Joi.string().valid(
    'new', 'gathering', 'complete'
  )
};

/**
 * Validate connection data
 * @param {Object} data - Connection data to validate
 * @returns {Object} Validation result
 */
function validateConnection(data) {
  const schema = Joi.object({
    user_id: schemas.userId,
    token: Joi.string().min(10).max(1000).required()
  });

  const { error, value } = schema.validate(data);
  
  return {
    isValid: !error,
    error: error ? error.details[0].message : null,
    data: value
  };
}

/**
 * Validate signaling message
 * @param {Object} data - Message data to validate
 * @param {Array} requiredFields - Required fields for this message type
 * @returns {Object} Validation result
 */
function validateSignalingMessage(data, requiredFields = []) {
  try {
    if (!data || typeof data !== 'object') {
      return {
        isValid: false,
        error: 'Message data must be an object'
      };
    }

    // Build dynamic schema based on required fields
    const schemaFields = {};
    
    requiredFields.forEach(field => {
      switch (field) {
        case 'call_id':
          schemaFields.call_id = schemas.callId;
          break;
        case 'target_user_id':
          schemaFields.target_user_id = schemas.userId;
          break;
        case 'offer':
          schemaFields.offer = Joi.object({
            type: Joi.string().valid('offer').required(),
            sdp: schemas.sdp
          }).required();
          break;
        case 'answer':
          schemaFields.answer = Joi.object({
            type: Joi.string().valid('answer').required(),
            sdp: schemas.sdp
          }).required();
          break;
        case 'candidate':
          schemaFields.candidate = schemas.iceCandidate;
          break;
        case 'state':
          schemaFields.state = schemas.connectionState;
          break;
        default:
          schemaFields[field] = Joi.any().required();
      }
    });

    // Add optional fields
    if (!schemaFields.target_user_id) {
      schemaFields.target_user_id = schemas.userId.optional();
    }
    if (!schemaFields.ice_connection_state) {
      schemaFields.ice_connection_state = schemas.iceConnectionState.optional();
    }
    if (!schemaFields.ice_gathering_state) {
      schemaFields.ice_gathering_state = schemas.iceGatheringState.optional();
    }

    const schema = Joi.object(schemaFields);
    const { error, value } = schema.validate(data);

    return {
      isValid: !error,
      error: error ? error.details[0].message : null,
      data: value
    };

  } catch (validationError) {
    logger.error('Validation error', {
      error: validationError.message,
      data: data,
      requiredFields: requiredFields
    });

    return {
      isValid: false,
      error: 'Validation failed'
    };
  }
}

/**
 * Validate WebRTC offer
 * @param {Object} offer - Offer object to validate
 * @returns {Object} Validation result
 */
function validateOffer(offer) {
  const schema = Joi.object({
    type: Joi.string().valid('offer').required(),
    sdp: schemas.sdp
  });

  const { error, value } = schema.validate(offer);
  
  return {
    isValid: !error,
    error: error ? error.details[0].message : null,
    data: value
  };
}

/**
 * Validate WebRTC answer
 * @param {Object} answer - Answer object to validate
 * @returns {Object} Validation result
 */
function validateAnswer(answer) {
  const schema = Joi.object({
    type: Joi.string().valid('answer').required(),
    sdp: schemas.sdp
  });

  const { error, value } = schema.validate(answer);
  
  return {
    isValid: !error,
    error: error ? error.details[0].message : null,
    data: value
  };
}

/**
 * Validate ICE candidate
 * @param {Object} candidate - ICE candidate object to validate
 * @returns {Object} Validation result
 */
function validateIceCandidate(candidate) {
  const { error, value } = schemas.iceCandidate.validate(candidate);
  
  return {
    isValid: !error,
    error: error ? error.details[0].message : null,
    data: value
  };
}

/**
 * Validate call join request
 * @param {Object} data - Join request data
 * @returns {Object} Validation result
 */
function validateJoinCall(data) {
  const schema = Joi.object({
    call_id: schemas.callId,
    user_id: schemas.userId.optional() // Optional since it comes from socket auth
  });

  const { error, value } = schema.validate(data);
  
  return {
    isValid: !error,
    error: error ? error.details[0].message : null,
    data: value
  };
}

/**
 * Validate connection state update
 * @param {Object} data - Connection state data
 * @returns {Object} Validation result
 */
function validateConnectionState(data) {
  const schema = Joi.object({
    call_id: schemas.callId,
    state: schemas.connectionState,
    ice_connection_state: schemas.iceConnectionState.optional(),
    ice_gathering_state: schemas.iceGatheringState.optional(),
    target_user_id: schemas.userId.optional()
  });

  const { error, value } = schema.validate(data);
  
  return {
    isValid: !error,
    error: error ? error.details[0].message : null,
    data: value
  };
}

/**
 * Sanitize message data to prevent XSS and injection attacks
 * @param {Object} data - Data to sanitize
 * @returns {Object} Sanitized data
 */
function sanitizeData(data) {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const sanitized = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      // Basic sanitization - remove potentially dangerous characters
      sanitized[key] = value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .trim();
    } else if (typeof value === 'object' && value !== null) {
      sanitized[key] = sanitizeData(value);
    } else {
      sanitized[key] = value;
    }
  }

  return sanitized;
}

/**
 * Validate message size to prevent DoS attacks
 * @param {Object} data - Message data
 * @param {number} maxSize - Maximum allowed size in bytes
 * @returns {boolean} True if message size is acceptable
 */
function validateMessageSize(data, maxSize = 100000) { // 100KB default
  try {
    const messageSize = JSON.stringify(data).length;
    return messageSize <= maxSize;
  } catch (error) {
    logger.error('Error calculating message size', { error: error.message });
    return false;
  }
}

/**
 * Validate rate limiting data
 * @param {string} userId - User ID
 * @param {string} eventType - Event type
 * @param {number} timestamp - Event timestamp
 * @returns {Object} Validation result
 */
function validateRateLimit(userId, eventType, timestamp) {
  const schema = Joi.object({
    userId: schemas.userId,
    eventType: Joi.string().min(1).max(50).required(),
    timestamp: Joi.number().integer().positive().required()
  });

  const { error } = schema.validate({ userId, eventType, timestamp });
  
  return {
    isValid: !error,
    error: error ? error.details[0].message : null
  };
}

/**
 * Create custom validation schema
 * @param {Object} fields - Field definitions
 * @returns {Object} Joi schema
 */
function createCustomSchema(fields) {
  return Joi.object(fields);
}

module.exports = {
  validateConnection,
  validateSignalingMessage,
  validateOffer,
  validateAnswer,
  validateIceCandidate,
  validateJoinCall,
  validateConnectionState,
  sanitizeData,
  validateMessageSize,
  validateRateLimit,
  createCustomSchema,
  schemas
};
