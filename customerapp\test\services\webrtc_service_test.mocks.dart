// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in fuodz/test/services/webrtc_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i13;
import 'dart:ui' as _i18;

import 'package:flutter_webrtc/src/native/rtc_video_renderer_impl.dart' as _i17;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i16;
import 'package:webrtc_interface/src/enums.dart' as _i9;
import 'package:webrtc_interface/src/media_stream.dart' as _i7;
import 'package:webrtc_interface/src/media_stream_track.dart' as _i11;
import 'package:webrtc_interface/src/rtc_data_channel.dart' as _i3;
import 'package:webrtc_interface/src/rtc_dtmf_sender.dart' as _i4;
import 'package:webrtc_interface/src/rtc_ice_candidate.dart' as _i10;
import 'package:webrtc_interface/src/rtc_rtp_receiver.dart' as _i14;
import 'package:webrtc_interface/src/rtc_rtp_sender.dart' as _i5;
import 'package:webrtc_interface/src/rtc_rtp_transceiver.dart' as _i6;
import 'package:webrtc_interface/src/rtc_session_description.dart' as _i2;
import 'package:webrtc_interface/src/rtc_stats_report.dart' as _i15;
import 'package:webrtc_interface/src/rtc_track_event.dart' as _i12;
import 'package:webrtc_interface/webrtc_interface.dart' as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeRTCSessionDescription_0 extends _i1.SmartFake
    implements _i2.RTCSessionDescription {
  _FakeRTCSessionDescription_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRTCDataChannel_1 extends _i1.SmartFake
    implements _i3.RTCDataChannel {
  _FakeRTCDataChannel_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRTCDTMFSender_2 extends _i1.SmartFake implements _i4.RTCDTMFSender {
  _FakeRTCDTMFSender_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRTCRtpSender_3 extends _i1.SmartFake implements _i5.RTCRtpSender {
  _FakeRTCRtpSender_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRTCRtpTransceiver_4 extends _i1.SmartFake
    implements _i6.RTCRtpTransceiver {
  _FakeRTCRtpTransceiver_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeMediaStream_5 extends _i1.SmartFake implements _i7.MediaStream {
  _FakeMediaStream_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeRTCVideoValue_6 extends _i1.SmartFake implements _i8.RTCVideoValue {
  _FakeRTCVideoValue_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [RTCPeerConnection].
///
/// See the documentation for Mockito's code generation for more information.
class MockRTCPeerConnection extends _i1.Mock implements _i8.RTCPeerConnection {
  MockRTCPeerConnection() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set onSignalingState(
          dynamic Function(_i9.RTCSignalingState)? _onSignalingState) =>
      super.noSuchMethod(
        Invocation.setter(
          #onSignalingState,
          _onSignalingState,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onConnectionState(
          dynamic Function(_i9.RTCPeerConnectionState)? _onConnectionState) =>
      super.noSuchMethod(
        Invocation.setter(
          #onConnectionState,
          _onConnectionState,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onIceGatheringState(
          dynamic Function(_i9.RTCIceGatheringState)? _onIceGatheringState) =>
      super.noSuchMethod(
        Invocation.setter(
          #onIceGatheringState,
          _onIceGatheringState,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onIceConnectionState(
          dynamic Function(_i9.RTCIceConnectionState)? _onIceConnectionState) =>
      super.noSuchMethod(
        Invocation.setter(
          #onIceConnectionState,
          _onIceConnectionState,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onIceCandidate(dynamic Function(_i10.RTCIceCandidate)? _onIceCandidate) =>
      super.noSuchMethod(
        Invocation.setter(
          #onIceCandidate,
          _onIceCandidate,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onAddStream(dynamic Function(_i7.MediaStream)? _onAddStream) =>
      super.noSuchMethod(
        Invocation.setter(
          #onAddStream,
          _onAddStream,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onRemoveStream(dynamic Function(_i7.MediaStream)? _onRemoveStream) =>
      super.noSuchMethod(
        Invocation.setter(
          #onRemoveStream,
          _onRemoveStream,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onAddTrack(
          dynamic Function(
            _i7.MediaStream,
            _i11.MediaStreamTrack,
          )? _onAddTrack) =>
      super.noSuchMethod(
        Invocation.setter(
          #onAddTrack,
          _onAddTrack,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onRemoveTrack(
          dynamic Function(
            _i7.MediaStream,
            _i11.MediaStreamTrack,
          )? _onRemoveTrack) =>
      super.noSuchMethod(
        Invocation.setter(
          #onRemoveTrack,
          _onRemoveTrack,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onDataChannel(dynamic Function(_i3.RTCDataChannel)? _onDataChannel) =>
      super.noSuchMethod(
        Invocation.setter(
          #onDataChannel,
          _onDataChannel,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onRenegotiationNeeded(dynamic Function()? _onRenegotiationNeeded) =>
      super.noSuchMethod(
        Invocation.setter(
          #onRenegotiationNeeded,
          _onRenegotiationNeeded,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onTrack(dynamic Function(_i12.RTCTrackEvent)? _onTrack) =>
      super.noSuchMethod(
        Invocation.setter(
          #onTrack,
          _onTrack,
        ),
        returnValueForMissingStub: null,
      );

  @override
  Map<String, dynamic> get getConfiguration => (super.noSuchMethod(
        Invocation.getter(#getConfiguration),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  _i13.Future<List<_i5.RTCRtpSender>> get senders => (super.noSuchMethod(
        Invocation.getter(#senders),
        returnValue:
            _i13.Future<List<_i5.RTCRtpSender>>.value(<_i5.RTCRtpSender>[]),
      ) as _i13.Future<List<_i5.RTCRtpSender>>);

  @override
  _i13.Future<List<_i14.RTCRtpReceiver>> get receivers => (super.noSuchMethod(
        Invocation.getter(#receivers),
        returnValue: _i13.Future<List<_i14.RTCRtpReceiver>>.value(
            <_i14.RTCRtpReceiver>[]),
      ) as _i13.Future<List<_i14.RTCRtpReceiver>>);

  @override
  _i13.Future<List<_i6.RTCRtpTransceiver>> get transceivers =>
      (super.noSuchMethod(
        Invocation.getter(#transceivers),
        returnValue: _i13.Future<List<_i6.RTCRtpTransceiver>>.value(
            <_i6.RTCRtpTransceiver>[]),
      ) as _i13.Future<List<_i6.RTCRtpTransceiver>>);

  @override
  _i13.Future<_i9.RTCSignalingState?> getSignalingState() =>
      (super.noSuchMethod(
        Invocation.method(
          #getSignalingState,
          [],
        ),
        returnValue: _i13.Future<_i9.RTCSignalingState?>.value(),
      ) as _i13.Future<_i9.RTCSignalingState?>);

  @override
  _i13.Future<_i9.RTCIceGatheringState?> getIceGatheringState() =>
      (super.noSuchMethod(
        Invocation.method(
          #getIceGatheringState,
          [],
        ),
        returnValue: _i13.Future<_i9.RTCIceGatheringState?>.value(),
      ) as _i13.Future<_i9.RTCIceGatheringState?>);

  @override
  _i13.Future<_i9.RTCIceConnectionState?> getIceConnectionState() =>
      (super.noSuchMethod(
        Invocation.method(
          #getIceConnectionState,
          [],
        ),
        returnValue: _i13.Future<_i9.RTCIceConnectionState?>.value(),
      ) as _i13.Future<_i9.RTCIceConnectionState?>);

  @override
  _i13.Future<_i9.RTCPeerConnectionState?> getConnectionState() =>
      (super.noSuchMethod(
        Invocation.method(
          #getConnectionState,
          [],
        ),
        returnValue: _i13.Future<_i9.RTCPeerConnectionState?>.value(),
      ) as _i13.Future<_i9.RTCPeerConnectionState?>);

  @override
  _i13.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<void> setConfiguration(Map<String, dynamic>? configuration) =>
      (super.noSuchMethod(
        Invocation.method(
          #setConfiguration,
          [configuration],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<_i2.RTCSessionDescription> createOffer(
          [Map<String, dynamic>? constraints]) =>
      (super.noSuchMethod(
        Invocation.method(
          #createOffer,
          [constraints],
        ),
        returnValue: _i13.Future<_i2.RTCSessionDescription>.value(
            _FakeRTCSessionDescription_0(
          this,
          Invocation.method(
            #createOffer,
            [constraints],
          ),
        )),
      ) as _i13.Future<_i2.RTCSessionDescription>);

  @override
  _i13.Future<_i2.RTCSessionDescription> createAnswer(
          [Map<String, dynamic>? constraints]) =>
      (super.noSuchMethod(
        Invocation.method(
          #createAnswer,
          [constraints],
        ),
        returnValue: _i13.Future<_i2.RTCSessionDescription>.value(
            _FakeRTCSessionDescription_0(
          this,
          Invocation.method(
            #createAnswer,
            [constraints],
          ),
        )),
      ) as _i13.Future<_i2.RTCSessionDescription>);

  @override
  _i13.Future<void> addStream(_i7.MediaStream? stream) => (super.noSuchMethod(
        Invocation.method(
          #addStream,
          [stream],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<void> removeStream(_i7.MediaStream? stream) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeStream,
          [stream],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<_i2.RTCSessionDescription?> getLocalDescription() =>
      (super.noSuchMethod(
        Invocation.method(
          #getLocalDescription,
          [],
        ),
        returnValue: _i13.Future<_i2.RTCSessionDescription?>.value(),
      ) as _i13.Future<_i2.RTCSessionDescription?>);

  @override
  _i13.Future<void> setLocalDescription(
          _i2.RTCSessionDescription? description) =>
      (super.noSuchMethod(
        Invocation.method(
          #setLocalDescription,
          [description],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<_i2.RTCSessionDescription?> getRemoteDescription() =>
      (super.noSuchMethod(
        Invocation.method(
          #getRemoteDescription,
          [],
        ),
        returnValue: _i13.Future<_i2.RTCSessionDescription?>.value(),
      ) as _i13.Future<_i2.RTCSessionDescription?>);

  @override
  _i13.Future<void> setRemoteDescription(
          _i2.RTCSessionDescription? description) =>
      (super.noSuchMethod(
        Invocation.method(
          #setRemoteDescription,
          [description],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<void> addCandidate(_i10.RTCIceCandidate? candidate) =>
      (super.noSuchMethod(
        Invocation.method(
          #addCandidate,
          [candidate],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<List<_i15.StatsReport>> getStats(
          [_i11.MediaStreamTrack? track]) =>
      (super.noSuchMethod(
        Invocation.method(
          #getStats,
          [track],
        ),
        returnValue:
            _i13.Future<List<_i15.StatsReport>>.value(<_i15.StatsReport>[]),
      ) as _i13.Future<List<_i15.StatsReport>>);

  @override
  List<_i7.MediaStream?> getLocalStreams() => (super.noSuchMethod(
        Invocation.method(
          #getLocalStreams,
          [],
        ),
        returnValue: <_i7.MediaStream?>[],
      ) as List<_i7.MediaStream?>);

  @override
  List<_i7.MediaStream?> getRemoteStreams() => (super.noSuchMethod(
        Invocation.method(
          #getRemoteStreams,
          [],
        ),
        returnValue: <_i7.MediaStream?>[],
      ) as List<_i7.MediaStream?>);

  @override
  _i13.Future<_i3.RTCDataChannel> createDataChannel(
    String? label,
    _i3.RTCDataChannelInit? dataChannelDict,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #createDataChannel,
          [
            label,
            dataChannelDict,
          ],
        ),
        returnValue:
            _i13.Future<_i3.RTCDataChannel>.value(_FakeRTCDataChannel_1(
          this,
          Invocation.method(
            #createDataChannel,
            [
              label,
              dataChannelDict,
            ],
          ),
        )),
      ) as _i13.Future<_i3.RTCDataChannel>);

  @override
  _i13.Future<void> restartIce() => (super.noSuchMethod(
        Invocation.method(
          #restartIce,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i4.RTCDTMFSender createDtmfSender(_i11.MediaStreamTrack? track) =>
      (super.noSuchMethod(
        Invocation.method(
          #createDtmfSender,
          [track],
        ),
        returnValue: _FakeRTCDTMFSender_2(
          this,
          Invocation.method(
            #createDtmfSender,
            [track],
          ),
        ),
      ) as _i4.RTCDTMFSender);

  @override
  _i13.Future<List<_i5.RTCRtpSender>> getSenders() => (super.noSuchMethod(
        Invocation.method(
          #getSenders,
          [],
        ),
        returnValue:
            _i13.Future<List<_i5.RTCRtpSender>>.value(<_i5.RTCRtpSender>[]),
      ) as _i13.Future<List<_i5.RTCRtpSender>>);

  @override
  _i13.Future<List<_i14.RTCRtpReceiver>> getReceivers() => (super.noSuchMethod(
        Invocation.method(
          #getReceivers,
          [],
        ),
        returnValue: _i13.Future<List<_i14.RTCRtpReceiver>>.value(
            <_i14.RTCRtpReceiver>[]),
      ) as _i13.Future<List<_i14.RTCRtpReceiver>>);

  @override
  _i13.Future<List<_i6.RTCRtpTransceiver>> getTransceivers() =>
      (super.noSuchMethod(
        Invocation.method(
          #getTransceivers,
          [],
        ),
        returnValue: _i13.Future<List<_i6.RTCRtpTransceiver>>.value(
            <_i6.RTCRtpTransceiver>[]),
      ) as _i13.Future<List<_i6.RTCRtpTransceiver>>);

  @override
  _i13.Future<_i5.RTCRtpSender> addTrack(
    _i11.MediaStreamTrack? track, [
    _i7.MediaStream? stream,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #addTrack,
          [
            track,
            stream,
          ],
        ),
        returnValue: _i13.Future<_i5.RTCRtpSender>.value(_FakeRTCRtpSender_3(
          this,
          Invocation.method(
            #addTrack,
            [
              track,
              stream,
            ],
          ),
        )),
      ) as _i13.Future<_i5.RTCRtpSender>);

  @override
  _i13.Future<bool> removeTrack(_i5.RTCRtpSender? sender) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeTrack,
          [sender],
        ),
        returnValue: _i13.Future<bool>.value(false),
      ) as _i13.Future<bool>);

  @override
  _i13.Future<_i6.RTCRtpTransceiver> addTransceiver({
    _i11.MediaStreamTrack? track,
    _i9.RTCRtpMediaType? kind,
    _i6.RTCRtpTransceiverInit? init,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addTransceiver,
          [],
          {
            #track: track,
            #kind: kind,
            #init: init,
          },
        ),
        returnValue:
            _i13.Future<_i6.RTCRtpTransceiver>.value(_FakeRTCRtpTransceiver_4(
          this,
          Invocation.method(
            #addTransceiver,
            [],
            {
              #track: track,
              #kind: kind,
              #init: init,
            },
          ),
        )),
      ) as _i13.Future<_i6.RTCRtpTransceiver>);
}

/// A class which mocks [MediaStream].
///
/// See the documentation for Mockito's code generation for more information.
class MockMediaStream extends _i1.Mock implements _i7.MediaStream {
  MockMediaStream() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set onAddTrack(dynamic Function(_i11.MediaStreamTrack)? _onAddTrack) =>
      super.noSuchMethod(
        Invocation.setter(
          #onAddTrack,
          _onAddTrack,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onRemoveTrack(dynamic Function(_i11.MediaStreamTrack)? _onRemoveTrack) =>
      super.noSuchMethod(
        Invocation.setter(
          #onRemoveTrack,
          _onRemoveTrack,
        ),
        returnValueForMissingStub: null,
      );

  @override
  String get id => (super.noSuchMethod(
        Invocation.getter(#id),
        returnValue: _i16.dummyValue<String>(
          this,
          Invocation.getter(#id),
        ),
      ) as String);

  @override
  String get ownerTag => (super.noSuchMethod(
        Invocation.getter(#ownerTag),
        returnValue: _i16.dummyValue<String>(
          this,
          Invocation.getter(#ownerTag),
        ),
      ) as String);

  @override
  _i13.Future<void> getMediaTracks() => (super.noSuchMethod(
        Invocation.method(
          #getMediaTracks,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<void> addTrack(
    _i11.MediaStreamTrack? track, {
    bool? addToNative = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #addTrack,
          [track],
          {#addToNative: addToNative},
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<void> removeTrack(
    _i11.MediaStreamTrack? track, {
    bool? removeFromNative = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeTrack,
          [track],
          {#removeFromNative: removeFromNative},
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  List<_i11.MediaStreamTrack> getTracks() => (super.noSuchMethod(
        Invocation.method(
          #getTracks,
          [],
        ),
        returnValue: <_i11.MediaStreamTrack>[],
      ) as List<_i11.MediaStreamTrack>);

  @override
  List<_i11.MediaStreamTrack> getAudioTracks() => (super.noSuchMethod(
        Invocation.method(
          #getAudioTracks,
          [],
        ),
        returnValue: <_i11.MediaStreamTrack>[],
      ) as List<_i11.MediaStreamTrack>);

  @override
  List<_i11.MediaStreamTrack> getVideoTracks() => (super.noSuchMethod(
        Invocation.method(
          #getVideoTracks,
          [],
        ),
        returnValue: <_i11.MediaStreamTrack>[],
      ) as List<_i11.MediaStreamTrack>);

  @override
  _i11.MediaStreamTrack? getTrackById(String? trackId) =>
      (super.noSuchMethod(Invocation.method(
        #getTrackById,
        [trackId],
      )) as _i11.MediaStreamTrack?);

  @override
  _i13.Future<_i7.MediaStream> clone() => (super.noSuchMethod(
        Invocation.method(
          #clone,
          [],
        ),
        returnValue: _i13.Future<_i7.MediaStream>.value(_FakeMediaStream_5(
          this,
          Invocation.method(
            #clone,
            [],
          ),
        )),
      ) as _i13.Future<_i7.MediaStream>);

  @override
  _i13.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);
}

/// A class which mocks [RTCVideoRenderer].
///
/// See the documentation for Mockito's code generation for more information.
class MockRTCVideoRenderer extends _i1.Mock implements _i17.RTCVideoRenderer {
  MockRTCVideoRenderer() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set onResize(Function? _onResize) => super.noSuchMethod(
        Invocation.setter(
          #onResize,
          _onResize,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set onFirstFrameRendered(Function? _onFirstFrameRendered) =>
      super.noSuchMethod(
        Invocation.setter(
          #onFirstFrameRendered,
          _onFirstFrameRendered,
        ),
        returnValueForMissingStub: null,
      );

  @override
  int get videoWidth => (super.noSuchMethod(
        Invocation.getter(#videoWidth),
        returnValue: 0,
      ) as int);

  @override
  int get videoHeight => (super.noSuchMethod(
        Invocation.getter(#videoHeight),
        returnValue: 0,
      ) as int);

  @override
  set srcObject(_i7.MediaStream? stream) => super.noSuchMethod(
        Invocation.setter(
          #srcObject,
          stream,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get renderVideo => (super.noSuchMethod(
        Invocation.getter(#renderVideo),
        returnValue: false,
      ) as bool);

  @override
  bool get muted => (super.noSuchMethod(
        Invocation.getter(#muted),
        returnValue: false,
      ) as bool);

  @override
  set muted(bool? mute) => super.noSuchMethod(
        Invocation.setter(
          #muted,
          mute,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i8.RTCVideoValue get value => (super.noSuchMethod(
        Invocation.getter(#value),
        returnValue: _FakeRTCVideoValue_6(
          this,
          Invocation.getter(#value),
        ),
      ) as _i8.RTCVideoValue);

  @override
  set value(_i8.RTCVideoValue? newValue) => super.noSuchMethod(
        Invocation.setter(
          #value,
          newValue,
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  _i13.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<void> setSrcObject({
    _i7.MediaStream? stream,
    String? trackId,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #setSrcObject,
          [],
          {
            #stream: stream,
            #trackId: trackId,
          },
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  _i13.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i13.Future<void>.value(),
        returnValueForMissingStub: _i13.Future<void>.value(),
      ) as _i13.Future<void>);

  @override
  void eventListener(dynamic event) => super.noSuchMethod(
        Invocation.method(
          #eventListener,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void errorListener(Object? obj) => super.noSuchMethod(
        Invocation.method(
          #errorListener,
          [obj],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i13.Future<bool> audioOutput(String? deviceId) => (super.noSuchMethod(
        Invocation.method(
          #audioOutput,
          [deviceId],
        ),
        returnValue: _i13.Future<bool>.value(false),
      ) as _i13.Future<bool>);

  @override
  void addListener(_i18.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i18.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [RTCSessionDescription].
///
/// See the documentation for Mockito's code generation for more information.
class MockRTCSessionDescription extends _i1.Mock
    implements _i2.RTCSessionDescription {
  MockRTCSessionDescription() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set sdp(String? _sdp) => super.noSuchMethod(
        Invocation.setter(
          #sdp,
          _sdp,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set type(String? _type) => super.noSuchMethod(
        Invocation.setter(
          #type,
          _type,
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [RTCIceCandidate].
///
/// See the documentation for Mockito's code generation for more information.
class MockRTCIceCandidate extends _i1.Mock implements _i10.RTCIceCandidate {
  MockRTCIceCandidate() {
    _i1.throwOnMissingStub(this);
  }
}
