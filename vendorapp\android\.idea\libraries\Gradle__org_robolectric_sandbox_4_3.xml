<component name="libraryTable">
  <library name="Gradle: org.robolectric:sandbox:4.3">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.robolectric/sandbox/4.3/2302e406aebab5f6843dbf6c2f21952fa86ec26f/sandbox-4.3.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.robolectric/sandbox/4.3/fccce0e3cb551aa6af3149394a5fe514a1d125db/sandbox-4.3-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.robolectric/sandbox/4.3/5ba1249513d93d257f067db2b11b2f2eea657980/sandbox-4.3-sources.jar!/" />
    </SOURCES>
  </library>
</component>