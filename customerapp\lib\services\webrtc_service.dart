import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:logger/logger.dart';

class WebRTCService {
  static final WebRTCService _instance = WebRTCService._internal();
  factory WebRTCService() => _instance;
  WebRTCService._internal();

  final Logger _logger = Logger();
  RTCPeerConnection? _peerConnection;
  MediaStream? _localStream;
  MediaStream? _remoteStream;
  
  // Callbacks
  Function(MediaStream)? onLocalStream;
  Function(MediaStream)? onRemoteStream;
  Function(RTCPeerConnectionState)? onConnectionStateChange;
  Function(RTCIceCandidate)? onIceCandidate;
  Function(String)? onError;

  // Configuration
  final Map<String, dynamic> _configuration = {
    'iceServers': [
      {'urls': 'stun:stun.l.google.com:19302'},
      {'urls': 'stun:stun1.l.google.com:19302'},
      {'urls': 'stun:stun2.l.google.com:19302'},
    ],
    'iceCandidatePoolSize': 10,
    'bundlePolicy': 'max-bundle',
    'rtcpMuxPolicy': 'require',
  };

  // Media constraints optimized for customer app
  final Map<String, dynamic> _mediaConstraints = {
    'audio': {
      'echoCancellation': true,
      'noiseSuppression': true,
      'autoGainControl': true,
      'googEchoCancellation': true,
      'googAutoGainControl': true,
      'googNoiseSuppression': true,
    },
    'video': {
      'facingMode': 'user', // Front camera for customers
      'width': {'ideal': 1280, 'max': 1920, 'min': 640},
      'height': {'ideal': 720, 'max': 1080, 'min': 480},
      'frameRate': {'ideal': 30, 'max': 60, 'min': 15},
    }
  };

  /// Initialize WebRTC peer connection
  Future<void> initialize() async {
    try {
      _peerConnection = await createPeerConnection(_configuration);
      _setupPeerConnectionListeners();
      _logger.i('Customer WebRTC peer connection initialized');
    } catch (e) {
      _logger.e('Failed to initialize WebRTC: $e');
      onError?.call('Failed to initialize video call: $e');
      rethrow;
    }
  }

  /// Set up peer connection event listeners
  void _setupPeerConnectionListeners() {
    _peerConnection!.onIceCandidate = (RTCIceCandidate candidate) {
      _logger.d('ICE candidate generated');
      onIceCandidate?.call(candidate);
    };

    _peerConnection!.onAddStream = (MediaStream stream) {
      _logger.i('Vendor stream received');
      _remoteStream = stream;
      onRemoteStream?.call(stream);
    };

    _peerConnection!.onRemoveStream = (MediaStream stream) {
      _logger.i('Vendor stream removed');
      _remoteStream = null;
    };

    _peerConnection!.onConnectionState = (RTCPeerConnectionState state) {
      _logger.i('Connection state changed: $state');
      onConnectionStateChange?.call(state);
      
      if (state == RTCPeerConnectionState.RTCPeerConnectionStateFailed) {
        onError?.call('Connection to vendor failed');
      }
    };

    _peerConnection!.onIceConnectionState = (RTCIceConnectionState state) {
      _logger.i('ICE connection state: $state');
      
      if (state == RTCIceConnectionState.RTCIceConnectionStateFailed) {
        onError?.call('Network connection failed');
      }
    };
  }

  /// Get user media (camera and microphone)
  Future<MediaStream> getUserMedia({
    bool video = true,
    bool audio = true,
  }) async {
    try {
      final constraints = <String, dynamic>{
        'audio': audio ? _mediaConstraints['audio'] : false,
        'video': video ? _mediaConstraints['video'] : false,
      };

      _localStream = await navigator.mediaDevices.getUserMedia(constraints);
      _logger.i('Customer media obtained: video=$video, audio=$audio');
      
      onLocalStream?.call(_localStream!);
      return _localStream!;
    } catch (e) {
      _logger.e('Failed to get user media: $e');
      onError?.call('Failed to access camera/microphone. Please check permissions.');
      rethrow;
    }
  }

  /// Create and return WebRTC offer (customer initiates call)
  Future<RTCSessionDescription> createOffer() async {
    try {
      if (_localStream != null) {
        await _peerConnection!.addStream(_localStream!);
      }

      final offer = await _peerConnection!.createOffer();
      await _peerConnection!.setLocalDescription(offer);
      
      _logger.i('Customer call offer created');
      return offer;
    } catch (e) {
      _logger.e('Failed to create offer: $e');
      onError?.call('Failed to initiate call: $e');
      rethrow;
    }
  }

  /// Create and return WebRTC answer (if customer receives call)
  Future<RTCSessionDescription> createAnswer() async {
    try {
      if (_localStream != null) {
        await _peerConnection!.addStream(_localStream!);
      }

      final answer = await _peerConnection!.createAnswer();
      await _peerConnection!.setLocalDescription(answer);
      
      _logger.i('Customer call answer created');
      return answer;
    } catch (e) {
      _logger.e('Failed to create answer: $e');
      onError?.call('Failed to answer call: $e');
      rethrow;
    }
  }

  /// Set remote description (vendor's offer or answer)
  Future<void> setRemoteDescription(RTCSessionDescription description) async {
    try {
      await _peerConnection!.setRemoteDescription(description);
      _logger.i('Vendor description set: ${description.type}');
    } catch (e) {
      _logger.e('Failed to set remote description: $e');
      onError?.call('Failed to connect with vendor: $e');
      rethrow;
    }
  }

  /// Add ICE candidate
  Future<void> addIceCandidate(RTCIceCandidate candidate) async {
    try {
      await _peerConnection!.addCandidate(candidate);
      _logger.d('ICE candidate added');
    } catch (e) {
      _logger.e('Failed to add ICE candidate: $e');
      // Don't throw error for ICE candidates as they can fail gracefully
    }
  }

  /// Toggle local video
  Future<bool> toggleVideo() async {
    if (_localStream != null && _localStream!.getVideoTracks().isNotEmpty) {
      final videoTrack = _localStream!.getVideoTracks().first;
      videoTrack.enabled = !videoTrack.enabled;
      _logger.i('Customer video toggled: ${videoTrack.enabled}');
      return videoTrack.enabled;
    }
    return false;
  }

  /// Toggle local audio
  Future<bool> toggleAudio() async {
    if (_localStream != null && _localStream!.getAudioTracks().isNotEmpty) {
      final audioTrack = _localStream!.getAudioTracks().first;
      audioTrack.enabled = !audioTrack.enabled;
      _logger.i('Customer audio toggled: ${audioTrack.enabled}');
      return audioTrack.enabled;
    }
    return false;
  }

  /// Switch camera (front/back)
  Future<void> switchCamera() async {
    if (_localStream != null && _localStream!.getVideoTracks().isNotEmpty) {
      try {
        final videoTrack = _localStream!.getVideoTracks().first;
        await Helper.switchCamera(videoTrack);
        _logger.i('Customer camera switched');
      } catch (e) {
        _logger.e('Failed to switch camera: $e');
        onError?.call('Failed to switch camera: $e');
      }
    }
  }

  /// Close connection and clean up resources
  Future<void> dispose() async {
    try {
      // Stop all tracks
      _localStream?.getTracks().forEach((track) {
        track.stop();
      });
      
      await _localStream?.dispose();
      await _remoteStream?.dispose();
      await _peerConnection?.close();
      
      _localStream = null;
      _remoteStream = null;
      _peerConnection = null;
      
      _logger.i('Customer WebRTC resources disposed');
    } catch (e) {
      _logger.e('Error disposing WebRTC resources: $e');
    }
  }

  // Getters
  RTCPeerConnection? get peerConnection => _peerConnection;
  MediaStream? get localStream => _localStream;
  MediaStream? get remoteStream => _remoteStream;
  bool get isInitialized => _peerConnection != null;
  bool get hasLocalStream => _localStream != null;
  bool get hasRemoteStream => _remoteStream != null;
  
  bool get isVideoEnabled => _localStream?.getVideoTracks().isNotEmpty == true && 
                            _localStream!.getVideoTracks().first.enabled;
  
  bool get isAudioEnabled => _localStream?.getAudioTracks().isNotEmpty == true && 
                            _localStream!.getAudioTracks().first.enabled;
}
