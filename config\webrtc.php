<?php

return [

    /*
    |--------------------------------------------------------------------------
    | WebRTC Signaling Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for WebRTC signaling server and data management.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Signaling Mode
    |--------------------------------------------------------------------------
    |
    | Determines how WebRTC signaling data is handled:
    | - 'hybrid': <PERSON><PERSON> stores offers/answers + WebSocket for ICE candidates
    | - 'laravel': All signaling data stored in Laravel (fallback mode)
    | - 'websocket': Direct WebSocket signaling only (no Laravel storage)
    |
    */
    'signaling_mode' => env('WEBRTC_SIGNALING_MODE', 'hybrid'),

    /*
    |--------------------------------------------------------------------------
    | Signaling Data Storage
    |--------------------------------------------------------------------------
    |
    | Configure what signaling data to store in Laravel database.
    |
    */
    'storage' => [
        'store_offers' => env('WEBRTC_STORE_OFFERS', true),
        'store_answers' => env('WEBRTC_STORE_ANSWERS', true),
        'store_ice_candidates' => env('WEBRTC_STORE_ICE_CANDIDATES', false), // Use cache instead
        'store_connection_state' => env('WEBRTC_STORE_CONNECTION_STATE', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Data Retention and Cleanup
    |--------------------------------------------------------------------------
    |
    | Configure how long signaling data is retained.
    |
    */
    'retention' => [
        'signaling_data_ttl' => env('WEBRTC_SIGNALING_TTL', 300), // 5 minutes
        'ice_candidate_ttl' => env('WEBRTC_ICE_TTL', 300), // 5 minutes
        'connection_state_ttl' => env('WEBRTC_CONNECTION_TTL', 1800), // 30 minutes
        'auto_cleanup' => env('WEBRTC_AUTO_CLEANUP', true),
        'cleanup_interval' => env('WEBRTC_CLEANUP_INTERVAL', 60), // 1 minute
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configure rate limits for signaling operations.
    |
    */
    'rate_limits' => [
        'ice_candidate_limit' => env('WEBRTC_ICE_CANDIDATE_LIMIT', 50),
        'signaling_requests_per_minute' => env('WEBRTC_SIGNALING_RPM', 60),
        'connection_state_updates_per_minute' => env('WEBRTC_CONNECTION_RPM', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | WebSocket Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for WebSocket-based signaling.
    |
    */
    'websocket' => [
        'enabled' => env('WEBRTC_WEBSOCKET_ENABLED', true),
        'fallback_to_http' => env('WEBRTC_WEBSOCKET_FALLBACK', true),
        'channel_prefix' => env('WEBRTC_CHANNEL_PREFIX', 'call'),
        'broadcast_driver' => env('BROADCAST_DRIVER', 'reverb'),
    ],

    /*
    |--------------------------------------------------------------------------
    | STUN/TURN Server Configuration
    |--------------------------------------------------------------------------
    |
    | Default ICE server configuration for WebRTC connections.
    |
    */
    'ice_servers' => [
        'stun' => [
            'enabled' => env('WEBRTC_STUN_ENABLED', true),
            'servers' => [
                env('WEBRTC_STUN_SERVER', 'stun:stun.l.google.com:19302'),
                'stun:stun1.l.google.com:19302',
                'stun:stun2.l.google.com:19302',
            ],
        ],
        'turn' => [
            'enabled' => env('WEBRTC_TURN_ENABLED', false),
            'servers' => [
                [
                    'urls' => env('WEBRTC_TURN_SERVER', 'turn:your-turn-server.com:3478'),
                    'username' => env('WEBRTC_TURN_USERNAME'),
                    'credential' => env('WEBRTC_TURN_CREDENTIAL'),
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Connection Quality Monitoring
    |--------------------------------------------------------------------------
    |
    | Configuration for monitoring WebRTC connection quality.
    |
    */
    'monitoring' => [
        'enabled' => env('WEBRTC_MONITORING_ENABLED', true),
        'collect_stats' => env('WEBRTC_COLLECT_STATS', true),
        'stats_interval' => env('WEBRTC_STATS_INTERVAL', 5), // seconds
        'quality_thresholds' => [
            'packet_loss_warning' => env('WEBRTC_PACKET_LOSS_WARNING', 5), // %
            'packet_loss_critical' => env('WEBRTC_PACKET_LOSS_CRITICAL', 10), // %
            'latency_warning' => env('WEBRTC_LATENCY_WARNING', 200), // ms
            'latency_critical' => env('WEBRTC_LATENCY_CRITICAL', 500), // ms
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | Security settings for WebRTC signaling.
    |
    */
    'security' => [
        'require_authentication' => env('WEBRTC_REQUIRE_AUTH', true),
        'validate_participants' => env('WEBRTC_VALIDATE_PARTICIPANTS', true),
        'encrypt_signaling_data' => env('WEBRTC_ENCRYPT_SIGNALING', false),
        'allowed_origins' => env('WEBRTC_ALLOWED_ORIGINS', '*'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configure logging for WebRTC signaling events.
    |
    */
    'logging' => [
        'enabled' => env('WEBRTC_LOGGING_ENABLED', true),
        'log_offers_answers' => env('WEBRTC_LOG_OFFERS_ANSWERS', true),
        'log_ice_candidates' => env('WEBRTC_LOG_ICE_CANDIDATES', false), // Too verbose
        'log_connection_states' => env('WEBRTC_LOG_CONNECTION_STATES', true),
        'log_signaling_errors' => env('WEBRTC_LOG_SIGNALING_ERRORS', true),
        'log_level' => env('WEBRTC_LOG_LEVEL', 'info'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Development and Testing
    |--------------------------------------------------------------------------
    |
    | Configuration for development and testing environments.
    |
    */
    'development' => [
        'mock_signaling' => env('WEBRTC_MOCK_SIGNALING', false),
        'simulate_network_issues' => env('WEBRTC_SIMULATE_NETWORK_ISSUES', false),
        'debug_mode' => env('WEBRTC_DEBUG_MODE', false),
        'test_ice_servers' => [
            'stun:stun.l.google.com:19302',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Optimization
    |--------------------------------------------------------------------------
    |
    | Performance-related configuration options.
    |
    */
    'performance' => [
        'cache_driver' => env('WEBRTC_CACHE_DRIVER', 'redis'),
        'queue_signaling_events' => env('WEBRTC_QUEUE_EVENTS', true),
        'batch_ice_candidates' => env('WEBRTC_BATCH_ICE_CANDIDATES', false),
        'compress_signaling_data' => env('WEBRTC_COMPRESS_DATA', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    |
    | Enable/disable specific WebRTC features.
    |
    */
    'features' => [
        'data_channels' => env('WEBRTC_DATA_CHANNELS', true),
        'screen_sharing' => env('WEBRTC_SCREEN_SHARING', true),
        'recording' => env('WEBRTC_RECORDING', false),
        'transcription' => env('WEBRTC_TRANSCRIPTION', false),
        'quality_adaptation' => env('WEBRTC_QUALITY_ADAPTATION', true),
    ],

];
