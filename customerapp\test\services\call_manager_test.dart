import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'dart:async';

import '../../lib/services/call_manager.dart';
import '../../lib/services/webrtc_service.dart';
import '../../lib/services/websocket_service.dart';
import '../../lib/services/api_service.dart';
import 'call_manager_test.mocks.dart';

@GenerateMocks([
  WebRTCService,
  WebSocketService,
  ApiService,
  MediaStream,
  RTCSessionDescription,
  RTCIceCandidate,
])
void main() {
  group('CallManager Integration Tests', () {
    late CallManager callManager;
    late MockWebRTCService mockWebRTCService;
    late MockWebSocketService mockWebSocketService;
    late MockApiService mockApiService;
    late StreamController<Map<String, dynamic>> messageController;

    setUp(() {
      callManager = CallManager();
      mockWebRTCService = MockWebRTCService();
      mockWebSocketService = MockWebSocketService();
      mockApiService = MockApiService();
      messageController = StreamController<Map<String, dynamic>>.broadcast();

      // Setup mock streams
      when(mockWebSocketService.messageStream).thenAnswer((_) => messageController.stream);
      when(mockWebSocketService.errorStream).thenAnswer((_) => Stream.empty());
    });

    tearDown(() {
      messageController.close();
      callManager.dispose();
    });

    group('Initialization', () {
      test('should initialize all services successfully', () async {
        when(mockApiService.initialize(baseUrl: anyNamed('baseUrl'))).thenReturn(null);
        when(mockApiService.setAuthToken(any)).thenAnswer((_) async {});
        when(mockWebRTCService.initialize()).thenAnswer((_) async {});
        when(mockWebSocketService.connect(
          serverUrl: anyNamed('serverUrl'),
          authToken: anyNamed('authToken'),
          userId: anyNamed('userId'),
        )).thenAnswer((_) async {});

        await callManager.initialize(
          signalingServerUrl: 'ws://localhost:3001',
          apiBaseUrl: 'http://localhost:8000/api',
          authToken: 'test-token',
          userId: 'user-123',
        );

        verify(mockApiService.initialize(baseUrl: 'http://localhost:8000/api')).called(1);
        verify(mockApiService.setAuthToken('test-token')).called(1);
        verify(mockWebRTCService.initialize()).called(1);
        verify(mockWebSocketService.connect(
          serverUrl: 'ws://localhost:3001',
          authToken: 'test-token',
          userId: 'user-123',
        )).called(1);
      });

      test('should handle initialization failure', () async {
        when(mockWebRTCService.initialize()).thenThrow(Exception('WebRTC init failed'));

        expect(
          () async => await callManager.initialize(
            signalingServerUrl: 'ws://localhost:3001',
            apiBaseUrl: 'http://localhost:8000/api',
            authToken: 'test-token',
            userId: 'user-123',
          ),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('Outgoing Call Flow', () {
      test('should complete outgoing call flow successfully', () async {
        // Setup mocks
        when(mockApiService.initiateCall(vendorId: anyNamed('vendorId')))
            .thenAnswer((_) async => {'call_id': 'call-123'});
        when(mockWebSocketService.joinCall(any)).thenAnswer((_) async {});
        when(mockWebRTCService.getUserMedia()).thenAnswer((_) async {});
        when(mockWebRTCService.createOffer()).thenAnswer((_) async {
          final mockOffer = MockRTCSessionDescription();
          when(mockOffer.sdp).thenReturn('test-sdp');
          when(mockOffer.type).thenReturn('offer');
          return mockOffer;
        });
        when(mockWebSocketService.sendOffer(
          callId: anyNamed('callId'),
          vendorId: anyNamed('vendorId'),
          offer: any,
        )).thenAnswer((_) async {});

        CallState? receivedState;
        callManager.onCallStateChanged = (state) {
          receivedState = state;
        };

        await callManager.startCall('vendor-456');

        expect(callManager.callState, equals(CallState.connecting));
        expect(callManager.currentCallId, equals('call-123'));
        expect(callManager.vendorId, equals('vendor-456'));

        verify(mockApiService.initiateCall(vendorId: 'vendor-456')).called(1);
        verify(mockWebSocketService.joinCall('call-123')).called(1);
        verify(mockWebRTCService.getUserMedia()).called(1);
        verify(mockWebRTCService.createOffer()).called(1);
      });

      test('should handle outgoing call failure', () async {
        when(mockApiService.initiateCall(vendorId: anyNamed('vendorId')))
            .thenThrow(Exception('API call failed'));

        String? receivedError;
        callManager.onError = (error) {
          receivedError = error;
        };

        expect(
          () async => await callManager.startCall('vendor-456'),
          throwsA(isA<Exception>()),
        );

        expect(receivedError, isNotNull);
        expect(callManager.callState, equals(CallState.error));
      });
    });

    group('Incoming Call Flow', () {
      test('should handle incoming call notification', () async {
        String? receivedCallId;
        String? receivedVendorId;
        Map<String, dynamic>? receivedVendorInfo;

        callManager.onIncomingCall = (callId, vendorId, vendorInfo) {
          receivedCallId = callId;
          receivedVendorId = vendorId;
          receivedVendorInfo = vendorInfo;
        };

        final incomingCallMessage = {
          'type': 'incoming-call',
          'data': {
            'call_id': 'call-789',
            'from_user_id': 'vendor-456',
            'vendor_info': {'name': 'Test Vendor'},
          },
        };

        messageController.add(incomingCallMessage);
        await Future.delayed(Duration(milliseconds: 10));

        expect(receivedCallId, equals('call-789'));
        expect(receivedVendorId, equals('vendor-456'));
        expect(receivedVendorInfo?['name'], equals('Test Vendor'));
        expect(callManager.callState, equals(CallState.ringing));
      });

      test('should accept incoming call successfully', () async {
        // Setup incoming call state
        callManager.onIncomingCall = (callId, vendorId, vendorInfo) {};
        
        final incomingCallMessage = {
          'type': 'incoming-call',
          'data': {
            'call_id': 'call-789',
            'from_user_id': 'vendor-456',
            'vendor_info': {'name': 'Test Vendor'},
          },
        };

        messageController.add(incomingCallMessage);
        await Future.delayed(Duration(milliseconds: 10));

        // Setup accept call mocks
        when(mockApiService.acceptCall(any)).thenAnswer((_) async => {});
        when(mockWebSocketService.joinCall(any)).thenAnswer((_) async {});

        await callManager.acceptCall();

        verify(mockApiService.acceptCall('call-789')).called(1);
        verify(mockWebSocketService.joinCall('call-789')).called(1);
      });

      test('should reject incoming call successfully', () async {
        // Setup incoming call state
        callManager.onIncomingCall = (callId, vendorId, vendorInfo) {};
        
        final incomingCallMessage = {
          'type': 'incoming-call',
          'data': {
            'call_id': 'call-789',
            'from_user_id': 'vendor-456',
            'vendor_info': {'name': 'Test Vendor'},
          },
        };

        messageController.add(incomingCallMessage);
        await Future.delayed(Duration(milliseconds: 10));

        // Setup reject call mocks
        when(mockApiService.rejectCall(any)).thenAnswer((_) async => {});

        await callManager.rejectCall();

        verify(mockApiService.rejectCall('call-789')).called(1);
        expect(callManager.callState, equals(CallState.ended));
      });
    });

    group('WebRTC Signaling', () {
      test('should handle incoming offer', () async {
        when(mockWebRTCService.getUserMedia()).thenAnswer((_) async {});
        when(mockWebRTCService.setRemoteDescription(any)).thenAnswer((_) async {});
        when(mockWebRTCService.createAnswer()).thenAnswer((_) async {
          final mockAnswer = MockRTCSessionDescription();
          when(mockAnswer.sdp).thenReturn('answer-sdp');
          when(mockAnswer.type).thenReturn('answer');
          return mockAnswer;
        });
        when(mockWebSocketService.sendAnswer(
          callId: anyNamed('callId'),
          vendorId: anyNamed('vendorId'),
          answer: any,
        )).thenAnswer((_) async {});

        final offerMessage = {
          'type': 'webrtc-offer-received',
          'data': {
            'call_id': 'call-123',
            'from_user_id': 'vendor-456',
            'offer': {
              'sdp': 'offer-sdp',
              'type': 'offer',
            },
          },
        };

        messageController.add(offerMessage);
        await Future.delayed(Duration(milliseconds: 10));

        verify(mockWebRTCService.getUserMedia()).called(1);
        verify(mockWebRTCService.setRemoteDescription(any)).called(1);
        verify(mockWebRTCService.createAnswer()).called(1);
      });

      test('should handle incoming answer', () async {
        when(mockWebRTCService.setRemoteDescription(any)).thenAnswer((_) async {});

        final answerMessage = {
          'type': 'webrtc-answer-received',
          'data': {
            'answer': {
              'sdp': 'answer-sdp',
              'type': 'answer',
            },
          },
        };

        messageController.add(answerMessage);
        await Future.delayed(Duration(milliseconds: 10));

        verify(mockWebRTCService.setRemoteDescription(any)).called(1);
      });

      test('should handle incoming ICE candidate', () async {
        when(mockWebRTCService.addIceCandidate(any)).thenAnswer((_) async {});

        final candidateMessage = {
          'type': 'ice-candidate-received',
          'data': {
            'candidate': {
              'candidate': 'candidate:1 1 UDP 2130706431 ************* 54400 typ host',
              'sdpMid': '0',
              'sdpMLineIndex': 0,
            },
          },
        };

        messageController.add(candidateMessage);
        await Future.delayed(Duration(milliseconds: 10));

        verify(mockWebRTCService.addIceCandidate(any)).called(1);
      });
    });

    group('Call State Management', () {
      test('should transition call states correctly', () async {
        final stateChanges = <CallState>[];
        callManager.onCallStateChanged = (state) {
          stateChanges.add(state);
        };

        // Simulate state transitions
        callManager.onCallStateChanged!(CallState.initiating);
        callManager.onCallStateChanged!(CallState.connecting);
        callManager.onCallStateChanged!(CallState.connected);
        callManager.onCallStateChanged!(CallState.ended);

        expect(stateChanges, equals([
          CallState.initiating,
          CallState.connecting,
          CallState.connected,
          CallState.ended,
        ]));
      });

      test('should handle connection state changes from WebRTC', () async {
        CallState? receivedState;
        callManager.onCallStateChanged = (state) {
          receivedState = state;
        };

        // Simulate WebRTC connection state changes
        when(mockWebRTCService.onConnectionStateChange).thenReturn(null);

        // This would typically be called by WebRTC service
        // callManager would listen to these changes and update its state

        expect(receivedState, isNull); // No state change yet
      });
    });

    group('Media Controls', () {
      test('should toggle video successfully', () async {
        when(mockWebRTCService.toggleVideo()).thenAnswer((_) async => false);

        final result = await callManager.toggleVideo();

        expect(result, isFalse);
        verify(mockWebRTCService.toggleVideo()).called(1);
      });

      test('should toggle audio successfully', () async {
        when(mockWebRTCService.toggleAudio()).thenAnswer((_) async => false);

        final result = await callManager.toggleAudio();

        expect(result, isFalse);
        verify(mockWebRTCService.toggleAudio()).called(1);
      });

      test('should switch camera successfully', () async {
        when(mockWebRTCService.switchCamera()).thenAnswer((_) async => {});

        await callManager.switchCamera();

        verify(mockWebRTCService.switchCamera()).called(1);
      });
    });

    group('Call Termination', () {
      test('should end call successfully', () async {
        // Setup active call
        when(mockApiService.initiateCall(vendorId: anyNamed('vendorId')))
            .thenAnswer((_) async => {'call_id': 'call-123'});

        await callManager.startCall('vendor-456');

        // Setup end call mocks
        when(mockApiService.endCall(any)).thenAnswer((_) async => {});
        when(mockWebSocketService.leaveCall(any)).thenAnswer((_) async => {});
        when(mockWebRTCService.dispose()).thenAnswer((_) async => {});

        await callManager.endCall();

        verify(mockApiService.endCall('call-123')).called(1);
        verify(mockWebSocketService.leaveCall('call-123')).called(1);
        expect(callManager.callState, equals(CallState.ended));
      });

      test('should handle user left event', () async {
        final userLeftMessage = {
          'type': 'user-left',
          'data': {
            'user_id': 'vendor-456',
          },
        };

        // Setup call with vendor
        callManager.onIncomingCall = (callId, vendorId, vendorInfo) {};
        final incomingCallMessage = {
          'type': 'incoming-call',
          'data': {
            'call_id': 'call-789',
            'from_user_id': 'vendor-456',
            'vendor_info': {'name': 'Test Vendor'},
          },
        };

        messageController.add(incomingCallMessage);
        await Future.delayed(Duration(milliseconds: 10));

        // Vendor leaves
        messageController.add(userLeftMessage);
        await Future.delayed(Duration(milliseconds: 10));

        expect(callManager.callState, equals(CallState.ended));
      });
    });

    group('Error Scenarios', () {
      test('should handle WebRTC errors', () async {
        String? receivedError;
        callManager.onError = (error) {
          receivedError = error;
        };

        // Simulate WebRTC error
        when(mockWebRTCService.onError).thenReturn(null);

        // This would typically be called by WebRTC service
        callManager.onError!('WebRTC connection failed');

        expect(receivedError, equals('WebRTC connection failed'));
      });

      test('should handle WebSocket errors', () async {
        String? receivedError;
        callManager.onError = (error) {
          receivedError = error;
        };

        // Simulate WebSocket error through error stream
        final errorController = StreamController<String>();
        when(mockWebSocketService.errorStream).thenAnswer((_) => errorController.stream);

        errorController.add('WebSocket connection lost');
        await Future.delayed(Duration(milliseconds: 10));

        expect(receivedError, contains('WebSocket'));
        errorController.close();
      });

      test('should handle API errors', () async {
        when(mockApiService.initiateCall(vendorId: anyNamed('vendorId')))
            .thenThrow(Exception('Network error'));

        String? receivedError;
        callManager.onError = (error) {
          receivedError = error;
        };

        expect(
          () async => await callManager.startCall('vendor-456'),
          throwsA(isA<Exception>()),
        );

        expect(receivedError, isNotNull);
      });
    });

    group('Network Loss During Call', () {
      test('should handle network loss during active call', () async {
        // Setup active call
        when(mockApiService.initiateCall(vendorId: anyNamed('vendorId')))
            .thenAnswer((_) async => {'call_id': 'call-123'});

        await callManager.startCall('vendor-456');

        String? receivedError;
        callManager.onError = (error) {
          receivedError = error;
        };

        // Simulate network loss
        final errorController = StreamController<String>();
        when(mockWebSocketService.errorStream).thenAnswer((_) => errorController.stream);

        errorController.add('Network disconnected');
        await Future.delayed(Duration(milliseconds: 10));

        expect(receivedError, contains('Network'));
        errorController.close();
      });

      test('should attempt to reconnect after network recovery', () async {
        // This would test the reconnection logic
        // Implementation depends on how reconnection is handled
        expect(true, isTrue); // Placeholder
      });
    });
  });
}
