import 'package:flutter/material.dart';
import '../services/call_manager.dart';

class CallStatusIndicator extends StatefulWidget {
  final CallState callState;
  final String vendorName;
  final VoidCallback? onTap;

  const CallStatusIndicator({
    Key? key,
    required this.callState,
    required this.vendorName,
    this.onTap,
  }) : super(key: key);

  @override
  State<CallStatusIndicator> createState() => _CallStatusIndicatorState();
}

class _CallStatusIndicatorState extends State<CallStatusIndicator>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: -100.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Positioned(
          top: _slideAnimation.value,
          left: 0,
          right: 0,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: _buildStatusBar(),
          ),
        );
      },
    );
  }

  Widget _buildStatusBar() {
    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: _getStatusColor(),
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // Status icon
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getStatusIcon(),
                  color: Colors.white,
                  size: 18,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // Status text
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getStatusTitle(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      widget.vendorName,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Action indicator
              if (widget.onTap != null)
                const Icon(
                  Icons.touch_app,
                  color: Colors.white70,
                  size: 16,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (widget.callState) {
      case CallState.initiating:
        return Colors.blue;
      case CallState.ringing:
        return Colors.orange;
      case CallState.connecting:
        return Colors.amber;
      case CallState.connected:
        return Colors.green;
      case CallState.ended:
        return Colors.grey;
      case CallState.error:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon() {
    switch (widget.callState) {
      case CallState.initiating:
        return Icons.phone_callback;
      case CallState.ringing:
        return Icons.phone_in_talk;
      case CallState.connecting:
        return Icons.sync;
      case CallState.connected:
        return Icons.videocam;
      case CallState.ended:
        return Icons.call_end;
      case CallState.error:
        return Icons.error;
      default:
        return Icons.phone;
    }
  }

  String _getStatusTitle() {
    switch (widget.callState) {
      case CallState.initiating:
        return 'Calling...';
      case CallState.ringing:
        return 'Incoming call';
      case CallState.connecting:
        return 'Connecting...';
      case CallState.connected:
        return 'Video call active';
      case CallState.ended:
        return 'Call ended';
      case CallState.error:
        return 'Call failed';
      default:
        return 'Unknown status';
    }
  }
}
