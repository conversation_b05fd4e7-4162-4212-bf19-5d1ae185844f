import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';

import '../../lib/services/webrtc_service.dart';
import 'webrtc_service_test.mocks.dart';

@GenerateMocks([
  RTCPeerConnection,
  MediaStream,
  RTCVideoRenderer,
  RTCSessionDescription,
  RTCIceCandidate,
])
void main() {
  group('WebRTCService Tests', () {
    late WebRTCService webrtcService;
    late MockRTCPeerConnection mockPeerConnection;
    late MockMediaStream mockLocalStream;
    setUp(() {
      webrtcService = WebRTCService();
      mockPeerConnection = MockRTCPeerConnection();
      mockLocalStream = MockMediaStream();
    });

    tearDown(() {
      webrtcService.dispose();
    });

    group('Initialization', () {
      test('should initialize successfully', () async {
        // Mock the peer connection creation
        when(mockPeerConnection.getConfiguration).thenReturn({});

        await webrtcService.initialize();

        expect(webrtcService.isInitialized, isTrue);
      });

      test('should handle initialization failure', () async {
        // Test initialization failure scenario
        expect(() async => await webrtcService.initialize(), 
               throwsA(isA<Exception>()));
      });
    });

    group('User Media', () {
      test('should get user media successfully', () async {
        // Mock getUserMedia
        when(mockLocalStream.getVideoTracks()).thenReturn([]);
        when(mockLocalStream.getAudioTracks()).thenReturn([]);
        
        await webrtcService.initialize();
        await webrtcService.getUserMedia();
        
        expect(webrtcService.localStream, isNotNull);
        expect(webrtcService.isVideoEnabled, isTrue);
        expect(webrtcService.isAudioEnabled, isTrue);
      });

      test('should handle getUserMedia failure', () async {
        await webrtcService.initialize();
        
        // Test getUserMedia failure
        expect(() async => await webrtcService.getUserMedia(), 
               throwsA(isA<Exception>()));
      });

      test('should get user media with video only', () async {
        await webrtcService.initialize();
        await webrtcService.getUserMedia(video: true, audio: false);

        // Verify media stream is obtained
        expect(webrtcService.localStream, isNotNull);
      });
    });

    group('Offer/Answer Creation', () {
      test('should create offer successfully', () async {
        final mockOffer = MockRTCSessionDescription();
        when(mockOffer.sdp).thenReturn('mock-offer-sdp');
        when(mockOffer.type).thenReturn('offer');
        
        when(mockPeerConnection.createOffer()).thenAnswer((_) async => mockOffer);
        when(mockPeerConnection.setLocalDescription(any)).thenAnswer((_) async => {});
        
        await webrtcService.initialize();
        final offer = await webrtcService.createOffer();
        
        expect(offer, isNotNull);
        expect(offer.type, equals('offer'));
        verify(mockPeerConnection.createOffer()).called(1);
        verify(mockPeerConnection.setLocalDescription(any)).called(1);
      });

      test('should create answer successfully', () async {
        final mockAnswer = MockRTCSessionDescription();
        when(mockAnswer.sdp).thenReturn('mock-answer-sdp');
        when(mockAnswer.type).thenReturn('answer');
        
        when(mockPeerConnection.createAnswer()).thenAnswer((_) async => mockAnswer);
        when(mockPeerConnection.setLocalDescription(any)).thenAnswer((_) async => {});
        
        await webrtcService.initialize();
        final answer = await webrtcService.createAnswer();
        
        expect(answer, isNotNull);
        expect(answer.type, equals('answer'));
        verify(mockPeerConnection.createAnswer()).called(1);
        verify(mockPeerConnection.setLocalDescription(any)).called(1);
      });

      test('should handle offer creation failure', () async {
        when(mockPeerConnection.createOffer()).thenThrow(Exception('Failed to create offer'));
        
        await webrtcService.initialize();
        
        expect(() async => await webrtcService.createOffer(), 
               throwsA(isA<Exception>()));
      });
    });

    group('Remote Description', () {
      test('should set remote description successfully', () async {
        final mockRemoteDesc = MockRTCSessionDescription();
        when(mockRemoteDesc.sdp).thenReturn('remote-sdp');
        when(mockRemoteDesc.type).thenReturn('offer');
        
        when(mockPeerConnection.setRemoteDescription(any)).thenAnswer((_) async => {});
        
        await webrtcService.initialize();
        await webrtcService.setRemoteDescription(mockRemoteDesc);
        
        verify(mockPeerConnection.setRemoteDescription(mockRemoteDesc)).called(1);
      });

      test('should handle set remote description failure', () async {
        final mockRemoteDesc = MockRTCSessionDescription();
        when(mockPeerConnection.setRemoteDescription(any))
            .thenThrow(Exception('Failed to set remote description'));
        
        await webrtcService.initialize();
        
        expect(() async => await webrtcService.setRemoteDescription(mockRemoteDesc), 
               throwsA(isA<Exception>()));
      });
    });

    group('ICE Candidates', () {
      test('should add ICE candidate successfully', () async {
        final mockCandidate = MockRTCIceCandidate();
        when(mockCandidate.candidate).thenReturn('candidate:1 1 UDP 2130706431 ************* 54400 typ host');
        when(mockCandidate.sdpMid).thenReturn('0');
        when(mockCandidate.sdpMLineIndex).thenReturn(0);
        
        when(mockPeerConnection.addCandidate(any)).thenAnswer((_) async => {});
        
        await webrtcService.initialize();
        await webrtcService.addIceCandidate(mockCandidate);
        
        verify(mockPeerConnection.addCandidate(mockCandidate)).called(1);
      });

      test('should handle add ICE candidate failure gracefully', () async {
        final mockCandidate = MockRTCIceCandidate();
        when(mockPeerConnection.addCandidate(any))
            .thenThrow(Exception('Failed to add ICE candidate'));
        
        await webrtcService.initialize();
        
        // Should not throw exception, just log error
        await webrtcService.addIceCandidate(mockCandidate);
        
        verify(mockPeerConnection.addCandidate(mockCandidate)).called(1);
      });
    });

    group('Media Controls', () {
      test('should toggle video successfully', () async {
        await webrtcService.initialize();
        await webrtcService.getUserMedia();
        
        // Initially video should be enabled
        expect(webrtcService.isVideoEnabled, isTrue);
        
        // Toggle video off
        final result1 = await webrtcService.toggleVideo();
        expect(result1, isFalse);
        expect(webrtcService.isVideoEnabled, isFalse);
        
        // Toggle video on
        final result2 = await webrtcService.toggleVideo();
        expect(result2, isTrue);
        expect(webrtcService.isVideoEnabled, isTrue);
      });

      test('should toggle audio successfully', () async {
        await webrtcService.initialize();
        await webrtcService.getUserMedia();
        
        // Initially audio should be enabled
        expect(webrtcService.isAudioEnabled, isTrue);
        
        // Toggle audio off
        final result1 = await webrtcService.toggleAudio();
        expect(result1, isFalse);
        expect(webrtcService.isAudioEnabled, isFalse);
        
        // Toggle audio on
        final result2 = await webrtcService.toggleAudio();
        expect(result2, isTrue);
        expect(webrtcService.isAudioEnabled, isTrue);
      });

      test('should switch camera successfully', () async {
        await webrtcService.initialize();
        await webrtcService.getUserMedia();
        
        // Should not throw exception
        await webrtcService.switchCamera();
      });

      test('should handle media control failures gracefully', () async {
        await webrtcService.initialize();
        
        // Try to toggle without user media
        final videoResult = await webrtcService.toggleVideo();
        final audioResult = await webrtcService.toggleAudio();
        
        expect(videoResult, isFalse);
        expect(audioResult, isFalse);
      });
    });

    group('Connection State', () {
      test('should handle connection state changes', () async {
        RTCPeerConnectionState? receivedState;
        
        webrtcService.onConnectionStateChange = (state) {
          receivedState = state;
        };
        
        await webrtcService.initialize();
        
        // Simulate connection state change
        // Note: This would typically be triggered by the peer connection
        // In a real test, you'd mock the peer connection callbacks
        
        expect(receivedState, isNull); // No state change yet
      });

      test('should handle ICE candidate events', () async {
        RTCIceCandidate? receivedCandidate;
        
        webrtcService.onIceCandidate = (candidate) {
          receivedCandidate = candidate;
        };
        
        await webrtcService.initialize();
        
        // Simulate ICE candidate event
        // Note: This would typically be triggered by the peer connection
        
        expect(receivedCandidate, isNull); // No candidate yet
      });
    });

    group('Error Handling', () {
      test('should handle peer connection errors', () async {
        String? receivedError;

        webrtcService.onError = (error) {
          receivedError = error;
        };

        await webrtcService.initialize();

        // Simulate error
        // Note: This would typically be triggered by the peer connection

        expect(receivedError, isNull); // No error yet
      });

      test('should handle stream errors gracefully', () async {
        await webrtcService.initialize();

        // Test various error scenarios
        expect(() async => await webrtcService.createOffer(),
               throwsA(isA<Exception>()));
      });
    });

    group('Cleanup', () {
      test('should dispose resources properly', () async {
        await webrtcService.initialize();
        await webrtcService.getUserMedia();

        await webrtcService.dispose();

        expect(webrtcService.localStream, isNull);
        expect(webrtcService.remoteStream, isNull);
      });

      test('should handle multiple dispose calls', () async {
        await webrtcService.initialize();

        await webrtcService.dispose();
        await webrtcService.dispose(); // Should not throw
      });
    });

    group('Edge Cases', () {
      test('should handle operations before initialization', () async {
        // Try operations before initialization
        expect(() async => await webrtcService.getUserMedia(),
               throwsA(isA<Exception>()));
        expect(() async => await webrtcService.createOffer(),
               throwsA(isA<Exception>()));
      });

      test('should handle operations after disposal', () async {
        await webrtcService.initialize();
        await webrtcService.dispose();

        // Try operations after disposal
        expect(() async => await webrtcService.getUserMedia(),
               throwsA(isA<Exception>()));
      });

      test('should handle null callbacks gracefully', () async {
        await webrtcService.initialize();

        // Set callbacks to null
        webrtcService.onLocalStream = null;
        webrtcService.onRemoteStream = null;
        webrtcService.onConnectionStateChange = null;
        webrtcService.onIceCandidate = null;
        webrtcService.onError = null;

        // Should not throw when callbacks are triggered
        await webrtcService.getUserMedia();
      });
    });
  });
}
