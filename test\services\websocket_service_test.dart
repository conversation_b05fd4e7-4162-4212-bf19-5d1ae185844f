import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'dart:async';

import '../../lib/services/websocket_service.dart';
import 'websocket_service_test.mocks.dart';

@GenerateMocks([IO.Socket, RTCSessionDescription, RTCIceCandidate])
void main() {
  group('WebSocketService Tests', () {
    late WebSocketService webSocketService;
    late MockSocket mockSocket;
    late StreamController<Map<String, dynamic>> messageController;
    late StreamController<String> errorController;

    setUp(() {
      webSocketService = WebSocketService();
      mockSocket = MockSocket();
      messageController = StreamController<Map<String, dynamic>>.broadcast();
      errorController = StreamController<String>.broadcast();
      
      // Setup mock socket streams
      when(mockSocket.connected).thenReturn(false);
      when(mockSocket.disconnected).thenReturn(true);
    });

    tearDown(() {
      messageController.close();
      errorController.close();
      webSocketService.disconnect();
    });

    group('Connection Management', () {
      test('should connect successfully', () async {
        when(mockSocket.connect()).thenReturn(mockSocket);
        when(mockSocket.connected).thenReturn(true);
        when(mockSocket.disconnected).thenReturn(false);
        
        await webSocketService.connect(
          serverUrl: 'ws://localhost:3001',
          authToken: 'test-token',
          userId: 'user-123',
        );
        
        expect(webSocketService.isConnected, isTrue);
        verify(mockSocket.connect()).called(1);
      });

      test('should handle connection failure', () async {
        when(mockSocket.connect()).thenThrow(Exception('Connection failed'));
        
        expect(
          () async => await webSocketService.connect(
            serverUrl: 'ws://localhost:3001',
            authToken: 'test-token',
            userId: 'user-123',
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('should disconnect successfully', () async {
        when(mockSocket.disconnect()).thenReturn(mockSocket);
        when(mockSocket.connected).thenReturn(false);
        when(mockSocket.disconnected).thenReturn(true);
        
        await webSocketService.disconnect();
        
        expect(webSocketService.isConnected, isFalse);
      });

      test('should handle reconnection', () async {
        when(mockSocket.connect()).thenReturn(mockSocket);
        when(mockSocket.connected).thenReturn(true);
        
        await webSocketService.connect(
          serverUrl: 'ws://localhost:3001',
          authToken: 'test-token',
          userId: 'user-123',
        );
        
        // Simulate disconnection
        when(mockSocket.connected).thenReturn(false);
        when(mockSocket.disconnected).thenReturn(true);
        
        // Reconnect
        when(mockSocket.connected).thenReturn(true);
        when(mockSocket.disconnected).thenReturn(false);
        
        await webSocketService.connect(
          serverUrl: 'ws://localhost:3001',
          authToken: 'test-token',
          userId: 'user-123',
        );
        
        expect(webSocketService.isConnected, isTrue);
      });
    });

    group('Message Handling', () {
      test('should receive and parse messages correctly', () async {
        final testMessage = {
          'type': 'webrtc-offer-received',
          'data': {
            'call_id': 'call-123',
            'from_user_id': 'user-456',
            'offer': {
              'sdp': 'test-sdp',
              'type': 'offer',
            },
          },
        };
        
        Map<String, dynamic>? receivedMessage;
        webSocketService.messageStream.listen((message) {
          receivedMessage = message;
        });
        
        // Simulate receiving message
        messageController.add(testMessage);
        
        await Future.delayed(Duration(milliseconds: 10));
        
        expect(receivedMessage, equals(testMessage));
      });

      test('should handle malformed messages gracefully', () async {
        final malformedMessage = {
          'invalid': 'structure',
        };
        
        String? receivedError;
        webSocketService.errorStream.listen((error) {
          receivedError = error;
        });
        
        // Simulate receiving malformed message
        messageController.add(malformedMessage);
        
        await Future.delayed(Duration(milliseconds: 10));
        
        expect(receivedError, isNotNull);
      });

      test('should filter messages by call_id', () async {
        final message1 = {
          'type': 'webrtc-offer-received',
          'data': {'call_id': 'call-123'},
        };
        
        final message2 = {
          'type': 'webrtc-offer-received',
          'data': {'call_id': 'call-456'},
        };
        
        final receivedMessages = <Map<String, dynamic>>[];
        webSocketService.messageStream.listen((message) {
          receivedMessages.add(message);
        });
        
        messageController.add(message1);
        messageController.add(message2);
        
        await Future.delayed(Duration(milliseconds: 10));
        
        expect(receivedMessages, hasLength(2));
        expect(receivedMessages[0]['data']['call_id'], equals('call-123'));
        expect(receivedMessages[1]['data']['call_id'], equals('call-456'));
      });
    });

    group('Call Management', () {
      test('should join call successfully', () async {
        when(mockSocket.emit(any, any)).thenReturn(mockSocket);
        
        await webSocketService.joinCall('call-123');
        
        verify(mockSocket.emit('join-call', {'call_id': 'call-123'})).called(1);
      });

      test('should leave call successfully', () async {
        when(mockSocket.emit(any, any)).thenReturn(mockSocket);
        
        await webSocketService.leaveCall('call-123');
        
        verify(mockSocket.emit('leave-call', {'call_id': 'call-123'})).called(1);
      });

      test('should handle join call failure', () async {
        when(mockSocket.emit(any, any)).thenThrow(Exception('Emit failed'));
        
        expect(
          () async => await webSocketService.joinCall('call-123'),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('WebRTC Signaling', () {
      test('should send offer successfully', () async {
        final mockOffer = MockRTCSessionDescription();
        when(mockOffer.sdp).thenReturn('test-offer-sdp');
        when(mockOffer.type).thenReturn('offer');
        when(mockSocket.emit(any, any)).thenReturn(mockSocket);
        
        await webSocketService.sendOffer(
          callId: 'call-123',
          vendorId: 'vendor-456',
          offer: mockOffer,
        );
        
        verify(mockSocket.emit('webrtc-offer', {
          'call_id': 'call-123',
          'to_user_id': 'vendor-456',
          'offer': {
            'sdp': 'test-offer-sdp',
            'type': 'offer',
          },
        })).called(1);
      });

      test('should send answer successfully', () async {
        final mockAnswer = MockRTCSessionDescription();
        when(mockAnswer.sdp).thenReturn('test-answer-sdp');
        when(mockAnswer.type).thenReturn('answer');
        when(mockSocket.emit(any, any)).thenReturn(mockSocket);
        
        await webSocketService.sendAnswer(
          callId: 'call-123',
          vendorId: 'vendor-456',
          answer: mockAnswer,
        );
        
        verify(mockSocket.emit('webrtc-answer', {
          'call_id': 'call-123',
          'to_user_id': 'vendor-456',
          'answer': {
            'sdp': 'test-answer-sdp',
            'type': 'answer',
          },
        })).called(1);
      });

      test('should send ICE candidate successfully', () async {
        final mockCandidate = MockRTCIceCandidate();
        when(mockCandidate.candidate).thenReturn('candidate:1 1 UDP 2130706431 ************* 54400 typ host');
        when(mockCandidate.sdpMid).thenReturn('0');
        when(mockCandidate.sdpMLineIndex).thenReturn(0);
        when(mockSocket.emit(any, any)).thenReturn(mockSocket);
        
        await webSocketService.sendIceCandidate(
          callId: 'call-123',
          vendorId: 'vendor-456',
          candidate: mockCandidate,
        );
        
        verify(mockSocket.emit('ice-candidate', {
          'call_id': 'call-123',
          'to_user_id': 'vendor-456',
          'candidate': {
            'candidate': 'candidate:1 1 UDP 2130706431 ************* 54400 typ host',
            'sdpMid': '0',
            'sdpMLineIndex': 0,
          },
        })).called(1);
      });

      test('should handle signaling failures gracefully', () async {
        final mockOffer = MockRTCSessionDescription();
        when(mockOffer.sdp).thenReturn('test-sdp');
        when(mockOffer.type).thenReturn('offer');
        when(mockSocket.emit(any, any)).thenThrow(Exception('Emit failed'));
        
        expect(
          () async => await webSocketService.sendOffer(
            callId: 'call-123',
            vendorId: 'vendor-456',
            offer: mockOffer,
          ),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('Error Handling', () {
      test('should handle connection errors', () async {
        String? receivedError;
        webSocketService.errorStream.listen((error) {
          receivedError = error;
        });
        
        // Simulate connection error
        errorController.add('Connection timeout');
        
        await Future.delayed(Duration(milliseconds: 10));
        
        expect(receivedError, equals('Connection timeout'));
      });

      test('should handle socket errors', () async {
        String? receivedError;
        webSocketService.errorStream.listen((error) {
          receivedError = error;
        });
        
        // Simulate socket error
        errorController.add('Socket error: Connection refused');
        
        await Future.delayed(Duration(milliseconds: 10));
        
        expect(receivedError, contains('Socket error'));
      });

      test('should retry connection on failure', () async {
        var connectionAttempts = 0;
        when(mockSocket.connect()).thenAnswer((_) {
          connectionAttempts++;
          if (connectionAttempts < 3) {
            throw Exception('Connection failed');
          }
          when(mockSocket.connected).thenReturn(true);
          return mockSocket;
        });
        
        await webSocketService.connect(
          serverUrl: 'ws://localhost:3001',
          authToken: 'test-token',
          userId: 'user-123',
        );
        
        expect(connectionAttempts, equals(3));
        expect(webSocketService.isConnected, isTrue);
      });
    });

    group('Network Loss Scenarios', () {
      test('should handle network disconnection', () async {
        // Setup connected state
        when(mockSocket.connected).thenReturn(true);
        when(mockSocket.disconnected).thenReturn(false);
        
        await webSocketService.connect(
          serverUrl: 'ws://localhost:3001',
          authToken: 'test-token',
          userId: 'user-123',
        );
        
        expect(webSocketService.isConnected, isTrue);
        
        // Simulate network loss
        when(mockSocket.connected).thenReturn(false);
        when(mockSocket.disconnected).thenReturn(true);
        
        // Trigger disconnection event
        errorController.add('Network disconnected');
        
        await Future.delayed(Duration(milliseconds: 10));
        
        expect(webSocketService.isConnected, isFalse);
      });

      test('should attempt reconnection after network loss', () async {
        var reconnectionAttempts = 0;
        
        when(mockSocket.connect()).thenAnswer((_) {
          reconnectionAttempts++;
          if (reconnectionAttempts <= 2) {
            throw Exception('Network unavailable');
          }
          when(mockSocket.connected).thenReturn(true);
          return mockSocket;
        });
        
        // Simulate reconnection attempts
        try {
          await webSocketService.connect(
            serverUrl: 'ws://localhost:3001',
            authToken: 'test-token',
            userId: 'user-123',
          );
        } catch (e) {
          // Expected to fail initially
        }
        
        // Eventually succeeds
        await webSocketService.connect(
          serverUrl: 'ws://localhost:3001',
          authToken: 'test-token',
          userId: 'user-123',
        );
        
        expect(reconnectionAttempts, greaterThan(1));
        expect(webSocketService.isConnected, isTrue);
      });

      test('should queue messages during disconnection', () async {
        final mockOffer = MockRTCSessionDescription();
        when(mockOffer.sdp).thenReturn('test-sdp');
        when(mockOffer.type).thenReturn('offer');
        
        // Start disconnected
        when(mockSocket.connected).thenReturn(false);
        
        // Try to send message while disconnected
        expect(
          () async => await webSocketService.sendOffer(
            callId: 'call-123',
            vendorId: 'vendor-456',
            offer: mockOffer,
          ),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('Authentication', () {
      test('should include auth token in connection', () async {
        when(mockSocket.connect()).thenReturn(mockSocket);
        when(mockSocket.connected).thenReturn(true);
        
        await webSocketService.connect(
          serverUrl: 'ws://localhost:3001',
          authToken: 'test-auth-token',
          userId: 'user-123',
        );
        
        // Verify auth token is used in connection
        expect(webSocketService.isConnected, isTrue);
      });

      test('should handle authentication failure', () async {
        when(mockSocket.connect()).thenThrow(Exception('Authentication failed'));
        
        expect(
          () async => await webSocketService.connect(
            serverUrl: 'ws://localhost:3001',
            authToken: 'invalid-token',
            userId: 'user-123',
          ),
          throwsA(isA<Exception>()),
        );
      });
    });
  });
}
