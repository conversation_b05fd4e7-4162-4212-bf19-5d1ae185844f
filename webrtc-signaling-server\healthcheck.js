const http = require('http');

/**
 * Health check script for Docker container
 * Checks if the server is responding and healthy
 */

const options = {
  hostname: 'localhost',
  port: process.env.PORT || 3001,
  path: '/health',
  method: 'GET',
  timeout: 3000
};

const healthCheck = http.request(options, (res) => {
  console.log(`Health check status: ${res.statusCode}`);
  
  if (res.statusCode === 200) {
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const healthData = JSON.parse(data);
        
        // Check if server is healthy
        if (healthData.status === 'healthy') {
          console.log('Health check passed');
          process.exit(0);
        } else {
          console.error('Health check failed - server not healthy');
          process.exit(1);
        }
      } catch (error) {
        console.error('Health check failed - invalid response:', error.message);
        process.exit(1);
      }
    });
  } else {
    console.error(`Health check failed - HTTP ${res.statusCode}`);
    process.exit(1);
  }
});

healthCheck.on('error', (error) => {
  console.error('Health check failed - request error:', error.message);
  process.exit(1);
});

healthCheck.on('timeout', () => {
  console.error('Health check failed - timeout');
  healthCheck.destroy();
  process.exit(1);
});

healthCheck.end();
