<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\VideoCall;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Cache;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class WebRTCSignalingTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $caller;
    protected $receiver;
    protected $videoCall;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->caller = User::factory()->create(['is_active' => true]);
        $this->receiver = User::factory()->create(['is_active' => true]);

        // Create test video call
        $this->videoCall = VideoCall::factory()->create([
            'caller_id' => $this->caller->id,
            'receiver_id' => $this->receiver->id,
            'status' => VideoCall::STATUS_PENDING
        ]);
    }

    public function test_caller_can_store_offer()
    {
        Sanctum::actingAs($this->caller);

        $offerData = [
            'type' => 'offer',
            'sdp' => 'v=0\r\no=- 123456789 2 IN IP4 127.0.0.1\r\n...'
        ];

        $response = $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/offer", $offerData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Offer stored successfully'
                ]);

        // Check database
        $this->videoCall->refresh();
        $this->assertNotNull($this->videoCall->webrtc_data);
        $this->assertEquals('offer', $this->videoCall->webrtc_data['offer']['type']);
        $this->assertEquals($offerData['sdp'], $this->videoCall->webrtc_data['offer']['sdp']);
    }

    public function test_receiver_cannot_store_offer()
    {
        Sanctum::actingAs($this->receiver);

        $offerData = [
            'type' => 'offer',
            'sdp' => 'v=0\r\no=- 123456789 2 IN IP4 127.0.0.1\r\n...'
        ];

        $response = $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/offer", $offerData);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Only the caller can store the offer'
                ]);
    }

    public function test_receiver_can_store_answer()
    {
        Sanctum::actingAs($this->receiver);

        $answerData = [
            'type' => 'answer',
            'sdp' => 'v=0\r\no=- 987654321 2 IN IP4 127.0.0.1\r\n...'
        ];

        $response = $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/answer", $answerData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Answer stored successfully'
                ]);

        // Check database
        $this->videoCall->refresh();
        $this->assertNotNull($this->videoCall->webrtc_data);
        $this->assertEquals('answer', $this->videoCall->webrtc_data['answer']['type']);
        $this->assertEquals($answerData['sdp'], $this->videoCall->webrtc_data['answer']['sdp']);
    }

    public function test_caller_cannot_store_answer()
    {
        Sanctum::actingAs($this->caller);

        $answerData = [
            'type' => 'answer',
            'sdp' => 'v=0\r\no=- 987654321 2 IN IP4 127.0.0.1\r\n...'
        ];

        $response = $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/answer", $answerData);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Only the receiver can store the answer'
                ]);
    }

    public function test_both_participants_can_store_ice_candidates()
    {
        $iceCandidateData = [
            'candidate' => 'candidate:1 1 UDP 2130706431 ************* 54400 typ host',
            'sdpMid' => '0',
            'sdpMLineIndex' => 0
        ];

        // Test caller storing ICE candidate
        Sanctum::actingAs($this->caller);
        $response = $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/ice-candidate", $iceCandidateData);
        $response->assertStatus(200);

        // Test receiver storing ICE candidate
        Sanctum::actingAs($this->receiver);
        $response = $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/ice-candidate", $iceCandidateData);
        $response->assertStatus(200);

        // Check cache
        $cacheKey = "ice_candidates:{$this->videoCall->call_id}";
        $iceCandidates = Cache::get($cacheKey, []);
        $this->assertCount(2, $iceCandidates);
    }

    public function test_non_participant_cannot_store_ice_candidate()
    {
        $otherUser = User::factory()->create();
        Sanctum::actingAs($otherUser);

        $iceCandidateData = [
            'candidate' => 'candidate:1 1 UDP 2130706431 ************* 54400 typ host',
            'sdpMid' => '0',
            'sdpMLineIndex' => 0
        ];

        $response = $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/ice-candidate", $iceCandidateData);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'You are not a participant in this call'
                ]);
    }

    public function test_get_signaling_data()
    {
        // Store some signaling data first
        $webrtcData = [
            'offer' => [
                'type' => 'offer',
                'sdp' => 'v=0\r\no=- 123456789 2 IN IP4 127.0.0.1\r\n...',
                'timestamp' => now()->toISOString()
            ],
            'answer' => [
                'type' => 'answer',
                'sdp' => 'v=0\r\no=- 987654321 2 IN IP4 127.0.0.1\r\n...',
                'timestamp' => now()->toISOString()
            ]
        ];

        $this->videoCall->update(['webrtc_data' => $webrtcData]);

        // Store ICE candidates in cache
        $iceCandidates = [
            [
                'candidate' => 'candidate:1 1 UDP 2130706431 ************* 54400 typ host',
                'sdpMid' => '0',
                'sdpMLineIndex' => 0,
                'from_user_id' => $this->caller->id,
                'timestamp' => now()->toISOString()
            ]
        ];
        Cache::put("ice_candidates:{$this->videoCall->call_id}", $iceCandidates, now()->addMinutes(5));

        Sanctum::actingAs($this->receiver);

        $response = $this->getJson("/api/calls/{$this->videoCall->call_id}/signaling");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'call_id',
                        'offer',
                        'answer',
                        'ice_candidates',
                        'last_updated',
                        'retrieved_at'
                    ]
                ]);

        $data = $response->json('data');
        $this->assertEquals($this->videoCall->call_id, $data['call_id']);
        $this->assertEquals('offer', $data['offer']['type']);
        $this->assertEquals('answer', $data['answer']['type']);
        $this->assertCount(1, $data['ice_candidates']);
    }

    public function test_ice_candidates_filtered_by_user()
    {
        // Store ICE candidates from both users
        $iceCandidates = [
            [
                'candidate' => 'candidate:1 1 UDP 2130706431 ************* 54400 typ host',
                'from_user_id' => $this->caller->id,
                'timestamp' => now()->toISOString()
            ],
            [
                'candidate' => 'candidate:2 1 UDP 2130706431 ************* 54401 typ host',
                'from_user_id' => $this->receiver->id,
                'timestamp' => now()->toISOString()
            ]
        ];
        Cache::put("ice_candidates:{$this->videoCall->call_id}", $iceCandidates, now()->addMinutes(5));

        // Caller should only see receiver's ICE candidates
        Sanctum::actingAs($this->caller);
        $response = $this->getJson("/api/calls/{$this->videoCall->call_id}/signaling/ice-candidates");
        
        $response->assertStatus(200);
        $candidates = $response->json('data.ice_candidates');
        $this->assertCount(1, $candidates);
        $this->assertEquals($this->receiver->id, $candidates[0]['from_user_id']);

        // Receiver should only see caller's ICE candidates
        Sanctum::actingAs($this->receiver);
        $response = $this->getJson("/api/calls/{$this->videoCall->call_id}/signaling/ice-candidates");
        
        $response->assertStatus(200);
        $candidates = $response->json('data.ice_candidates');
        $this->assertCount(1, $candidates);
        $this->assertEquals($this->caller->id, $candidates[0]['from_user_id']);
    }

    public function test_update_connection_state()
    {
        Sanctum::actingAs($this->caller);

        $connectionData = [
            'connection_state' => 'connected',
            'ice_connection_state' => 'connected',
            'ice_gathering_state' => 'complete'
        ];

        $response = $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/connection-state", $connectionData);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Connection state updated successfully'
                ]);

        // Check database
        $this->videoCall->refresh();
        $this->assertNotNull($this->videoCall->webrtc_data);
        $this->assertEquals('connected', $this->videoCall->webrtc_data['connection_state']['connection_state']);
        $this->assertEquals($this->caller->id, $this->videoCall->webrtc_data['connection_state']['updated_by']);
    }

    public function test_invalid_connection_state_rejected()
    {
        Sanctum::actingAs($this->caller);

        $connectionData = [
            'connection_state' => 'invalid_state'
        ];

        $response = $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/connection-state", $connectionData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['connection_state']);
    }

    public function test_signaling_data_validation()
    {
        Sanctum::actingAs($this->caller);

        // Test invalid offer type
        $response = $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/offer", [
            'type' => 'invalid',
            'sdp' => 'test'
        ]);
        $response->assertStatus(422)->assertJsonValidationErrors(['type']);

        // Test missing SDP
        $response = $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/offer", [
            'type' => 'offer'
        ]);
        $response->assertStatus(422)->assertJsonValidationErrors(['sdp']);

        // Test invalid ICE candidate
        $response = $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/ice-candidate", [
            'sdpMLineIndex' => 'invalid'
        ]);
        $response->assertStatus(422)->assertJsonValidationErrors(['candidate']);
    }

    public function test_signaling_only_works_for_active_calls()
    {
        // End the call
        $this->videoCall->update(['status' => VideoCall::STATUS_ENDED]);

        Sanctum::actingAs($this->caller);

        $response = $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/offer", [
            'type' => 'offer',
            'sdp' => 'test'
        ]);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'Call is not in pending status'
                ]);
    }

    public function test_ice_candidate_limit_enforced()
    {
        Sanctum::actingAs($this->caller);

        // Set a low limit for testing
        config(['webrtc.rate_limits.ice_candidate_limit' => 2]);

        $iceCandidateData = [
            'candidate' => 'candidate:1 1 UDP 2130706431 ************* 54400 typ host',
            'sdpMid' => '0',
            'sdpMLineIndex' => 0
        ];

        // Store candidates up to limit
        $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/ice-candidate", $iceCandidateData)->assertStatus(200);
        $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/ice-candidate", $iceCandidateData)->assertStatus(200);

        // Third candidate should be rejected
        $response = $this->postJson("/api/calls/{$this->videoCall->call_id}/signaling/ice-candidate", $iceCandidateData);
        $response->assertStatus(429)
                ->assertJson([
                    'success' => false,
                    'message' => 'ICE candidate limit reached for this call'
                ]);
    }

    protected function tearDown(): void
    {
        // Clean up cache
        Cache::forget("ice_candidates:{$this->videoCall->call_id}");
        parent::tearDown();
    }
}
