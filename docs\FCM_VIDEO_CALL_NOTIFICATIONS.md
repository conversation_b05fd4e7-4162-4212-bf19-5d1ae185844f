# Firebase Cloud Messaging for Video Call Notifications

This document describes the implementation of Firebase Cloud Messaging (FCM) push notifications for video call events in the Laravel application.

## Overview

The FCM notification system automatically sends push notifications to users when:
- A new video call is initiated (incoming call notification)
- Call status changes (accepted, rejected, missed)
- Call ends with duration information

## Architecture

### Components

1. **VideoCallNotificationService** - Main service for video call notifications
2. **FCMService** - Enhanced FCM service with platform-specific configurations
3. **SendVideoCallFCMNotifications** - Event listener for automatic notifications
4. **FCMTokenController** - API endpoints for token management

### Flow

```
Call Event → Event Listener → Notification Service → FCM Service → Firebase → User Device
```

## Setup Instructions

### 1. Environment Configuration

Add these variables to your `.env` file:

```env
# FCM Configuration
FCM_SEND_IN_LOCAL=false
CALL_TIMEOUT_SECONDS=60
MISSED_CALL_THRESHOLD_SECONDS=300

# Firebase Configuration (if not already set)
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CREDENTIALS=path/to/service-account.json
```

### 2. Firebase Project Setup

1. Create a Firebase project at https://console.firebase.google.com
2. Enable Cloud Messaging
3. Download the service account JSON file
4. Place it in your Laravel storage directory
5. Update the `FIREBASE_CREDENTIALS` path in `.env`

### 3. Mobile App Configuration

#### Android
Add these notification channels to your Android app:

```kotlin
// In your Application class or MainActivity
private fun createNotificationChannels() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
        // Incoming call channel
        val incomingCallChannel = NotificationChannel(
            "incoming_call_channel",
            "Incoming Calls",
            NotificationManager.IMPORTANCE_HIGH
        ).apply {
            description = "Notifications for incoming video/audio calls"
            enableVibration(true)
            setSound(Uri.parse("android.resource://$packageName/raw/call_ringtone"), null)
        }

        // Call status channel
        val callStatusChannel = NotificationChannel(
            "call_status_channel",
            "Call Status",
            NotificationManager.IMPORTANCE_DEFAULT
        ).apply {
            description = "Notifications for call status updates"
        }

        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.createNotificationChannel(incomingCallChannel)
        notificationManager.createNotificationChannel(callStatusChannel)
    }
}
```

#### iOS
Add these categories to your iOS app:

```swift
// In AppDelegate or SceneDelegate
func setupNotificationCategories() {
    let acceptAction = UNNotificationAction(
        identifier: "ACCEPT_CALL",
        title: "Accept",
        options: [.foreground]
    )
    
    let rejectAction = UNNotificationAction(
        identifier: "REJECT_CALL",
        title: "Decline",
        options: []
    )
    
    let callCategory = UNNotificationCategory(
        identifier: "CALL_CATEGORY",
        actions: [acceptAction, rejectAction],
        intentIdentifiers: [],
        options: [.customDismissAction]
    )
    
    UNUserNotificationCenter.current().setNotificationCategories([callCategory])
}
```

## API Endpoints

### FCM Token Management

All endpoints require authentication via Laravel Sanctum.

#### Register FCM Token

**POST** `/api/fcm/register`

```json
{
    "token": "fcm_token_string",
    "device_type": "android",
    "device_id": "unique_device_id",
    "app_version": "1.0.0"
}
```

#### Remove FCM Token

**POST** `/api/fcm/remove`

```json
{
    "token": "fcm_token_string"
}
```

#### List User's FCM Tokens

**GET** `/api/fcm/tokens`

#### Test FCM Notification

**POST** `/api/fcm/test`

## Notification Types

### 1. Incoming Call Notification

**Triggered**: When a call is initiated
**Recipient**: Call receiver
**Features**:
- High priority notification
- Custom ringtone
- Full-screen intent (Android)
- Critical interruption level (iOS)
- Action buttons (Accept/Decline)

**Payload**:
```json
{
    "title": "Incoming Video Call",
    "body": "John Doe is calling you",
    "data": {
        "type": "incoming_call",
        "call_id": "call_uuid",
        "caller_id": 123,
        "caller_name": "John Doe",
        "caller_photo": "https://...",
        "call_type": "video",
        "room_id": "room_abc",
        "initiated_at": "2024-01-15T10:30:00Z"
    }
}
```

### 2. Call Status Notifications

**Triggered**: When call status changes
**Recipients**: Varies by status

#### Call Accepted
- **Recipient**: Caller
- **Message**: "Your call was accepted"

#### Call Rejected
- **Recipient**: Caller
- **Message**: "Your call was declined" + reason

#### Call Missed
- **Recipient**: Caller
- **Message**: "You missed a call from [Name]"

### 3. Call Ended Notification

**Triggered**: When call ends
**Recipients**: Both participants
**Message**: "Call with [Name] ended (duration)"

## Testing

### Command Line Testing

Test FCM notifications using the provided console command:

```bash
# Test with specific user
php artisan fcm:test-video-call --user-id=123 --type=incoming

# Test with specific token
php artisan fcm:test-video-call --token=fcm_token_here --type=accepted

# Test with random users
php artisan fcm:test-video-call --type=incoming
```

### API Testing

Use the test endpoint to verify FCM setup:

```bash
curl -X POST /api/fcm/test \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"
```

## Configuration Options

### Notification Channels (Android)

Configure in `config/fcm.php`:

```php
'video_calls' => [
    'channels' => [
        'incoming_call_channel' => [
            'name' => 'Incoming Calls',
            'importance' => 'high',
            'sound' => 'call_ringtone',
        ],
        'call_status_channel' => [
            'name' => 'Call Status',
            'importance' => 'default',
            'sound' => 'default',
        ],
    ],
]
```

### Notification Sounds

```php
'sounds' => [
    'android' => [
        'incoming_call' => 'call_ringtone',
        'status_change' => 'default',
    ],
    'ios' => [
        'incoming_call' => 'call_ringtone.caf',
        'status_change' => 'default',
    ],
],
```

### Call Timeouts

```php
'timeouts' => [
    'incoming_call_timeout' => 60, // seconds
    'missed_call_threshold' => 300, // seconds
],
```

## Troubleshooting

### Common Issues

1. **Notifications not received**
   - Check FCM token registration
   - Verify Firebase project configuration
   - Check device notification permissions
   - Review Laravel logs for errors

2. **Notifications received but not displayed**
   - Verify notification channel setup (Android)
   - Check notification category setup (iOS)
   - Ensure app is not in battery optimization

3. **Sound not playing**
   - Verify sound file exists in app resources
   - Check notification channel sound settings
   - Ensure device is not in silent mode

### Debug Commands

```bash
# Check FCM configuration
php artisan config:show fcm

# View recent logs
tail -f storage/logs/laravel.log | grep FCM

# Test with specific token
php artisan fcm:test-video-call --token=your_token --type=incoming
```

### Log Monitoring

Monitor these log entries:
- `FCM notification sent successfully`
- `FCM token registered`
- `FCM notification failed`
- `No FCM tokens found`

## Security Considerations

1. **Token Validation**: FCM tokens are validated before sending
2. **User Authorization**: Only authenticated users can register tokens
3. **Token Cleanup**: Invalid tokens are automatically removed
4. **Rate Limiting**: Consider implementing rate limiting for token registration
5. **Data Privacy**: Sensitive call data is not included in notification payload

## Performance Optimization

1. **Batch Processing**: Notifications are sent in batches of 500
2. **Queue Processing**: Notifications are processed asynchronously
3. **Token Cleanup**: Invalid tokens are automatically cleaned up
4. **Caching**: Consider caching user tokens for better performance

## Monitoring and Analytics

Track these metrics:
- Notification delivery rates
- Token registration/removal rates
- Failed notification attempts
- User engagement with notifications

## Future Enhancements

1. **Rich Notifications**: Add images and action buttons
2. **Notification Scheduling**: Schedule notifications for different time zones
3. **A/B Testing**: Test different notification formats
4. **Analytics Integration**: Track notification performance
5. **Multi-language Support**: Localized notification messages
