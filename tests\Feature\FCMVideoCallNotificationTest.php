<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\UserToken;
use App\Models\VideoCall;
use App\Services\VideoCallNotificationService;
use App\Services\FCMService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;
use Mockery;

class FCMVideoCallNotificationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $caller;
    protected $receiver;
    protected $fcmService;
    protected $notificationService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->caller = User::factory()->create(['is_active' => true]);
        $this->receiver = User::factory()->create(['is_active' => true]);

        // Create FCM tokens for receiver
        UserToken::factory()->count(2)->create([
            'user_id' => $this->receiver->id,
            'token' => 'test_fcm_token_' . $this->faker->uuid,
        ]);

        // Mock FCM service to avoid actual Firebase calls in tests
        $this->fcmService = Mockery::mock(FCMService::class);
        $this->app->instance(FCMService::class, $this->fcmService);

        $this->notificationService = new VideoCallNotificationService($this->fcmService);
    }

    public function test_fcm_token_registration()
    {
        Sanctum::actingAs($this->caller);

        $response = $this->postJson('/api/fcm/register', [
            'token' => 'new_fcm_token_123',
            'device_type' => 'android',
            'device_id' => 'device_123',
            'app_version' => '1.0.0'
        ]);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                    'message' => 'FCM token registered successfully'
                ]);

        $this->assertDatabaseHas('user_tokens', [
            'user_id' => $this->caller->id,
            'token' => 'new_fcm_token_123',
            'device_type' => 'android'
        ]);
    }

    public function test_fcm_token_update()
    {
        Sanctum::actingAs($this->caller);

        // Create existing token
        $existingToken = UserToken::create([
            'user_id' => $this->caller->id,
            'token' => 'existing_token_123',
            'device_type' => 'ios'
        ]);

        $response = $this->postJson('/api/fcm/register', [
            'token' => 'existing_token_123',
            'device_type' => 'android', // Changed from ios to android
            'device_id' => 'new_device_456'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'FCM token updated successfully'
                ]);

        $existingToken->refresh();
        $this->assertEquals('android', $existingToken->device_type);
        $this->assertEquals('new_device_456', $existingToken->device_id);
    }

    public function test_fcm_token_removal()
    {
        Sanctum::actingAs($this->caller);

        $token = UserToken::create([
            'user_id' => $this->caller->id,
            'token' => 'token_to_remove_123'
        ]);

        $response = $this->postJson('/api/fcm/remove', [
            'token' => 'token_to_remove_123'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'FCM token removed successfully'
                ]);

        $this->assertDatabaseMissing('user_tokens', [
            'id' => $token->id
        ]);
    }

    public function test_fcm_token_list()
    {
        Sanctum::actingAs($this->caller);

        UserToken::factory()->count(3)->create([
            'user_id' => $this->caller->id
        ]);

        $response = $this->getJson('/api/fcm/tokens');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'device_type',
                            'device_id',
                            'app_version',
                            'registered_at',
                            'last_updated'
                        ]
                    ]
                ]);

        $this->assertCount(3, $response->json('data'));
    }

    public function test_incoming_call_notification_service()
    {
        $videoCall = VideoCall::factory()->create([
            'caller_id' => $this->caller->id,
            'receiver_id' => $this->receiver->id,
            'status' => VideoCall::STATUS_PENDING
        ]);

        $videoCall->load(['caller', 'receiver']);

        // Mock FCM service to expect notification call
        $this->fcmService->shouldReceive('sendCallNotification')
            ->once()
            ->with(
                Mockery::type('array'), // tokens
                'Incoming Video Call',
                "{$this->caller->name} is calling you",
                Mockery::type('array'), // data
                true // isIncomingCall
            )
            ->andReturn(true);

        $result = $this->notificationService->sendIncomingCallNotification($videoCall);

        $this->assertTrue($result);
    }

    public function test_call_status_notification_service()
    {
        $videoCall = VideoCall::factory()->create([
            'caller_id' => $this->caller->id,
            'receiver_id' => $this->receiver->id,
            'status' => VideoCall::STATUS_ACCEPTED
        ]);

        $videoCall->load(['caller', 'receiver']);

        // Mock FCM service to expect notification call
        $this->fcmService->shouldReceive('sendCallNotification')
            ->once()
            ->with(
                Mockery::type('array'), // tokens
                'Call Accepted',
                'Your call was accepted',
                Mockery::type('array'), // data
                false // isIncomingCall
            )
            ->andReturn(true);

        $result = $this->notificationService->sendCallStatusNotification($videoCall, VideoCall::STATUS_PENDING);

        $this->assertTrue($result);
    }

    public function test_call_ended_notification_service()
    {
        $videoCall = VideoCall::factory()->create([
            'caller_id' => $this->caller->id,
            'receiver_id' => $this->receiver->id,
            'status' => VideoCall::STATUS_ENDED,
            'duration' => 120
        ]);

        $videoCall->load(['caller', 'receiver']);

        // Mock FCM service to expect notification calls for both participants
        $this->fcmService->shouldReceive('sendCallNotification')
            ->twice() // Once for each participant
            ->andReturn(true);

        $result = $this->notificationService->sendCallEndedNotification($videoCall);

        $this->assertTrue($result);
    }

    public function test_no_fcm_tokens_handling()
    {
        // Create user without FCM tokens
        $userWithoutTokens = User::factory()->create();

        $videoCall = VideoCall::factory()->create([
            'caller_id' => $this->caller->id,
            'receiver_id' => $userWithoutTokens->id,
            'status' => VideoCall::STATUS_PENDING
        ]);

        $videoCall->load(['caller', 'receiver']);

        // Should not call FCM service when no tokens exist
        $this->fcmService->shouldNotReceive('sendCallNotification');

        $result = $this->notificationService->sendIncomingCallNotification($videoCall);

        $this->assertFalse($result);
    }

    public function test_fcm_test_endpoint()
    {
        Sanctum::actingAs($this->caller);

        // Create FCM token for caller
        UserToken::create([
            'user_id' => $this->caller->id,
            'token' => 'test_token_for_caller'
        ]);

        // Mock FCM service
        $this->fcmService->shouldReceive('sendCallNotification')
            ->once()
            ->andReturn(true);

        $response = $this->postJson('/api/fcm/test');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Test notification sent successfully'
                ]);
    }

    public function test_fcm_test_endpoint_no_tokens()
    {
        Sanctum::actingAs($this->caller);

        // No FCM tokens for caller

        $response = $this->postJson('/api/fcm/test');

        $response->assertStatus(404)
                ->assertJson([
                    'success' => false,
                    'message' => 'No FCM tokens found for your account'
                ]);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
