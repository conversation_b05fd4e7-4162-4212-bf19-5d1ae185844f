<?php

namespace Database\Factories;

use App\Models\VideoCall;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\VideoCall>
 */
class VideoCallFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = VideoCall::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $initiatedAt = $this->faker->dateTimeBetween('-1 week', 'now');
        $status = $this->faker->randomElement([
            VideoCall::STATUS_PENDING,
            VideoCall::STATUS_ACCEPTED,
            VideoCall::STATUS_REJECTED,
            VideoCall::STATUS_ENDED,
            VideoCall::STATUS_MISSED,
            VideoCall::STATUS_CANCELLED
        ]);

        $acceptedAt = null;
        $endedAt = null;
        $duration = null;

        // Set timestamps based on status
        if (in_array($status, [VideoCall::STATUS_ACCEPTED, VideoCall::STATUS_ENDED])) {
            $acceptedAt = $this->faker->dateTimeBetween($initiatedAt, '+5 minutes');
            
            if ($status === VideoCall::STATUS_ENDED) {
                $endedAt = $this->faker->dateTimeBetween($acceptedAt, '+30 minutes');
                $duration = $this->faker->numberBetween(30, 1800); // 30 seconds to 30 minutes
            }
        } elseif (in_array($status, [VideoCall::STATUS_REJECTED, VideoCall::STATUS_MISSED, VideoCall::STATUS_CANCELLED])) {
            $endedAt = $this->faker->dateTimeBetween($initiatedAt, '+2 minutes');
        }

        return [
            'call_id' => 'call_' . Str::uuid(),
            'caller_id' => User::factory(),
            'receiver_id' => User::factory(),
            'status' => $status,
            'call_type' => $this->faker->randomElement([VideoCall::TYPE_AUDIO, VideoCall::TYPE_VIDEO]),
            'initiated_at' => $initiatedAt,
            'accepted_at' => $acceptedAt,
            'ended_at' => $endedAt,
            'duration' => $duration,
            'webrtc_data' => $this->faker->optional()->passthrough([
                'offer' => $this->faker->sha256,
                'answer' => $this->faker->sha256,
                'ice_candidates' => [
                    [
                        'candidate' => $this->faker->ipv4,
                        'sdpMid' => 'audio',
                        'sdpMLineIndex' => 0
                    ]
                ]
            ]),
            'room_id' => $this->faker->optional()->regexify('room_[a-zA-Z0-9]{10}'),
            'rejection_reason' => $status === VideoCall::STATUS_REJECTED ? $this->faker->sentence : null,
            'end_reason' => in_array($status, [VideoCall::STATUS_ENDED, VideoCall::STATUS_CANCELLED]) 
                ? $this->faker->randomElement(['Call completed', 'Network issues', 'User ended call']) 
                : null,
            'call_quality_metrics' => $status === VideoCall::STATUS_ENDED ? [
                'bandwidth' => $this->faker->numberBetween(256, 2048),
                'latency' => $this->faker->numberBetween(10, 200),
                'packet_loss' => $this->faker->randomFloat(2, 0, 5),
                'audio_quality' => $this->faker->numberBetween(1, 5),
                'video_quality' => $this->faker->numberBetween(1, 5),
            ] : null,
        ];
    }

    /**
     * Indicate that the call is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => VideoCall::STATUS_PENDING,
            'accepted_at' => null,
            'ended_at' => null,
            'duration' => null,
            'rejection_reason' => null,
            'end_reason' => null,
            'call_quality_metrics' => null,
        ]);
    }

    /**
     * Indicate that the call is accepted.
     */
    public function accepted(): static
    {
        return $this->state(function (array $attributes) {
            $acceptedAt = $this->faker->dateTimeBetween($attributes['initiated_at'], '+2 minutes');
            
            return [
                'status' => VideoCall::STATUS_ACCEPTED,
                'accepted_at' => $acceptedAt,
                'ended_at' => null,
                'duration' => null,
                'rejection_reason' => null,
                'end_reason' => null,
                'call_quality_metrics' => null,
            ];
        });
    }

    /**
     * Indicate that the call is ended.
     */
    public function ended(): static
    {
        return $this->state(function (array $attributes) {
            $acceptedAt = $this->faker->dateTimeBetween($attributes['initiated_at'], '+2 minutes');
            $endedAt = $this->faker->dateTimeBetween($acceptedAt, '+30 minutes');
            $duration = $this->faker->numberBetween(30, 1800);
            
            return [
                'status' => VideoCall::STATUS_ENDED,
                'accepted_at' => $acceptedAt,
                'ended_at' => $endedAt,
                'duration' => $duration,
                'rejection_reason' => null,
                'end_reason' => $this->faker->randomElement(['Call completed', 'Network issues', 'User ended call']),
                'call_quality_metrics' => [
                    'bandwidth' => $this->faker->numberBetween(256, 2048),
                    'latency' => $this->faker->numberBetween(10, 200),
                    'packet_loss' => $this->faker->randomFloat(2, 0, 5),
                    'audio_quality' => $this->faker->numberBetween(1, 5),
                    'video_quality' => $this->faker->numberBetween(1, 5),
                ],
            ];
        });
    }

    /**
     * Indicate that the call is rejected.
     */
    public function rejected(): static
    {
        return $this->state(function (array $attributes) {
            $endedAt = $this->faker->dateTimeBetween($attributes['initiated_at'], '+2 minutes');
            
            return [
                'status' => VideoCall::STATUS_REJECTED,
                'accepted_at' => null,
                'ended_at' => $endedAt,
                'duration' => null,
                'rejection_reason' => $this->faker->randomElement([
                    'Busy with another call',
                    'Not available right now',
                    'Poor network connection'
                ]),
                'end_reason' => null,
                'call_quality_metrics' => null,
            ];
        });
    }

    /**
     * Indicate that the call is for video.
     */
    public function video(): static
    {
        return $this->state(fn (array $attributes) => [
            'call_type' => VideoCall::TYPE_VIDEO,
        ]);
    }

    /**
     * Indicate that the call is for audio only.
     */
    public function audio(): static
    {
        return $this->state(fn (array $attributes) => [
            'call_type' => VideoCall::TYPE_AUDIO,
        ]);
    }

    /**
     * Set specific caller and receiver.
     */
    public function between(User $caller, User $receiver): static
    {
        return $this->state(fn (array $attributes) => [
            'caller_id' => $caller->id,
            'receiver_id' => $receiver->id,
        ]);
    }
}
