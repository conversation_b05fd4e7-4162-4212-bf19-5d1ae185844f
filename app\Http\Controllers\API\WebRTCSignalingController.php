<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\VideoCall;
use App\Events\WebRTCSignalingEvent;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

class WebRTCSignalingController extends Controller
{
    /**
     * Store WebRTC offer for a call
     */
    public function storeOffer(Request $request, string $callId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'type' => 'required|string|in:offer',
                'sdp' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $videoCall = VideoCall::where('call_id', $callId)->firstOrFail();

            // Check authorization - only caller can store offer
            if ($videoCall->caller_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only the caller can store the offer'
                ], 403);
            }

            // Check call status
            if ($videoCall->status !== VideoCall::STATUS_PENDING) {
                return response()->json([
                    'success' => false,
                    'message' => 'Call is not in pending status'
                ], 400);
            }

            $offerData = [
                'type' => $request->type,
                'sdp' => $request->sdp,
                'timestamp' => now()->toISOString()
            ];

            // Update webrtc_data with offer
            $webrtcData = $videoCall->webrtc_data ?? [];
            $webrtcData['offer'] = $offerData;
            $webrtcData['last_updated'] = now()->toISOString();

            $videoCall->update(['webrtc_data' => $webrtcData]);

            // Broadcast offer to receiver via WebSocket
            broadcast(new WebRTCSignalingEvent($videoCall, 'offer', $offerData));

            Log::info('WebRTC offer stored', [
                'call_id' => $callId,
                'caller_id' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Offer stored successfully',
                'data' => [
                    'call_id' => $callId,
                    'type' => 'offer',
                    'timestamp' => $offerData['timestamp']
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to store WebRTC offer', [
                'call_id' => $callId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to store offer'
            ], 500);
        }
    }

    /**
     * Store WebRTC answer for a call
     */
    public function storeAnswer(Request $request, string $callId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'type' => 'required|string|in:answer',
                'sdp' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $videoCall = VideoCall::where('call_id', $callId)->firstOrFail();

            // Check authorization - only receiver can store answer
            if ($videoCall->receiver_id !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only the receiver can store the answer'
                ], 403);
            }

            // Check call status
            if (!in_array($videoCall->status, [VideoCall::STATUS_PENDING, VideoCall::STATUS_ACCEPTED])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Call is not in a valid status for answer'
                ], 400);
            }

            $answerData = [
                'type' => $request->type,
                'sdp' => $request->sdp,
                'timestamp' => now()->toISOString()
            ];

            // Update webrtc_data with answer
            $webrtcData = $videoCall->webrtc_data ?? [];
            $webrtcData['answer'] = $answerData;
            $webrtcData['last_updated'] = now()->toISOString();

            $videoCall->update(['webrtc_data' => $webrtcData]);

            // Broadcast answer to caller via WebSocket
            broadcast(new WebRTCSignalingEvent($videoCall, 'answer', $answerData));

            Log::info('WebRTC answer stored', [
                'call_id' => $callId,
                'receiver_id' => Auth::id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Answer stored successfully',
                'data' => [
                    'call_id' => $callId,
                    'type' => 'answer',
                    'timestamp' => $answerData['timestamp']
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to store WebRTC answer', [
                'call_id' => $callId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to store answer'
            ], 500);
        }
    }

    /**
     * Store ICE candidate for a call
     */
    public function storeIceCandidate(Request $request, string $callId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'candidate' => 'required|string',
                'sdpMid' => 'nullable|string',
                'sdpMLineIndex' => 'nullable|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $videoCall = VideoCall::where('call_id', $callId)->firstOrFail();

            // Check authorization - both participants can store ICE candidates
            if (!$videoCall->isParticipant(Auth::id())) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not a participant in this call'
                ], 403);
            }

            $iceCandidateData = [
                'candidate' => $request->candidate,
                'sdpMid' => $request->sdpMid,
                'sdpMLineIndex' => $request->sdpMLineIndex,
                'timestamp' => now()->toISOString(),
                'from_user_id' => Auth::id()
            ];

            // Store ICE candidate temporarily in cache (5 minutes TTL)
            $cacheKey = "ice_candidates:{$callId}";
            $iceCandidates = Cache::get($cacheKey, []);
            
            // Limit ICE candidates per call
            if (count($iceCandidates) >= config('webrtc.ice_candidate_limit', 50)) {
                return response()->json([
                    'success' => false,
                    'message' => 'ICE candidate limit reached for this call'
                ], 429);
            }

            $iceCandidates[] = $iceCandidateData;
            Cache::put($cacheKey, $iceCandidates, now()->addMinutes(5));

            // Broadcast ICE candidate to other participant via WebSocket
            broadcast(new WebRTCSignalingEvent($videoCall, 'ice-candidate', $iceCandidateData));

            Log::debug('ICE candidate stored', [
                'call_id' => $callId,
                'from_user_id' => Auth::id(),
                'candidate_count' => count($iceCandidates)
            ]);

            return response()->json([
                'success' => true,
                'message' => 'ICE candidate stored successfully',
                'data' => [
                    'call_id' => $callId,
                    'timestamp' => $iceCandidateData['timestamp']
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to store ICE candidate', [
                'call_id' => $callId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to store ICE candidate'
            ], 500);
        }
    }

    /**
     * Retrieve all signaling data for a call
     */
    public function getSignalingData(string $callId): JsonResponse
    {
        try {
            $videoCall = VideoCall::where('call_id', $callId)->firstOrFail();

            // Check authorization
            if (!$videoCall->isParticipant(Auth::id())) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not a participant in this call'
                ], 403);
            }

            $webrtcData = $videoCall->webrtc_data ?? [];
            
            // Get ICE candidates from cache
            $cacheKey = "ice_candidates:{$callId}";
            $iceCandidates = Cache::get($cacheKey, []);

            // Filter ICE candidates for the requesting user (get candidates from other participant)
            $filteredIceCandidates = array_filter($iceCandidates, function ($candidate) {
                return $candidate['from_user_id'] !== Auth::id();
            });

            $signalingData = [
                'call_id' => $callId,
                'offer' => $webrtcData['offer'] ?? null,
                'answer' => $webrtcData['answer'] ?? null,
                'ice_candidates' => array_values($filteredIceCandidates),
                'last_updated' => $webrtcData['last_updated'] ?? null,
                'retrieved_at' => now()->toISOString()
            ];

            return response()->json([
                'success' => true,
                'data' => $signalingData
            ]);

        } catch (Exception $e) {
            Log::error('Failed to retrieve signaling data', [
                'call_id' => $callId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve signaling data'
            ], 500);
        }
    }

    /**
     * Get ICE candidates for a call
     */
    public function getIceCandidates(string $callId): JsonResponse
    {
        try {
            $videoCall = VideoCall::where('call_id', $callId)->firstOrFail();

            // Check authorization
            if (!$videoCall->isParticipant(Auth::id())) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not a participant in this call'
                ], 403);
            }

            // Get ICE candidates from cache
            $cacheKey = "ice_candidates:{$callId}";
            $iceCandidates = Cache::get($cacheKey, []);

            // Filter ICE candidates for the requesting user
            $filteredIceCandidates = array_filter($iceCandidates, function ($candidate) {
                return $candidate['from_user_id'] !== Auth::id();
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'call_id' => $callId,
                    'ice_candidates' => array_values($filteredIceCandidates),
                    'retrieved_at' => now()->toISOString()
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to retrieve ICE candidates', [
                'call_id' => $callId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve ICE candidates'
            ], 500);
        }
    }

    /**
     * Update connection state for a call
     */
    public function updateConnectionState(Request $request, string $callId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'connection_state' => 'required|string|in:new,connecting,connected,disconnected,failed,closed',
                'ice_connection_state' => 'sometimes|string|in:new,checking,connected,completed,failed,disconnected,closed',
                'ice_gathering_state' => 'sometimes|string|in:new,gathering,complete',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $videoCall = VideoCall::where('call_id', $callId)->firstOrFail();

            // Check authorization
            if (!$videoCall->isParticipant(Auth::id())) {
                return response()->json([
                    'success' => false,
                    'message' => 'You are not a participant in this call'
                ], 403);
            }

            $connectionData = [
                'connection_state' => $request->connection_state,
                'ice_connection_state' => $request->ice_connection_state,
                'ice_gathering_state' => $request->ice_gathering_state,
                'updated_by' => Auth::id(),
                'timestamp' => now()->toISOString()
            ];

            // Update webrtc_data with connection state
            $webrtcData = $videoCall->webrtc_data ?? [];
            $webrtcData['connection_state'] = $connectionData;
            $webrtcData['last_updated'] = now()->toISOString();

            $videoCall->update(['webrtc_data' => $webrtcData]);

            // Broadcast connection state change
            broadcast(new WebRTCSignalingEvent($videoCall, 'connection-state', $connectionData));

            Log::info('WebRTC connection state updated', [
                'call_id' => $callId,
                'user_id' => Auth::id(),
                'connection_state' => $request->connection_state
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Connection state updated successfully',
                'data' => [
                    'call_id' => $callId,
                    'connection_state' => $request->connection_state,
                    'timestamp' => $connectionData['timestamp']
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to update connection state', [
                'call_id' => $callId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update connection state'
            ], 500);
        }
    }
}
