<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\VideoCall;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class VideoCallSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some existing users or create them
        $users = User::active()->take(10)->get();
        
        if ($users->count() < 2) {
            // Create some test users if not enough exist
            $users = User::factory()->count(10)->create([
                'is_active' => true
            ]);
        }

        // Create various types of video calls
        foreach ($users as $index => $caller) {
            $receiver = $users->where('id', '!=', $caller->id)->random();
            
            // Create different types of calls for demonstration
            switch ($index % 5) {
                case 0:
                    // Completed video call
                    VideoCall::factory()
                        ->between($caller, $receiver)
                        ->video()
                        ->ended()
                        ->create();
                    break;
                    
                case 1:
                    // Rejected audio call
                    VideoCall::factory()
                        ->between($caller, $receiver)
                        ->audio()
                        ->rejected()
                        ->create();
                    break;
                    
                case 2:
                    // Active video call
                    VideoCall::factory()
                        ->between($caller, $receiver)
                        ->video()
                        ->accepted()
                        ->create();
                    break;
                    
                case 3:
                    // Pending video call
                    VideoCall::factory()
                        ->between($caller, $receiver)
                        ->video()
                        ->pending()
                        ->create();
                    break;
                    
                case 4:
                    // Missed call
                    VideoCall::factory()
                        ->between($caller, $receiver)
                        ->state([
                            'status' => VideoCall::STATUS_MISSED,
                            'ended_at' => now()->subMinutes(rand(1, 60)),
                        ])
                        ->create();
                    break;
            }
        }

        // Create some additional random calls
        VideoCall::factory()->count(20)->create();

        $this->command->info('Video call sample data created successfully!');
    }
}
