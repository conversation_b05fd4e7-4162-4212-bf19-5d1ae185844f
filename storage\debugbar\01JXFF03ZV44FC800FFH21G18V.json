{"__meta": {"id": "01JXFF03ZV44FC800FFH21G18V", "datetime": "2025-06-11 15:21:20", "utime": **********.508137, "method": "POST", "uri": "/livewire/message/dashboard-livewire", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749644477.127644, "end": **********.50815, "duration": 3.3805060386657715, "duration_str": "3.38s", "measures": [{"label": "Booting", "start": 1749644477.127644, "relative_start": 0, "end": **********.130001, "relative_end": **********.130001, "duration": 3.***************, "duration_str": "3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.130009, "relative_start": 3.****************, "end": **********.508151, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.142723, "relative_start": 3.****************, "end": **********.145331, "relative_end": **********.145331, "duration": 0.0026078224182128906, "duration_str": "2.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: livewire.dashboard", "start": **********.255439, "relative_start": 3.****************, "end": **********.255439, "relative_end": **********.255439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::45695c6f18e73078e666b51d5f0d1a23", "start": **********.263941, "relative_start": 3.****************, "end": **********.263941, "relative_end": **********.263941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dashboard-card", "start": **********.264332, "relative_start": 3.***************, "end": **********.264332, "relative_end": **********.264332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b2d6a16c890da3066df57f1e75f98f94", "start": **********.282778, "relative_start": 3.1551339626312256, "end": **********.282778, "relative_end": **********.282778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dashboard-card", "start": **********.283131, "relative_start": 3.155486822128296, "end": **********.283131, "relative_end": **********.283131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::850261f8052b07c7363edcc40992e0ac", "start": **********.287032, "relative_start": 3.1593878269195557, "end": **********.287032, "relative_end": **********.287032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dashboard-card", "start": **********.28736, "relative_start": 3.1597158908843994, "end": **********.28736, "relative_end": **********.28736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1a1df5545801c412d4341e2d0590ddea", "start": **********.292745, "relative_start": 3.1651010513305664, "end": **********.292745, "relative_end": **********.292745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dashboard-card", "start": **********.293084, "relative_start": 3.1654398441314697, "end": **********.293084, "relative_end": **********.293084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dashboard-chart", "start": **********.293516, "relative_start": 3.1658718585968018, "end": **********.293516, "relative_end": **********.293516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dashboard-chart", "start": **********.293932, "relative_start": 3.166287899017334, "end": **********.293932, "relative_end": **********.293932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dashboard-chart", "start": **********.294195, "relative_start": 3.166550874710083, "end": **********.294195, "relative_end": **********.294195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dashboard-chart", "start": **********.29445, "relative_start": 3.1668059825897217, "end": **********.29445, "relative_end": **********.29445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.dashboard-chart", "start": **********.294761, "relative_start": 3.16711688041687, "end": **********.294761, "relative_end": **********.294761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fe3c1901530572031455dd1e23828f9f", "start": **********.307657, "relative_start": 3.1800129413604736, "end": **********.307657, "relative_end": **********.307657, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.baseview", "start": **********.339002, "relative_start": 3.211357831954956, "end": **********.339002, "relative_end": **********.339002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: layouts.partials.demo-header", "start": **********.339788, "relative_start": 3.212143898010254, "end": **********.339788, "relative_end": **********.339788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: components.loading", "start": **********.340304, "relative_start": 3.2126598358154297, "end": **********.340304, "relative_end": **********.340304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.50578, "relative_start": 3.378135919570923, "end": **********.506563, "relative_end": **********.506563, "duration": 0.0007829666137695312, "duration_str": "783μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 49197720, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.28", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 18, "nb_templates": 18, "templates": [{"name": "livewire.dashboard", "param_count": null, "params": [], "start": **********.25539, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\resources\\views/livewire/dashboard.blade.phplivewire.dashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fresources%2Fviews%2Flivewire%2Fdashboard.blade.php&line=1", "ajax": false, "filename": "dashboard.blade.php", "line": "?"}}, {"name": "__components::45695c6f18e73078e666b51d5f0d1a23", "param_count": null, "params": [], "start": **********.263896, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\storage\\framework\\views/45695c6f18e73078e666b51d5f0d1a23.blade.php__components::45695c6f18e73078e666b51d5f0d1a23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fstorage%2Fframework%2Fviews%2F45695c6f18e73078e666b51d5f0d1a23.blade.php&line=1", "ajax": false, "filename": "45695c6f18e73078e666b51d5f0d1a23.blade.php", "line": "?"}}, {"name": "components.dashboard-card", "param_count": null, "params": [], "start": **********.26429, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\resources\\views/components/dashboard-card.blade.phpcomponents.dashboard-card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fresources%2Fviews%2Fcomponents%2Fdashboard-card.blade.php&line=1", "ajax": false, "filename": "dashboard-card.blade.php", "line": "?"}}, {"name": "__components::b2d6a16c890da3066df57f1e75f98f94", "param_count": null, "params": [], "start": **********.282726, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\storage\\framework\\views/b2d6a16c890da3066df57f1e75f98f94.blade.php__components::b2d6a16c890da3066df57f1e75f98f94", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fstorage%2Fframework%2Fviews%2Fb2d6a16c890da3066df57f1e75f98f94.blade.php&line=1", "ajax": false, "filename": "b2d6a16c890da3066df57f1e75f98f94.blade.php", "line": "?"}}, {"name": "components.dashboard-card", "param_count": null, "params": [], "start": **********.283091, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\resources\\views/components/dashboard-card.blade.phpcomponents.dashboard-card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fresources%2Fviews%2Fcomponents%2Fdashboard-card.blade.php&line=1", "ajax": false, "filename": "dashboard-card.blade.php", "line": "?"}}, {"name": "__components::850261f8052b07c7363edcc40992e0ac", "param_count": null, "params": [], "start": **********.286988, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\storage\\framework\\views/850261f8052b07c7363edcc40992e0ac.blade.php__components::850261f8052b07c7363edcc40992e0ac", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fstorage%2Fframework%2Fviews%2F850261f8052b07c7363edcc40992e0ac.blade.php&line=1", "ajax": false, "filename": "850261f8052b07c7363edcc40992e0ac.blade.php", "line": "?"}}, {"name": "components.dashboard-card", "param_count": null, "params": [], "start": **********.28732, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\resources\\views/components/dashboard-card.blade.phpcomponents.dashboard-card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fresources%2Fviews%2Fcomponents%2Fdashboard-card.blade.php&line=1", "ajax": false, "filename": "dashboard-card.blade.php", "line": "?"}}, {"name": "__components::1a1df5545801c412d4341e2d0590ddea", "param_count": null, "params": [], "start": **********.2927, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\storage\\framework\\views/1a1df5545801c412d4341e2d0590ddea.blade.php__components::1a1df5545801c412d4341e2d0590ddea", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fstorage%2Fframework%2Fviews%2F1a1df5545801c412d4341e2d0590ddea.blade.php&line=1", "ajax": false, "filename": "1a1df5545801c412d4341e2d0590ddea.blade.php", "line": "?"}}, {"name": "components.dashboard-card", "param_count": null, "params": [], "start": **********.293043, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\resources\\views/components/dashboard-card.blade.phpcomponents.dashboard-card", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fresources%2Fviews%2Fcomponents%2Fdashboard-card.blade.php&line=1", "ajax": false, "filename": "dashboard-card.blade.php", "line": "?"}}, {"name": "components.dashboard-chart", "param_count": null, "params": [], "start": **********.293475, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\resources\\views/components/dashboard-chart.blade.phpcomponents.dashboard-chart", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fresources%2Fviews%2Fcomponents%2Fdashboard-chart.blade.php&line=1", "ajax": false, "filename": "dashboard-chart.blade.php", "line": "?"}}, {"name": "components.dashboard-chart", "param_count": null, "params": [], "start": **********.293892, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\resources\\views/components/dashboard-chart.blade.phpcomponents.dashboard-chart", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fresources%2Fviews%2Fcomponents%2Fdashboard-chart.blade.php&line=1", "ajax": false, "filename": "dashboard-chart.blade.php", "line": "?"}}, {"name": "components.dashboard-chart", "param_count": null, "params": [], "start": **********.294155, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\resources\\views/components/dashboard-chart.blade.phpcomponents.dashboard-chart", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fresources%2Fviews%2Fcomponents%2Fdashboard-chart.blade.php&line=1", "ajax": false, "filename": "dashboard-chart.blade.php", "line": "?"}}, {"name": "components.dashboard-chart", "param_count": null, "params": [], "start": **********.294411, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\resources\\views/components/dashboard-chart.blade.phpcomponents.dashboard-chart", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fresources%2Fviews%2Fcomponents%2Fdashboard-chart.blade.php&line=1", "ajax": false, "filename": "dashboard-chart.blade.php", "line": "?"}}, {"name": "components.dashboard-chart", "param_count": null, "params": [], "start": **********.294722, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\resources\\views/components/dashboard-chart.blade.phpcomponents.dashboard-chart", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fresources%2Fviews%2Fcomponents%2Fdashboard-chart.blade.php&line=1", "ajax": false, "filename": "dashboard-chart.blade.php", "line": "?"}}, {"name": "__components::fe3c1901530572031455dd1e23828f9f", "param_count": null, "params": [], "start": **********.30762, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\storage\\framework\\views/fe3c1901530572031455dd1e23828f9f.blade.php__components::fe3c1901530572031455dd1e23828f9f", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fstorage%2Fframework%2Fviews%2Ffe3c1901530572031455dd1e23828f9f.blade.php&line=1", "ajax": false, "filename": "fe3c1901530572031455dd1e23828f9f.blade.php", "line": "?"}}, {"name": "components.baseview", "param_count": null, "params": [], "start": **********.338958, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\resources\\views/components/baseview.blade.phpcomponents.baseview", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fresources%2Fviews%2Fcomponents%2Fbaseview.blade.php&line=1", "ajax": false, "filename": "baseview.blade.php", "line": "?"}}, {"name": "layouts.partials.demo-header", "param_count": null, "params": [], "start": **********.339748, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\resources\\views/layouts/partials/demo-header.blade.phplayouts.partials.demo-header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fdemo-header.blade.php&line=1", "ajax": false, "filename": "demo-header.blade.php", "line": "?"}}, {"name": "components.loading", "param_count": null, "params": [], "start": **********.34026, "type": "blade", "hash": "bladeC:\\xampp\\htdocs\\resources\\views/components/loading.blade.phpcomponents.loading", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fresources%2Fviews%2Fcomponents%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}}]}, "queries": {"count": 104, "nb_statements": 104, "nb_visible_statements": 104, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.09385000000000002, "accumulated_duration_str": "93.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 4 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select `users`.*, (select count(*) from `referrals` where `users`.`id` = `referrals`.`user_id`) as `my_referrals_count` from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/UserLang.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Middleware\\UserLang.php", "line": 29}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.154988, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mart", "explain": null, "start_percent": 0, "width_percent": 0.607}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 103}, {"index": 32, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 333}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "start": **********.180917, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "mart", "explain": null, "start_percent": 0.607, "width_percent": 0.81}, {"sql": "select `vendors`.*, (select count(*) from `reviews` where `vendors`.`id` = `reviews`.`vendor_id` and `reviews`.`deleted_at` is null) as `reviews_count`, (select count(*) from `orders` where `vendors`.`id` = `orders`.`vendor_id` and exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order' and `name` in ('successful', 'delivered') and `id` = (select max(id) from `statuses` where `model_type` = 'App\\\\Models\\\\Order' and `model_id` = `orders`.`id`)) and `orders`.`deleted_at` is null) as `successful_sales_count` from `vendors` where `vendors`.`deleted_at` is null order by `successful_sales_count` desc limit 6", "type": "query", "params": [], "bindings": ["App\\Models\\Order", "successful", "delivered", "App\\Models\\Order"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 333}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.186935, "duration": 0.01026, "duration_str": "10.26ms", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:333", "source": {"index": 15, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 333}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=333", "ajax": false, "filename": "DashboardLivewire.php", "line": "333"}, "connection": "mart", "explain": null, "start_percent": 1.417, "width_percent": 10.932}, {"sql": "select * from `vendor_types` where `vendor_types`.`id` in (3) and `vendor_types`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 333}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.200444, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:333", "source": {"index": 20, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 333}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=333", "ajax": false, "filename": "DashboardLivewire.php", "line": "333"}, "connection": "mart", "explain": null, "start_percent": 12.349, "width_percent": 0.618}, {"sql": "select `fees`.*, `fee_vendor`.`vendor_id` as `pivot_vendor_id`, `fee_vendor`.`fee_id` as `pivot_fee_id` from `fees` inner join `fee_vendor` on `fees`.`id` = `fee_vendor`.`fee_id` where `is_active` = 1 and `fee_vendor`.`vendor_id` in (1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 333}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.203274, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:333", "source": {"index": 19, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 333}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=333", "ajax": false, "filename": "DashboardLivewire.php", "line": "333"}, "connection": "mart", "explain": null, "start_percent": 12.968, "width_percent": 0.767}, {"sql": "select `vendors`.*, (select count(*) from `reviews` where `vendors`.`id` = `reviews`.`vendor_id` and `reviews`.`deleted_at` is null) as `reviews_count`, (select count(*) from `ratings` where `vendors`.`id` = `ratings`.`rateable_id` and `ratings`.`rateable_type` = 'App\\\\Models\\\\Vendor') as `ratings_count` from `vendors` where `vendors`.`deleted_at` is null order by `ratings_count` desc limit 6", "type": "query", "params": [], "bindings": ["App\\Models\\Vendor"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 340}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.20694, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:340", "source": {"index": 15, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=340", "ajax": false, "filename": "DashboardLivewire.php", "line": "340"}, "connection": "mart", "explain": null, "start_percent": 13.735, "width_percent": 1.129}, {"sql": "select * from `vendor_types` where `vendor_types`.`id` in (3) and `vendor_types`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 340}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.210212, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:340", "source": {"index": 20, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=340", "ajax": false, "filename": "DashboardLivewire.php", "line": "340"}, "connection": "mart", "explain": null, "start_percent": 14.864, "width_percent": 0.298}, {"sql": "select `fees`.*, `fee_vendor`.`vendor_id` as `pivot_vendor_id`, `fee_vendor`.`fee_id` as `pivot_fee_id` from `fees` inner join `fee_vendor` on `fees`.`id` = `fee_vendor`.`fee_id` where `is_active` = 1 and `fee_vendor`.`vendor_id` in (1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 340}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.212177, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:340", "source": {"index": 19, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 340}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=340", "ajax": false, "filename": "DashboardLivewire.php", "line": "340"}, "connection": "mart", "explain": null, "start_percent": 15.162, "width_percent": 0.309}, {"sql": "select `users`.*, (select count(*) from `referrals` where `users`.`id` = `referrals`.`user_id`) as `my_referrals_count`, (select count(*) from `orders` where `users`.`id` = `orders`.`user_id` and `orders`.`deleted_at` is null) as `orders_count`, (select count(*) from `orders` where `users`.`id` = `orders`.`user_id` and `payment_status` = 'successful' and exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order' and `name` in ('delivered') and `id` = (select max(id) from `statuses` where `model_type` = 'App\\\\Models\\\\Order' and `model_id` = `orders`.`id`)) and `orders`.`deleted_at` is null) as `successful_orders_count` from `users` where `users`.`deleted_at` is null order by `successful_orders_count` desc limit 6", "type": "query", "params": [], "bindings": ["successful", "App\\Models\\Order", "delivered", "App\\Models\\Order"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 346}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.214945, "duration": 0.0109, "duration_str": "10.9ms", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:346", "source": {"index": 15, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 346}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=346", "ajax": false, "filename": "DashboardLivewire.php", "line": "346"}, "connection": "mart", "explain": null, "start_percent": 15.471, "width_percent": 11.614}, {"sql": "select `products`.*, (select count(*) from `product_reviews` where `products`.`id` = `product_reviews`.`product_id`) as `reviews_count`, (select sum(`order_products`.`quantity`) from `order_products` where `products`.`id` = `order_products`.`product_id` and exists (select * from `orders` where `order_products`.`order_id` = `orders`.`id` and `payment_status` = 'successful' and exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order' and `name` in ('delivered') and `id` = (select max(id) from `statuses` where `model_type` = 'App\\\\Models\\\\Order' and `model_id` = `orders`.`id`)))) as `successful_sales_sum_quantity`, (select count(*) from `order_products` where `products`.`id` = `order_products`.`product_id` and exists (select * from `orders` where `order_products`.`order_id` = `orders`.`id` and `payment_status` = 'successful' and exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order' and `name` in ('delivered') and `id` = (select max(id) from `statuses` where `model_type` = 'App\\\\Models\\\\Order' and `model_id` = `orders`.`id`)))) as `successful_sales_count` from `products` where `products`.`deleted_at` is null order by `successful_sales_count` desc limit 6", "type": "query", "params": [], "bindings": ["successful", "App\\Models\\Order", "delivered", "App\\Models\\Order", "successful", "App\\Models\\Order", "delivered", "App\\Models\\Order"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 362}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.229551, "duration": 0.01045, "duration_str": "10.45ms", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:362", "source": {"index": 15, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 362}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=362", "ajax": false, "filename": "DashboardLivewire.php", "line": "362"}, "connection": "mart", "explain": null, "start_percent": 27.086, "width_percent": 11.135}, {"sql": "select `vendors`.*, (select count(*) from `reviews` where `vendors`.`id` = `reviews`.`vendor_id` and `reviews`.`deleted_at` is null) as `reviews_count` from `vendors` where `vendors`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 362}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.24238, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:362", "source": {"index": 20, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 362}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=362", "ajax": false, "filename": "DashboardLivewire.php", "line": "362"}, "connection": "mart", "explain": null, "start_percent": 38.221, "width_percent": 0.607}, {"sql": "select * from `vendor_types` where `vendor_types`.`id` in (3) and `vendor_types`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 362}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.2447622, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:362", "source": {"index": 25, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 362}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=362", "ajax": false, "filename": "DashboardLivewire.php", "line": "362"}, "connection": "mart", "explain": null, "start_percent": 38.828, "width_percent": 0.32}, {"sql": "select `fees`.*, `fee_vendor`.`vendor_id` as `pivot_vendor_id`, `fee_vendor`.`fee_id` as `pivot_fee_id` from `fees` inner join `fee_vendor` on `fees`.`id` = `fee_vendor`.`fee_id` where `is_active` = 1 and `fee_vendor`.`vendor_id` in (1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 24, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 362}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.246568, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:362", "source": {"index": 24, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 362}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=362", "ajax": false, "filename": "DashboardLivewire.php", "line": "362"}, "connection": "mart", "explain": null, "start_percent": 39.148, "width_percent": 0.309}, {"sql": "select `tags`.*, `product_tag`.`product_id` as `pivot_product_id`, `product_tag`.`tag_id` as `pivot_tag_id` from `tags` inner join `product_tag` on `tags`.`id` = `product_tag`.`tag_id` where `product_tag`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 362}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.2483542, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:362", "source": {"index": 19, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 362}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=362", "ajax": false, "filename": "DashboardLivewire.php", "line": "362"}, "connection": "mart", "explain": null, "start_percent": 39.457, "width_percent": 0.469}, {"sql": "select * from `product_timings` where `product_timings`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 362}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.2506812, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:362", "source": {"index": 20, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 362}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=362", "ajax": false, "filename": "DashboardLivewire.php", "line": "362"}, "connection": "mart", "explain": null, "start_percent": 39.925, "width_percent": 0.298}, {"sql": "select count(*) as aggregate from `orders` where `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 45}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.259693, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:45", "source": {"index": 16, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=45", "ajax": false, "filename": "DashboardLivewire.php", "line": "45"}, "connection": "mart", "explain": null, "start_percent": 40.224, "width_percent": 0.437}, {"sql": "select `users`.*, (select count(*) from `referrals` where `users`.`id` = `referrals`.`user_id`) as `my_referrals_count` from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.264972, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:49", "source": {"index": 20, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 49}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=49", "ajax": false, "filename": "DashboardLivewire.php", "line": "49"}, "connection": "mart", "explain": null, "start_percent": 40.661, "width_percent": 0.501}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "start": **********.267245, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "mart", "explain": null, "start_percent": 41.161, "width_percent": 0.394}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'mart' and table_name = 'commissions' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 51}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.269186, "duration": 0.00902, "duration_str": "9.02ms", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:51", "source": {"index": 14, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=51", "ajax": false, "filename": "DashboardLivewire.php", "line": "51"}, "connection": "mart", "explain": null, "start_percent": 41.556, "width_percent": 9.611}, {"sql": "select sum(`admin_commission`) as aggregate from `commissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 52}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.280391, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:52", "source": {"index": 19, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=52", "ajax": false, "filename": "DashboardLivewire.php", "line": "52"}, "connection": "mart", "explain": null, "start_percent": 51.167, "width_percent": 0.298}, {"sql": "select count(*) as aggregate from `vendors` where `vendors`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 82}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.283937, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:82", "source": {"index": 16, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 82}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=82", "ajax": false, "filename": "DashboardLivewire.php", "line": "82"}, "connection": "mart", "explain": null, "start_percent": 51.465, "width_percent": 0.458}, {"sql": "select * from `roles` where `name` = 'client' and `guard_name` = 'web' limit 1", "type": "query", "params": [], "bindings": ["client", "web"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 102}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 96}, {"index": 26, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 182}, {"index": 35, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 86}], "start": **********.288321, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Role.php:169", "source": {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Models/Role.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Models\\Role.php", "line": 169}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=169", "ajax": false, "filename": "Role.php", "line": "169"}, "connection": "mart", "explain": null, "start_percent": 51.923, "width_percent": 0.469}, {"sql": "select count(*) as aggregate from `users` where exists (select * from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `users`.`id` = `model_has_roles`.`model_id` and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User' and `roles`.`id` in (3)) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 86}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\xampp\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.290062, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DashboardLivewire.php:86", "source": {"index": 16, "namespace": null, "name": "app/Http/Livewire/DashboardLivewire.php", "file": "C:\\xampp\\htdocs\\app\\Http\\Livewire\\DashboardLivewire.php", "line": 86}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FHttp%2FLivewire%2FDashboardLivewire.php&line=86", "ajax": false, "filename": "DashboardLivewire.php", "line": "86"}, "connection": "mart", "explain": null, "start_percent": 52.392, "width_percent": 0.543}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\Vendor'", "type": "query", "params": [], "bindings": ["App\\Models\\Vendor"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 278}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 297}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 309}], "start": **********.29611, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:565", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=565", "ajax": false, "filename": "InteractsWithMedia.php", "line": "565"}, "connection": "mart", "explain": null, "start_percent": 52.936, "width_percent": 0.597}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\Vendor'", "type": "query", "params": [], "bindings": ["App\\Models\\Vendor"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 278}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 297}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 309}], "start": **********.3054059, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:565", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=565", "ajax": false, "filename": "InteractsWithMedia.php", "line": "565"}, "connection": "mart", "explain": null, "start_percent": 53.532, "width_percent": 0.586}, {"sql": "select avg(`rating`) as aggregate from `ratings` where `ratings`.`rateable_type` = 'App\\\\Models\\\\Vendor' and `ratings`.`rateable_id` = 1 and `ratings`.`rateable_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\Vendor", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 118}, {"index": 27, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 177}, {"index": 34, "namespace": "view", "name": "livewire.dashboard", "file": "C:\\xampp\\htdocs\\resources\\views/livewire/dashboard.blade.php", "line": 445}, {"index": 36, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}], "start": **********.323444, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Rateable.php:70", "source": {"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fwillvincent%2Flaravel-rateable%2Fsrc%2FRateable.php&line=70", "ajax": false, "filename": "Rateable.php", "line": "70"}, "connection": "mart", "explain": null, "start_percent": 54.118, "width_percent": 0.597}, {"sql": "select avg(`rating`) as aggregate from `ratings` where `ratings`.`rateable_type` = 'App\\\\Models\\\\Vendor' and `ratings`.`rateable_id` = 1 and `ratings`.`rateable_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\Vendor", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 118}, {"index": 27, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 177}, {"index": 33, "namespace": "view", "name": "livewire.dashboard", "file": "C:\\xampp\\htdocs\\resources\\views/livewire/dashboard.blade.php", "line": 445}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/ComponentConcerns/RendersLivewireComponents.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}], "start": **********.325325, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Rateable.php:70", "source": {"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fwillvincent%2Flaravel-rateable%2Fsrc%2FRateable.php&line=70", "ajax": false, "filename": "Rateable.php", "line": "70"}, "connection": "mart", "explain": null, "start_percent": 54.715, "width_percent": 0.256}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 278}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 297}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 309}], "start": **********.326817, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:565", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=565", "ajax": false, "filename": "InteractsWithMedia.php", "line": "565"}, "connection": "mart", "explain": null, "start_percent": 54.971, "width_percent": 0.298}, {"sql": "select * from `media` where `media`.`model_id` in (2) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 278}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 297}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 309}], "start": **********.328467, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:565", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=565", "ajax": false, "filename": "InteractsWithMedia.php", "line": "565"}, "connection": "mart", "explain": null, "start_percent": 55.269, "width_percent": 0.245}, {"sql": "select * from `media` where `media`.`model_id` in (3) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 278}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 297}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 309}], "start": **********.330311, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:565", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=565", "ajax": false, "filename": "InteractsWithMedia.php", "line": "565"}, "connection": "mart", "explain": null, "start_percent": 55.514, "width_percent": 0.32}, {"sql": "select * from `media` where `media`.`model_id` in (4) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 278}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 297}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 309}], "start": **********.331606, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:565", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=565", "ajax": false, "filename": "InteractsWithMedia.php", "line": "565"}, "connection": "mart", "explain": null, "start_percent": 55.834, "width_percent": 0.266}, {"sql": "select * from `media` where `media`.`model_id` in (5) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 278}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 297}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 309}], "start": **********.33285, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:565", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=565", "ajax": false, "filename": "InteractsWithMedia.php", "line": "565"}, "connection": "mart", "explain": null, "start_percent": 56.1, "width_percent": 0.437}, {"sql": "select * from `media` where `media`.`model_id` in (6) and `media`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 278}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 297}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 309}], "start": **********.3345058, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:565", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=565", "ajax": false, "filename": "InteractsWithMedia.php", "line": "565"}, "connection": "mart", "explain": null, "start_percent": 56.537, "width_percent": 0.256}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\Product'", "type": "query", "params": [], "bindings": ["App\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 278}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 297}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 309}], "start": **********.336071, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:565", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=565", "ajax": false, "filename": "InteractsWithMedia.php", "line": "565"}, "connection": "mart", "explain": null, "start_percent": 56.793, "width_percent": 0.565}, {"sql": "select avg(`rating`) as aggregate from `ratings` where `ratings`.`rateable_type` = 'App\\\\Models\\\\Vendor' and `ratings`.`rateable_id` = 1 and `ratings`.`rateable_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\Vendor", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 118}, {"index": 27, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 177}, {"index": 38, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 39, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}], "start": **********.358254, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Rateable.php:70", "source": {"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fwillvincent%2Flaravel-rateable%2Fsrc%2FRateable.php&line=70", "ajax": false, "filename": "Rateable.php", "line": "70"}, "connection": "mart", "explain": null, "start_percent": 57.357, "width_percent": 0.511}, {"sql": "select * from `reviews` where `user_id` = 1 and `vendor_id` = 1 and `reviews`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 223}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.3595588, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Vendor.php:223", "source": {"index": 16, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 223}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=223", "ajax": false, "filename": "Vendor.php", "line": "223"}, "connection": "mart", "explain": null, "start_percent": 57.869, "width_percent": 0.266}, {"sql": "select `days`.*, `day_vendor`.`vendor_id` as `pivot_vendor_id`, `day_vendor`.`day_id` as `pivot_day_id`, `day_vendor`.`id` as `pivot_id`, `day_vendor`.`open` as `pivot_open`, `day_vendor`.`close` as `pivot_close` from `days` inner join `day_vendor` on `days`.`id` = `day_vendor`.`day_id` where `day_vendor`.`vendor_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 231}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 33, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 36, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.3611128, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Vendor.php:231", "source": {"index": 20, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 231}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=231", "ajax": false, "filename": "Vendor.php", "line": "231"}, "connection": "mart", "explain": null, "start_percent": 58.135, "width_percent": 0.437}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'mart' and table_name = 'vendors' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 185}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.362486, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "Vendor.php:185", "source": {"index": 14, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=185", "ajax": false, "filename": "Vendor.php", "line": "185"}, "connection": "mart", "explain": null, "start_percent": 58.572, "width_percent": 3.069}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'mart' and table_name = 'favourites' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 281}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.367386, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "Vendor.php:281", "source": {"index": 14, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 281}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=281", "ajax": false, "filename": "Vendor.php", "line": "281"}, "connection": "mart", "explain": null, "start_percent": 61.641, "width_percent": 2.77}, {"sql": "select * from `favourites` where `favourites`.`vendor_id` = 1 and `user_id` = 1 and `favourites`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 284}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 33, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 34, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 37, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.371878, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Vendor.php:284", "source": {"index": 21, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 284}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=284", "ajax": false, "filename": "Vendor.php", "line": "284"}, "connection": "mart", "explain": null, "start_percent": 64.411, "width_percent": 0.639}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\Vendor' and `document_requests`.`model_id` = 1 and `document_requests`.`model_id` is not null and `status` = 'requested' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Vendor", 1, "requested"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.374562, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:11", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=11", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "11"}, "connection": "mart", "explain": null, "start_percent": 65.051, "width_percent": 0.394}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\Vendor' and `document_requests`.`model_id` = 1 and `document_requests`.`model_id` is not null and `status` = 'pending' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Vendor", 1, "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.3758478, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:22", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=22", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "22"}, "connection": "mart", "explain": null, "start_percent": 65.445, "width_percent": 0.256}, {"sql": "select * from `media` where `media`.`model_id` in (3) and `media`.`model_type` = 'App\\\\Models\\\\VendorType'", "type": "query", "params": [], "bindings": ["App\\Models\\VendorType"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 278}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 297}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 309}], "start": **********.377541, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:565", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=565", "ajax": false, "filename": "InteractsWithMedia.php", "line": "565"}, "connection": "mart", "explain": null, "start_percent": 65.701, "width_percent": 0.362}, {"sql": "select count(*) as aggregate from `banners` where exists (select * from `vendors` where `banners`.`vendor_id` = `vendors`.`id` and `vendor_type_id` = 3 and `vendors`.`deleted_at` is null) and `banners`.`deleted_at` is null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/VendorType.php", "file": "C:\\xampp\\htdocs\\app\\Models\\VendorType.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-translatable/src/HasTranslations.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-translatable\\src\\HasTranslations.php", "line": 50}, {"index": 22, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\HasTranslations.php", "line": 16}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}], "start": **********.380097, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "VendorType.php:58", "source": {"index": 16, "namespace": null, "name": "app/Models/VendorType.php", "file": "C:\\xampp\\htdocs\\app\\Models\\VendorType.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendorType.php&line=58", "ajax": false, "filename": "VendorType.php", "line": "58"}, "connection": "mart", "explain": null, "start_percent": 66.063, "width_percent": 0.416}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\Favourite'", "type": "query", "params": [], "bindings": ["App\\Models\\Favourite"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 278}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 297}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 309}], "start": **********.382205, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:565", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=565", "ajax": false, "filename": "InteractsWithMedia.php", "line": "565"}, "connection": "mart", "explain": null, "start_percent": 66.478, "width_percent": 0.266}, {"sql": "select avg(`rating`) as aggregate from `ratings` where `ratings`.`rateable_type` = 'App\\\\Models\\\\Vendor' and `ratings`.`rateable_id` = 1 and `ratings`.`rateable_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\Vendor", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 118}, {"index": 27, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 177}, {"index": 38, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 39, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}], "start": **********.383849, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Rateable.php:70", "source": {"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fwillvincent%2Flaravel-rateable%2Fsrc%2FRateable.php&line=70", "ajax": false, "filename": "Rateable.php", "line": "70"}, "connection": "mart", "explain": null, "start_percent": 66.745, "width_percent": 0.256}, {"sql": "select * from `reviews` where `user_id` = 1 and `vendor_id` = 1 and `reviews`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 223}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.38495, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Vendor.php:223", "source": {"index": 16, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 223}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=223", "ajax": false, "filename": "Vendor.php", "line": "223"}, "connection": "mart", "explain": null, "start_percent": 67.001, "width_percent": 0.234}, {"sql": "select `days`.*, `day_vendor`.`vendor_id` as `pivot_vendor_id`, `day_vendor`.`day_id` as `pivot_day_id`, `day_vendor`.`id` as `pivot_id`, `day_vendor`.`open` as `pivot_open`, `day_vendor`.`close` as `pivot_close` from `days` inner join `day_vendor` on `days`.`id` = `day_vendor`.`day_id` where `day_vendor`.`vendor_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 231}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 33, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 36, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.386569, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Vendor.php:231", "source": {"index": 20, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 231}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=231", "ajax": false, "filename": "Vendor.php", "line": "231"}, "connection": "mart", "explain": null, "start_percent": 67.235, "width_percent": 0.394}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'mart' and table_name = 'vendors' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 185}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.38806, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "Vendor.php:185", "source": {"index": 14, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=185", "ajax": false, "filename": "Vendor.php", "line": "185"}, "connection": "mart", "explain": null, "start_percent": 67.629, "width_percent": 3.324}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'mart' and table_name = 'favourites' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 281}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.3925872, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "Vendor.php:281", "source": {"index": 14, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 281}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=281", "ajax": false, "filename": "Vendor.php", "line": "281"}, "connection": "mart", "explain": null, "start_percent": 70.954, "width_percent": 3.665}, {"sql": "select * from `favourites` where `favourites`.`vendor_id` = 1 and `user_id` = 1 and `favourites`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 284}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 33, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 34, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 37, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.3975542, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Vendor.php:284", "source": {"index": 21, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 284}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=284", "ajax": false, "filename": "Vendor.php", "line": "284"}, "connection": "mart", "explain": null, "start_percent": 74.619, "width_percent": 0.469}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\Vendor' and `document_requests`.`model_id` = 1 and `document_requests`.`model_id` is not null and `status` = 'requested' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Vendor", 1, "requested"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.399071, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:11", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=11", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "11"}, "connection": "mart", "explain": null, "start_percent": 75.088, "width_percent": 0.245}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\Vendor' and `document_requests`.`model_id` = 1 and `document_requests`.`model_id` is not null and `status` = 'pending' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Vendor", 1, "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.400422, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:22", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=22", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "22"}, "connection": "mart", "explain": null, "start_percent": 75.333, "width_percent": 0.288}, {"sql": "select * from `media` where `media`.`model_id` in (3) and `media`.`model_type` = 'App\\\\Models\\\\VendorType'", "type": "query", "params": [], "bindings": ["App\\Models\\VendorType"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 278}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 297}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 309}], "start": **********.40205, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:565", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=565", "ajax": false, "filename": "InteractsWithMedia.php", "line": "565"}, "connection": "mart", "explain": null, "start_percent": 75.621, "width_percent": 0.309}, {"sql": "select count(*) as aggregate from `banners` where exists (select * from `vendors` where `banners`.`vendor_id` = `vendors`.`id` and `vendor_type_id` = 3 and `vendors`.`deleted_at` is null) and `banners`.`deleted_at` is null", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/VendorType.php", "file": "C:\\xampp\\htdocs\\app\\Models\\VendorType.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-translatable/src/HasTranslations.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-translatable\\src\\HasTranslations.php", "line": 50}, {"index": 22, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\HasTranslations.php", "line": 16}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}], "start": **********.403825, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "VendorType.php:58", "source": {"index": 16, "namespace": null, "name": "app/Models/VendorType.php", "file": "C:\\xampp\\htdocs\\app\\Models\\VendorType.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendorType.php&line=58", "ajax": false, "filename": "VendorType.php", "line": "58"}, "connection": "mart", "explain": null, "start_percent": 75.93, "width_percent": 0.575}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\Favourite'", "type": "query", "params": [], "bindings": ["App\\Models\\Favourite"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 278}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 297}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 309}], "start": **********.406003, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:565", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=565", "ajax": false, "filename": "InteractsWithMedia.php", "line": "565"}, "connection": "mart", "explain": null, "start_percent": 76.505, "width_percent": 0.352}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 103}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 33, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 36, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.4080832, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "User.php:103", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=103", "ajax": false, "filename": "User.php", "line": "103"}, "connection": "mart", "explain": null, "start_percent": 76.857, "width_percent": 0.309}, {"sql": "select avg(`rating`) as aggregate from `ratings` where `ratings`.`rateable_type` = 'App\\\\Models\\\\User' and `ratings`.`rateable_id` = 1 and `ratings`.`rateable_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 118}, {"index": 27, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 118}, {"index": 38, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 39, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}], "start": **********.409782, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Rateable.php:70", "source": {"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fwillvincent%2Flaravel-rateable%2Fsrc%2FRateable.php&line=70", "ajax": false, "filename": "Rateable.php", "line": "70"}, "connection": "mart", "explain": null, "start_percent": 77.166, "width_percent": 0.266}, {"sql": "select count(*) as aggregate from `orders` where `driver_id` = 1 and (exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order' and `name` not in ('failed', 'cancelled', 'delivered') and `id` = (select max(id) from `statuses` where `model_type` = 'App\\\\Models\\\\Order' and `model_id` = `orders`.`id`)) or not exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order')) and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, "App\\Models\\Order", "failed", "cancelled", "delivered", "App\\Models\\Order", "App\\Models\\Order"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 125}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.4112542, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "User.php:125", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=125", "ajax": false, "filename": "User.php", "line": "125"}, "connection": "mart", "explain": null, "start_percent": 77.432, "width_percent": 0.522}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\User' and `document_requests`.`model_id` = 1 and `document_requests`.`model_id` is not null and `status` = 'requested' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1, "requested"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.430195, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:11", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=11", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "11"}, "connection": "mart", "explain": null, "start_percent": 77.954, "width_percent": 0.543}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\User' and `document_requests`.`model_id` = 1 and `document_requests`.`model_id` is not null and `status` = 'pending' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1, "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.4316902, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:22", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=22", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "22"}, "connection": "mart", "explain": null, "start_percent": 78.498, "width_percent": 0.479}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 2 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [2, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 103}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 33, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 36, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.43502, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "User.php:103", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=103", "ajax": false, "filename": "User.php", "line": "103"}, "connection": "mart", "explain": null, "start_percent": 78.977, "width_percent": 0.437}, {"sql": "select avg(`rating`) as aggregate from `ratings` where `ratings`.`rateable_type` = 'App\\\\Models\\\\User' and `ratings`.`rateable_id` = 2 and `ratings`.`rateable_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 118}, {"index": 27, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 118}, {"index": 38, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 39, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}], "start": **********.437064, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Rateable.php:70", "source": {"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fwillvincent%2Flaravel-rateable%2Fsrc%2FRateable.php&line=70", "ajax": false, "filename": "Rateable.php", "line": "70"}, "connection": "mart", "explain": null, "start_percent": 79.414, "width_percent": 0.543}, {"sql": "select count(*) as aggregate from `orders` where `driver_id` = 2 and (exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order' and `name` not in ('failed', 'cancelled', 'delivered') and `id` = (select max(id) from `statuses` where `model_type` = 'App\\\\Models\\\\Order' and `model_id` = `orders`.`id`)) or not exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order')) and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [2, "App\\Models\\Order", "failed", "cancelled", "delivered", "App\\Models\\Order", "App\\Models\\Order"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 125}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.4388962, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "User.php:125", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=125", "ajax": false, "filename": "User.php", "line": "125"}, "connection": "mart", "explain": null, "start_percent": 79.957, "width_percent": 0.479}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\User' and `document_requests`.`model_id` = 2 and `document_requests`.`model_id` is not null and `status` = 'requested' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 2, "requested"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.441326, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:11", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=11", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "11"}, "connection": "mart", "explain": null, "start_percent": 80.437, "width_percent": 0.437}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\User' and `document_requests`.`model_id` = 2 and `document_requests`.`model_id` is not null and `status` = 'pending' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 2, "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.442877, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:22", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=22", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "22"}, "connection": "mart", "explain": null, "start_percent": 80.874, "width_percent": 0.394}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 3 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [3, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 103}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 33, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 36, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.444984, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "User.php:103", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=103", "ajax": false, "filename": "User.php", "line": "103"}, "connection": "mart", "explain": null, "start_percent": 81.268, "width_percent": 0.341}, {"sql": "select avg(`rating`) as aggregate from `ratings` where `ratings`.`rateable_type` = 'App\\\\Models\\\\User' and `ratings`.`rateable_id` = 3 and `ratings`.`rateable_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 3], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 118}, {"index": 27, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 118}, {"index": 38, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 39, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}], "start": **********.446583, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Rateable.php:70", "source": {"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fwillvincent%2Flaravel-rateable%2Fsrc%2FRateable.php&line=70", "ajax": false, "filename": "Rateable.php", "line": "70"}, "connection": "mart", "explain": null, "start_percent": 81.609, "width_percent": 0.234}, {"sql": "select count(*) as aggregate from `orders` where `driver_id` = 3 and (exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order' and `name` not in ('failed', 'cancelled', 'delivered') and `id` = (select max(id) from `statuses` where `model_type` = 'App\\\\Models\\\\Order' and `model_id` = `orders`.`id`)) or not exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order')) and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [3, "App\\Models\\Order", "failed", "cancelled", "delivered", "App\\Models\\Order", "App\\Models\\Order"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 125}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.447964, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "User.php:125", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=125", "ajax": false, "filename": "User.php", "line": "125"}, "connection": "mart", "explain": null, "start_percent": 81.843, "width_percent": 0.373}, {"sql": "select * from `driver_types` where `driver_types`.`driver_id` = 3 and `driver_types`.`driver_id` is not null limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/DriverAttributeTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DriverAttributeTrait.php", "line": 11}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 33, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 34, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 37, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.449842, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "DriverAttributeTrait.php:11", "source": {"index": 21, "namespace": null, "name": "app/Traits/DriverAttributeTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DriverAttributeTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDriverAttributeTrait.php&line=11", "ajax": false, "filename": "DriverAttributeTrait.php", "line": "11"}, "connection": "mart", "explain": null, "start_percent": 82.216, "width_percent": 0.309}, {"sql": "select * from `vehicles` where `vehicles`.`driver_id` = 3 and `vehicles`.`driver_id` is not null and `is_active` = 1 and `verified` = 1 limit 1", "type": "query", "params": [], "bindings": [3, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/DriverAttributeTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DriverAttributeTrait.php", "line": 13}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 33, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 34, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 37, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.451193, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "DriverAttributeTrait.php:13", "source": {"index": 21, "namespace": null, "name": "app/Traits/DriverAttributeTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DriverAttributeTrait.php", "line": 13}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDriverAttributeTrait.php&line=13", "ajax": false, "filename": "DriverAttributeTrait.php", "line": "13"}, "connection": "mart", "explain": null, "start_percent": 82.525, "width_percent": 0.288}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\User' and `document_requests`.`model_id` = 3 and `document_requests`.`model_id` is not null and `status` = 'requested' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 3, "requested"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.45243, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:11", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=11", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "11"}, "connection": "mart", "explain": null, "start_percent": 82.813, "width_percent": 0.245}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\User' and `document_requests`.`model_id` = 3 and `document_requests`.`model_id` is not null and `status` = 'pending' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 3, "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.453946, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:22", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=22", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "22"}, "connection": "mart", "explain": null, "start_percent": 83.058, "width_percent": 0.49}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 4 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [4, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 103}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 33, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 36, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.455949, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "User.php:103", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=103", "ajax": false, "filename": "User.php", "line": "103"}, "connection": "mart", "explain": null, "start_percent": 83.548, "width_percent": 0.33}, {"sql": "select avg(`rating`) as aggregate from `ratings` where `ratings`.`rateable_type` = 'App\\\\Models\\\\User' and `ratings`.`rateable_id` = 4 and `ratings`.`rateable_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 4], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 118}, {"index": 27, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 118}, {"index": 38, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 39, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}], "start": **********.4577348, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Rateable.php:70", "source": {"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fwillvincent%2Flaravel-rateable%2Fsrc%2FRateable.php&line=70", "ajax": false, "filename": "Rateable.php", "line": "70"}, "connection": "mart", "explain": null, "start_percent": 83.879, "width_percent": 0.266}, {"sql": "select count(*) as aggregate from `orders` where `driver_id` = 4 and (exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order' and `name` not in ('failed', 'cancelled', 'delivered') and `id` = (select max(id) from `statuses` where `model_type` = 'App\\\\Models\\\\Order' and `model_id` = `orders`.`id`)) or not exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order')) and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [4, "App\\Models\\Order", "failed", "cancelled", "delivered", "App\\Models\\Order", "App\\Models\\Order"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 125}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.459221, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "User.php:125", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=125", "ajax": false, "filename": "User.php", "line": "125"}, "connection": "mart", "explain": null, "start_percent": 84.145, "width_percent": 0.384}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\User' and `document_requests`.`model_id` = 4 and `document_requests`.`model_id` is not null and `status` = 'requested' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 4, "requested"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.4607759, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:11", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=11", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "11"}, "connection": "mart", "explain": null, "start_percent": 84.529, "width_percent": 0.266}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\User' and `document_requests`.`model_id` = 4 and `document_requests`.`model_id` is not null and `status` = 'pending' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 4, "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.462035, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:22", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=22", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "22"}, "connection": "mart", "explain": null, "start_percent": 84.795, "width_percent": 0.234}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 5 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [5, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 103}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 33, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 36, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.46379, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "User.php:103", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=103", "ajax": false, "filename": "User.php", "line": "103"}, "connection": "mart", "explain": null, "start_percent": 85.029, "width_percent": 0.362}, {"sql": "select avg(`rating`) as aggregate from `ratings` where `ratings`.`rateable_type` = 'App\\\\Models\\\\User' and `ratings`.`rateable_id` = 5 and `ratings`.`rateable_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 5], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 118}, {"index": 27, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 118}, {"index": 38, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 39, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}], "start": **********.4651508, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Rateable.php:70", "source": {"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fwillvincent%2Flaravel-rateable%2Fsrc%2FRateable.php&line=70", "ajax": false, "filename": "Rateable.php", "line": "70"}, "connection": "mart", "explain": null, "start_percent": 85.392, "width_percent": 0.256}, {"sql": "select count(*) as aggregate from `orders` where `driver_id` = 5 and (exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order' and `name` not in ('failed', 'cancelled', 'delivered') and `id` = (select max(id) from `statuses` where `model_type` = 'App\\\\Models\\\\Order' and `model_id` = `orders`.`id`)) or not exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order')) and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [5, "App\\Models\\Order", "failed", "cancelled", "delivered", "App\\Models\\Order", "App\\Models\\Order"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 125}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.466328, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "User.php:125", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=125", "ajax": false, "filename": "User.php", "line": "125"}, "connection": "mart", "explain": null, "start_percent": 85.647, "width_percent": 0.405}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\User' and `document_requests`.`model_id` = 5 and `document_requests`.`model_id` is not null and `status` = 'requested' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 5, "requested"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.4677281, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:11", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=11", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "11"}, "connection": "mart", "explain": null, "start_percent": 86.052, "width_percent": 0.298}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\User' and `document_requests`.`model_id` = 5 and `document_requests`.`model_id` is not null and `status` = 'pending' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 5, "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.468834, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:22", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=22", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "22"}, "connection": "mart", "explain": null, "start_percent": 86.351, "width_percent": 0.234}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 6 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [6, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 103}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 33, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 36, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.470403, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "User.php:103", "source": {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=103", "ajax": false, "filename": "User.php", "line": "103"}, "connection": "mart", "explain": null, "start_percent": 86.585, "width_percent": 0.554}, {"sql": "select avg(`rating`) as aggregate from `ratings` where `ratings`.`rateable_type` = 'App\\\\Models\\\\User' and `ratings`.`rateable_id` = 6 and `ratings`.`rateable_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\User", 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 118}, {"index": 27, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 118}, {"index": 38, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 39, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}], "start": **********.4720309, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Rateable.php:70", "source": {"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fwillvincent%2Flaravel-rateable%2Fsrc%2FRateable.php&line=70", "ajax": false, "filename": "Rateable.php", "line": "70"}, "connection": "mart", "explain": null, "start_percent": 87.139, "width_percent": 0.384}, {"sql": "select count(*) as aggregate from `orders` where `driver_id` = 6 and (exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order' and `name` not in ('failed', 'cancelled', 'delivered') and `id` = (select max(id) from `statuses` where `model_type` = 'App\\\\Models\\\\Order' and `model_id` = `orders`.`id`)) or not exists (select * from `statuses` where `orders`.`id` = `statuses`.`model_id` and `statuses`.`model_type` = 'App\\\\Models\\\\Order')) and `orders`.`deleted_at` is null", "type": "query", "params": [], "bindings": [6, "App\\Models\\Order", "failed", "cancelled", "delivered", "App\\Models\\Order", "App\\Models\\Order"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 125}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.4734762, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "User.php:125", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\xampp\\htdocs\\app\\Models\\User.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=125", "ajax": false, "filename": "User.php", "line": "125"}, "connection": "mart", "explain": null, "start_percent": 87.523, "width_percent": 0.501}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\User' and `document_requests`.`model_id` = 6 and `document_requests`.`model_id` is not null and `status` = 'requested' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 6, "requested"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.4749918, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:11", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=11", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "11"}, "connection": "mart", "explain": null, "start_percent": 88.023, "width_percent": 0.266}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\User' and `document_requests`.`model_id` = 6 and `document_requests`.`model_id` is not null and `status` = 'pending' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 6, "pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/LifecycleManager.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 154}], "start": **********.476058, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:22", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=22", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "22"}, "connection": "mart", "explain": null, "start_percent": 88.29, "width_percent": 0.245}, {"sql": "select * from `favourites` where `favourites`.`product_id` = 1 and `user_id` = 1 and `favourites`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Product.php", "line": 196}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-translatable/src/HasTranslations.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-translatable\\src\\HasTranslations.php", "line": 50}, {"index": 27, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\HasTranslations.php", "line": 16}, {"index": 34, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}], "start": **********.4779131, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Product.php:196", "source": {"index": 21, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Product.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FProduct.php&line=196", "ajax": false, "filename": "Product.php", "line": "196"}, "connection": "mart", "explain": null, "start_percent": 88.535, "width_percent": 0.33}, {"sql": "select avg(`rating`) as aggregate from `product_reviews` where `product_reviews`.`product_id` = 1 and `product_reviews`.`product_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Product.php", "line": 186}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-translatable/src/HasTranslations.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-translatable\\src\\HasTranslations.php", "line": 50}, {"index": 25, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\HasTranslations.php", "line": 16}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 33, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}], "start": **********.479054, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Product.php:186", "source": {"index": 19, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Product.php", "line": 186}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FProduct.php&line=186", "ajax": false, "filename": "Product.php", "line": "186"}, "connection": "mart", "explain": null, "start_percent": 88.865, "width_percent": 0.224}, {"sql": "select `option_group_id` from `options` where exists (select * from `products` inner join `option_product` on `products`.`id` = `option_product`.`product_id` where `options`.`id` = `option_product`.`option_id` and `product_id` = 1 and `products`.`deleted_at` is null) and `is_active` = 1 and `options`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Product.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-translatable/src/HasTranslations.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-translatable\\src\\HasTranslations.php", "line": 50}, {"index": 20, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\HasTranslations.php", "line": 16}, {"index": 27, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}], "start": **********.480653, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Product.php:159", "source": {"index": 14, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Product.php", "line": 159}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FProduct.php&line=159", "ajax": false, "filename": "Product.php", "line": "159"}, "connection": "mart", "explain": null, "start_percent": 89.089, "width_percent": 0.416}, {"sql": "select * from `option_groups` where 0 = 1 and `is_active` = 1 and `option_groups`.`deleted_at` is null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Product.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/spatie/laravel-translatable/src/HasTranslations.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-translatable\\src\\HasTranslations.php", "line": 50}, {"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\HasTranslations.php", "line": 16}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}], "start": **********.482226, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Product.php:170", "source": {"index": 15, "namespace": null, "name": "app/Models/Product.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Product.php", "line": 170}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FProduct.php&line=170", "ajax": false, "filename": "Product.php", "line": "170"}, "connection": "mart", "explain": null, "start_percent": 89.505, "width_percent": 0.256}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\Vendor'", "type": "query", "params": [], "bindings": ["App\\Models\\Vendor"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 278}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 297}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 309}], "start": **********.484676, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:565", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 565}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=565", "ajax": false, "filename": "InteractsWithMedia.php", "line": "565"}, "connection": "mart", "explain": null, "start_percent": 89.76, "width_percent": 0.448}, {"sql": "select avg(`rating`) as aggregate from `ratings` where `ratings`.`rateable_type` = 'App\\\\Models\\\\Vendor' and `ratings`.`rateable_id` = 1 and `ratings`.`rateable_id` is not null", "type": "query", "params": [], "bindings": ["App\\Models\\Vendor", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, {"index": 20, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 118}, {"index": 27, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 177}, {"index": 34, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\HasTranslations.php", "line": 16}, {"index": 41, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}], "start": **********.4860148, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Rateable.php:70", "source": {"index": 19, "namespace": null, "name": "vendor/willvincent/laravel-rateable/src/Rateable.php", "file": "C:\\xampp\\htdocs\\vendor\\willvincent\\laravel-rateable\\src\\Rateable.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fwillvincent%2Flaravel-rateable%2Fsrc%2FRateable.php&line=70", "ajax": false, "filename": "Rateable.php", "line": "70"}, "connection": "mart", "explain": null, "start_percent": 90.208, "width_percent": 0.416}, {"sql": "select * from `reviews` where `user_id` = 1 and `vendor_id` = 1 and `reviews`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 223}, {"index": 23, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\HasTranslations.php", "line": 16}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 31, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 32, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}], "start": **********.487365, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Vendor.php:223", "source": {"index": 16, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 223}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=223", "ajax": false, "filename": "Vendor.php", "line": "223"}, "connection": "mart", "explain": null, "start_percent": 90.623, "width_percent": 0.426}, {"sql": "select `days`.*, `day_vendor`.`vendor_id` as `pivot_vendor_id`, `day_vendor`.`day_id` as `pivot_day_id`, `day_vendor`.`id` as `pivot_id`, `day_vendor`.`open` as `pivot_open`, `day_vendor`.`close` as `pivot_close` from `days` inner join `day_vendor` on `days`.`id` = `day_vendor`.`day_id` where `day_vendor`.`vendor_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 231}, {"index": 27, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\HasTranslations.php", "line": 16}, {"index": 34, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 36, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}], "start": **********.4889421, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Vendor.php:231", "source": {"index": 20, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 231}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=231", "ajax": false, "filename": "Vendor.php", "line": "231"}, "connection": "mart", "explain": null, "start_percent": 91.05, "width_percent": 0.33}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'mart' and table_name = 'vendors' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 185}, {"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\HasTranslations.php", "line": 16}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}], "start": **********.4903522, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "Vendor.php:185", "source": {"index": 14, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=185", "ajax": false, "filename": "Vendor.php", "line": "185"}, "connection": "mart", "explain": null, "start_percent": 91.38, "width_percent": 3.133}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, extra as `extra` from information_schema.columns where table_schema = 'mart' and table_name = 'favourites' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 281}, {"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\HasTranslations.php", "line": 16}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 29, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}], "start": **********.494579, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "Vendor.php:281", "source": {"index": 14, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 281}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=281", "ajax": false, "filename": "Vendor.php", "line": "281"}, "connection": "mart", "explain": null, "start_percent": 94.513, "width_percent": 2.962}, {"sql": "select * from `favourites` where `favourites`.`vendor_id` = 1 and `user_id` = 1 and `favourites`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 284}, {"index": 28, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\HasTranslations.php", "line": 16}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 36, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 37, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}], "start": **********.498773, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Vendor.php:284", "source": {"index": 21, "namespace": null, "name": "app/Models/Vendor.php", "file": "C:\\xampp\\htdocs\\app\\Models\\Vendor.php", "line": 284}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=284", "ajax": false, "filename": "Vendor.php", "line": "284"}, "connection": "mart", "explain": null, "start_percent": 97.475, "width_percent": 0.469}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = 'App\\\\Models\\\\Vendor' and `document_requests`.`model_id` = 1 and `document_requests`.`model_id` is not null and `status` = 'requested' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Vendor", 1, "requested"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, {"index": 26, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\HasTranslations.php", "line": 16}, {"index": 33, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 244}, {"index": 34, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 237}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/HydrationMiddleware/HydratePublicProperties.php", "file": "C:\\xampp\\htdocs\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\HydratePublicProperties.php", "line": 114}], "start": **********.500009, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DocumentRequestTrait.php:11", "source": {"index": 19, "namespace": null, "name": "app/Traits/DocumentRequestTrait.php", "file": "C:\\xampp\\htdocs\\app\\Traits\\DocumentRequestTrait.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FTraits%2FDocumentRequestTrait.php&line=11", "ajax": false, "filename": "DocumentRequestTrait.php", "line": "11"}, "connection": "mart", "explain": null, "start_percent": 97.944, "width_percent": 0.384}, {"sql": "select * from `document_requests` where `document_requests`.`model_type` = ? and `document_requests`.`model_id` = ? and `document_requests`.`model_id` is not null and `status` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.501134, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mart", "explain": null, "start_percent": 98.327, "width_percent": 0.234}, {"sql": "select * from `media` where `media`.`model_id` in (3) and `media`.`model_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5017831, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mart", "explain": null, "start_percent": 98.562, "width_percent": 0.298}, {"sql": "select count(*) as aggregate from `banners` where exists (select * from `vendors` where `banners`.`vendor_id` = `vendors`.`id` and `vendor_type_id` = ? and `vendors`.`deleted_at` is null) and `banners`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.502766, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mart", "explain": null, "start_percent": 98.86, "width_percent": 0.458}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.504049, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mart", "explain": null, "start_percent": 99.318, "width_percent": 0.682}]}, "models": {"data": {"Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": {"value": 11, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FModels%2FMedia.php&line=1", "ajax": false, "filename": "Media.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Vendor": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendor.php&line=1", "ajax": false, "filename": "Vendor.php", "line": "?"}}, "App\\Models\\VendorType": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FVendorType.php&line=1", "ajax": false, "filename": "VendorType.php", "line": "?"}}, "App\\Models\\Favourite": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FFavourite.php&line=1", "ajax": false, "filename": "Favourite.php", "line": "?"}}, "App\\Models\\Product": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}}, "count": 38, "is_counter": true}, "livewire": {"data": {"dashboard-livewire #1W8rYiprddVIVFEQTPvU": "array:5 [\n  \"data\" => array:6 [\n    \"topSellingVendors\" => Illuminate\\Database\\Eloquent\\Collection {#2976\n      #items: array:1 [\n        0 => App\\Models\\Vendor {#3331\n          #connection: \"mysql\"\n          #table: \"vendors\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: array:2 [\n            0 => \"vendor_type\"\n            1 => \"fees\"\n          ]\n          #withCount: array:1 [\n            0 => \"reviews\"\n          ]\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:39 [\n            \"id\" => 1\n            \"name\" => \"Nafiss Market\"\n            \"description\" => \"<p>Nafiss demo market</p>\"\n            \"base_delivery_fee\" => 0.0\n            \"delivery_fee\" => 0.0\n            \"delivery_range\" => null\n            \"tax\" => null\n            \"phone\" => \"+***********\"\n            \"email\" => \"<EMAIL>\"\n            \"address\" => \"Doha\"\n            \"latitude\" => \"0\"\n            \"longitude\" => \"0\"\n            \"commission\" => 5.0\n            \"pickup\" => 1\n            \"delivery\" => 1\n            \"is_active\" => 1\n            \"charge_per_km\" => null\n            \"is_open\" => 1\n            \"vendor_type_id\" => 3\n            \"auto_assignment\" => 1\n            \"auto_accept\" => 1\n            \"allow_schedule_order\" => 0\n            \"has_sub_categories\" => 0\n            \"min_order\" => 50.0\n            \"max_order\" => 1000.0\n            \"use_subscription\" => 0\n            \"has_drivers\" => 1\n            \"prepare_time\" => null\n            \"prepare_time_unit\" => \"minutes\"\n            \"delivery_time\" => null\n            \"delivery_time_unit\" => \"minutes\"\n            \"in_order\" => 1\n            \"featured\" => 1\n            \"created_at\" => \"2025-06-09 23:38:38\"\n            \"updated_at\" => \"2025-06-09 23:38:38\"\n            \"deleted_at\" => null\n            \"creator_id\" => 1\n            \"reviews_count\" => 0\n            \"successful_sales_count\" => 0\n          ]\n          #original: array:39 [\n            \"id\" => 1\n            \"name\" => \"Nafiss Market\"\n            \"description\" => \"<p>Nafiss demo market</p>\"\n            \"base_delivery_fee\" => 0.0\n            \"delivery_fee\" => 0.0\n            \"delivery_range\" => null\n            \"tax\" => null\n            \"phone\" => \"+***********\"\n            \"email\" => \"<EMAIL>\"\n            \"address\" => \"Doha\"\n            \"latitude\" => \"0\"\n            \"longitude\" => \"0\"\n            \"commission\" => 5.0\n            \"pickup\" => 1\n            \"delivery\" => 1\n            \"is_active\" => 1\n            \"charge_per_km\" => null\n            \"is_open\" => 1\n            \"vendor_type_id\" => 3\n            \"auto_assignment\" => 1\n            \"auto_accept\" => 1\n            \"allow_schedule_order\" => 0\n            \"has_sub_categories\" => 0\n            \"min_order\" => 50.0\n            \"max_order\" => 1000.0\n            \"use_subscription\" => 0\n            \"has_drivers\" => 1\n            \"prepare_time\" => null\n            \"prepare_time_unit\" => \"minutes\"\n            \"delivery_time\" => null\n            \"delivery_time_unit\" => \"minutes\"\n            \"in_order\" => 1\n            \"featured\" => 1\n            \"created_at\" => \"2025-06-09 23:38:38\"\n            \"updated_at\" => \"2025-06-09 23:38:38\"\n            \"deleted_at\" => null\n            \"creator_id\" => 1\n            \"reviews_count\" => 0\n            \"successful_sales_count\" => 0\n          ]\n          #changes: []\n          #casts: array:12 [\n            \"id\" => \"integer\"\n            \"allow_schedule_order\" => \"boolean\"\n            \"has_sub_categories\" => \"boolean\"\n            \"has_subscription\" => \"boolean\"\n            \"use_subscription\" => \"boolean\"\n            \"vendor_type_id\" => \"int\"\n            \"pickup\" => \"int\"\n            \"delivery\" => \"int\"\n            \"is_active\" => \"int\"\n            \"reviews_count\" => \"int\"\n            \"is_open\" => \"boolean\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: array:12 [\n            0 => \"formatted_date\"\n            1 => \"logo\"\n            2 => \"feature_image\"\n            3 => \"rating\"\n            4 => \"can_rate\"\n            5 => \"slots\"\n            6 => \"is_package_vendor\"\n            7 => \"is_favourite\"\n            8 => \"has_subscription\"\n            9 => \"document_requested\"\n            10 => \"pending_document_approval\"\n            11 => \"description_url\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"vendor_type\" => App\\Models\\VendorType {#3292\n              #connection: \"mysql\"\n              #table: \"vendor_types\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 3\n                \"name\" => \"{\"en\":\"Grocery\"}\"\n                \"color\" => \"#45b12f\"\n                \"description\" => \"{\"en\":\"Buy grocery from your nearby markets\"}\"\n                \"slug\" => \"grocery\"\n                \"is_active\" => 1\n                \"in_order\" => 3\n                \"created_at\" => \"2021-06-30 16:59:15\"\n                \"updated_at\" => \"2024-05-01 13:50:34\"\n                \"deleted_at\" => null\n              ]\n              #original: array:10 [\n                \"id\" => 3\n                \"name\" => \"{\"en\":\"Grocery\"}\"\n                \"color\" => \"#45b12f\"\n                \"description\" => \"{\"en\":\"Buy grocery from your nearby markets\"}\"\n                \"slug\" => \"grocery\"\n                \"is_active\" => 1\n                \"in_order\" => 3\n                \"created_at\" => \"2021-06-30 16:59:15\"\n                \"updated_at\" => \"2024-05-01 13:50:34\"\n                \"deleted_at\" => null\n              ]\n              #changes: []\n              #casts: array:5 [\n                \"id\" => \"int\"\n                \"is_active\" => \"int\"\n                \"deleted_at\" => \"datetime\"\n                \"name\" => \"array\"\n                \"description\" => \"array\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: array:4 [\n                0 => \"formatted_date\"\n                1 => \"logo\"\n                2 => \"website_header\"\n                3 => \"has_banners\"\n              ]\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: array:1 [\n                0 => \"media\"\n              ]\n              #visible: []\n              #fillable: array:5 [\n                0 => \"name\"\n                1 => \"description\"\n                2 => \"slug\"\n                3 => \"is_active\"\n                4 => \"color\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              +mediaConversions: []\n              +mediaCollections: []\n              #deletePreservingMedia: false\n              #unAttachedMediaLibraryItems: []\n              #forceDeleting: false\n              +translatable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #translationLocale: null\n            }\n            \"fees\" => Illuminate\\Database\\Eloquent\\Collection {#3340\n              #items: []\n              #escapeWhenCastingToString: false\n            }\n            \"media\" => Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection {#2785\n              #items: array:2 [\n                0 => Spatie\\MediaLibrary\\MediaCollections\\Models\\Media {#2789\n                  #connection: \"mysql\"\n                  #table: \"media\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: true\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:18 [\n                    \"id\" => 32\n                    \"model_type\" => \"App\\Models\\Vendor\"\n                    \"model_id\" => 1\n                    \"uuid\" => \"9d68ed2c-51ee-4d12-a08e-d4f5e622b25c\"\n                    \"collection_name\" => \"logo\"\n                    \"name\" => \"QxrUuXyLKDrufppaf1W9jYsJw47NMZ-metaU2NyZWVuc2hvdF8yMDI1MDYwOV8wNjE2MDkuanBn-\"\n                    \"file_name\" => \"FWxWJ-1749490719.jpg\"\n                    \"mime_type\" => \"image/jpeg\"\n                    \"disk\" => \"public\"\n                    \"conversions_disk\" => \"public\"\n                    \"size\" => 1027868\n                    \"manipulations\" => \"[]\"\n                    \"custom_properties\" => \"[]\"\n                    \"responsive_images\" => \"[]\"\n                    \"order_column\" => 1\n                    \"created_at\" => \"2025-06-09 23:38:39\"\n                    \"updated_at\" => \"2025-06-09 23:38:39\"\n                    \"generated_conversions\" => \"[]\"\n                  ]\n                  #original: array:18 [\n                    \"id\" => 32\n                    \"model_type\" => \"App\\Models\\Vendor\"\n                    \"model_id\" => 1\n                    \"uuid\" => \"9d68ed2c-51ee-4d12-a08e-d4f5e622b25c\"\n                    \"collection_name\" => \"logo\"\n                    \"name\" => \"QxrUuXyLKDrufppaf1W9jYsJw47NMZ-metaU2NyZWVuc2hvdF8yMDI1MDYwOV8wNjE2MDkuanBn-\"\n                    \"file_name\" => \"FWxWJ-1749490719.jpg\"\n                    \"mime_type\" => \"image/jpeg\"\n                    \"disk\" => \"public\"\n                    \"conversions_disk\" => \"public\"\n                    \"size\" => 1027868\n                    \"manipulations\" => \"[]\"\n                    \"custom_properties\" => \"[]\"\n                    \"responsive_images\" => \"[]\"\n                    \"order_column\" => 1\n                    \"created_at\" => \"2025-06-09 23:38:39\"\n                    \"updated_at\" => \"2025-06-09 23:38:39\"\n                    \"generated_conversions\" => \"[]\"\n                  ]\n                  #changes: []\n                  #casts: array:4 [\n                    \"manipulations\" => \"array\"\n                    \"custom_properties\" => \"array\"\n                    \"generated_conversions\" => \"array\"\n                    \"responsive_images\" => \"array\"\n                  ]\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: array:2 [\n                    0 => \"original_url\"\n                    1 => \"preview_url\"\n                  ]\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  +timestamps: true\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  #streamChunkSize: 1048576\n                }\n                1 => Spatie\\MediaLibrary\\MediaCollections\\Models\\Media {#2766\n                  #connection: \"mysql\"\n                  #table: \"media\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: true\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:18 [\n                    \"id\" => 33\n                    \"model_type\" => \"App\\Models\\Vendor\"\n                    \"model_id\" => 1\n                    \"uuid\" => \"0097f452-**************-d58fc3a4a761\"\n                    \"collection_name\" => \"feature_image\"\n                    \"name\" => \"EthLlaKBBzxl2cOWYyihv6b11bVJKO-metaU2NyZWVuc2hvdF8yMDI1MDYwOV8wNjE2MDkuanBn-\"\n                    \"file_name\" => \"deNKU-1749490719.jpg\"\n                    \"mime_type\" => \"image/jpeg\"\n                    \"disk\" => \"public\"\n                    \"conversions_disk\" => \"public\"\n                    \"size\" => 1027868\n                    \"manipulations\" => \"[]\"\n                    \"custom_properties\" => \"[]\"\n                    \"responsive_images\" => \"[]\"\n                    \"order_column\" => 2\n                    \"created_at\" => \"2025-06-09 23:38:39\"\n                    \"updated_at\" => \"2025-06-09 23:38:39\"\n                    \"generated_conversions\" => \"[]\"\n                  ]\n                  #original: array:18 [\n                    \"id\" => 33\n                    \"model_type\" => \"App\\Models\\Vendor\"\n                    \"model_id\" => 1\n                    \"uuid\" => \"0097f452-**************-d58fc3a4a761\"\n                    \"collection_name\" => \"feature_image\"\n                    \"name\" => \"EthLlaKBBzxl2cOWYyihv6b11bVJKO-metaU2NyZWVuc2hvdF8yMDI1MDYwOV8wNjE2MDkuanBn-\"\n                    \"file_name\" => \"deNKU-1749490719.jpg\"\n                    \"mime_type\" => \"image/jpeg\"\n                    \"disk\" => \"public\"\n                    \"conversions_disk\" => \"public\"\n                    \"size\" => 1027868\n                    \"manipulations\" => \"[]\"\n                    \"custom_properties\" => \"[]\"\n                    \"responsive_images\" => \"[]\"\n                    \"order_column\" => 2\n                    \"created_at\" => \"2025-06-09 23:38:39\"\n                    \"updated_at\" => \"2025-06-09 23:38:39\"\n                    \"generated_conversions\" => \"[]\"\n                  ]\n                  #changes: []\n                  #casts: array:4 [\n                    \"manipulations\" => \"array\"\n                    \"custom_properties\" => \"array\"\n                    \"generated_conversions\" => \"array\"\n                    \"responsive_images\" => \"array\"\n                  ]\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: array:2 [\n                    0 => \"original_url\"\n                    1 => \"preview_url\"\n                  ]\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  +timestamps: true\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  #streamChunkSize: 1048576\n                }\n              ]\n              #escapeWhenCastingToString: false\n              +collectionName: null\n              +formFieldName: null\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: array:1 [\n            0 => \"media\"\n          ]\n          #visible: []\n          #fillable: array:18 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"description\"\n            3 => \"delivery_fee\"\n            4 => \"delivery_range\"\n            5 => \"tax\"\n            6 => \"phone\"\n            7 => \"email\"\n            8 => \"address\"\n            9 => \"latitude\"\n            10 => \"longitude\"\n            11 => \"commission\"\n            12 => \"pickup\"\n            13 => \"delivery\"\n            14 => \"is_active\"\n            15 => \"charge_per_km\"\n            16 => \"is_open\"\n            17 => \"vendor_type_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n          #forceDeleting: false\n          #geographical_options: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"topRatedVendors\" => Illuminate\\Database\\Eloquent\\Collection {#2994\n      #items: array:1 [\n        0 => App\\Models\\Vendor {#3257\n          #connection: \"mysql\"\n          #table: \"vendors\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: array:2 [\n            0 => \"vendor_type\"\n            1 => \"fees\"\n          ]\n          #withCount: array:1 [\n            0 => \"reviews\"\n          ]\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:39 [\n            \"id\" => 1\n            \"name\" => \"Nafiss Market\"\n            \"description\" => \"<p>Nafiss demo market</p>\"\n            \"base_delivery_fee\" => 0.0\n            \"delivery_fee\" => 0.0\n            \"delivery_range\" => null\n            \"tax\" => null\n            \"phone\" => \"+***********\"\n            \"email\" => \"<EMAIL>\"\n            \"address\" => \"Doha\"\n            \"latitude\" => \"0\"\n            \"longitude\" => \"0\"\n            \"commission\" => 5.0\n            \"pickup\" => 1\n            \"delivery\" => 1\n            \"is_active\" => 1\n            \"charge_per_km\" => null\n            \"is_open\" => 1\n            \"vendor_type_id\" => 3\n            \"auto_assignment\" => 1\n            \"auto_accept\" => 1\n            \"allow_schedule_order\" => 0\n            \"has_sub_categories\" => 0\n            \"min_order\" => 50.0\n            \"max_order\" => 1000.0\n            \"use_subscription\" => 0\n            \"has_drivers\" => 1\n            \"prepare_time\" => null\n            \"prepare_time_unit\" => \"minutes\"\n            \"delivery_time\" => null\n            \"delivery_time_unit\" => \"minutes\"\n            \"in_order\" => 1\n            \"featured\" => 1\n            \"created_at\" => \"2025-06-09 23:38:38\"\n            \"updated_at\" => \"2025-06-09 23:38:38\"\n            \"deleted_at\" => null\n            \"creator_id\" => 1\n            \"reviews_count\" => 0\n            \"ratings_count\" => 0\n          ]\n          #original: array:39 [\n            \"id\" => 1\n            \"name\" => \"Nafiss Market\"\n            \"description\" => \"<p>Nafiss demo market</p>\"\n            \"base_delivery_fee\" => 0.0\n            \"delivery_fee\" => 0.0\n            \"delivery_range\" => null\n            \"tax\" => null\n            \"phone\" => \"+***********\"\n            \"email\" => \"<EMAIL>\"\n            \"address\" => \"Doha\"\n            \"latitude\" => \"0\"\n            \"longitude\" => \"0\"\n            \"commission\" => 5.0\n            \"pickup\" => 1\n            \"delivery\" => 1\n            \"is_active\" => 1\n            \"charge_per_km\" => null\n            \"is_open\" => 1\n            \"vendor_type_id\" => 3\n            \"auto_assignment\" => 1\n            \"auto_accept\" => 1\n            \"allow_schedule_order\" => 0\n            \"has_sub_categories\" => 0\n            \"min_order\" => 50.0\n            \"max_order\" => 1000.0\n            \"use_subscription\" => 0\n            \"has_drivers\" => 1\n            \"prepare_time\" => null\n            \"prepare_time_unit\" => \"minutes\"\n            \"delivery_time\" => null\n            \"delivery_time_unit\" => \"minutes\"\n            \"in_order\" => 1\n            \"featured\" => 1\n            \"created_at\" => \"2025-06-09 23:38:38\"\n            \"updated_at\" => \"2025-06-09 23:38:38\"\n            \"deleted_at\" => null\n            \"creator_id\" => 1\n            \"reviews_count\" => 0\n            \"ratings_count\" => 0\n          ]\n          #changes: []\n          #casts: array:12 [\n            \"id\" => \"integer\"\n            \"allow_schedule_order\" => \"boolean\"\n            \"has_sub_categories\" => \"boolean\"\n            \"has_subscription\" => \"boolean\"\n            \"use_subscription\" => \"boolean\"\n            \"vendor_type_id\" => \"int\"\n            \"pickup\" => \"int\"\n            \"delivery\" => \"int\"\n            \"is_active\" => \"int\"\n            \"reviews_count\" => \"int\"\n            \"is_open\" => \"boolean\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: array:12 [\n            0 => \"formatted_date\"\n            1 => \"logo\"\n            2 => \"feature_image\"\n            3 => \"rating\"\n            4 => \"can_rate\"\n            5 => \"slots\"\n            6 => \"is_package_vendor\"\n            7 => \"is_favourite\"\n            8 => \"has_subscription\"\n            9 => \"document_requested\"\n            10 => \"pending_document_approval\"\n            11 => \"description_url\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"vendor_type\" => App\\Models\\VendorType {#3296\n              #connection: \"mysql\"\n              #table: \"vendor_types\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: []\n              #withCount: []\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:10 [\n                \"id\" => 3\n                \"name\" => \"{\"en\":\"Grocery\"}\"\n                \"color\" => \"#45b12f\"\n                \"description\" => \"{\"en\":\"Buy grocery from your nearby markets\"}\"\n                \"slug\" => \"grocery\"\n                \"is_active\" => 1\n                \"in_order\" => 3\n                \"created_at\" => \"2021-06-30 16:59:15\"\n                \"updated_at\" => \"2024-05-01 13:50:34\"\n                \"deleted_at\" => null\n              ]\n              #original: array:10 [\n                \"id\" => 3\n                \"name\" => \"{\"en\":\"Grocery\"}\"\n                \"color\" => \"#45b12f\"\n                \"description\" => \"{\"en\":\"Buy grocery from your nearby markets\"}\"\n                \"slug\" => \"grocery\"\n                \"is_active\" => 1\n                \"in_order\" => 3\n                \"created_at\" => \"2021-06-30 16:59:15\"\n                \"updated_at\" => \"2024-05-01 13:50:34\"\n                \"deleted_at\" => null\n              ]\n              #changes: []\n              #casts: array:5 [\n                \"id\" => \"int\"\n                \"is_active\" => \"int\"\n                \"deleted_at\" => \"datetime\"\n                \"name\" => \"array\"\n                \"description\" => \"array\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: array:4 [\n                0 => \"formatted_date\"\n                1 => \"logo\"\n                2 => \"website_header\"\n                3 => \"has_banners\"\n              ]\n              #dispatchesEvents: []\n              #observables: []\n              #relations: []\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: array:1 [\n                0 => \"media\"\n              ]\n              #visible: []\n              #fillable: array:5 [\n                0 => \"name\"\n                1 => \"description\"\n                2 => \"slug\"\n                3 => \"is_active\"\n                4 => \"color\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              +mediaConversions: []\n              +mediaCollections: []\n              #deletePreservingMedia: false\n              #unAttachedMediaLibraryItems: []\n              #forceDeleting: false\n              +translatable: array:2 [\n                0 => \"name\"\n                1 => \"description\"\n              ]\n              #translationLocale: null\n            }\n            \"fees\" => Illuminate\\Database\\Eloquent\\Collection {#3246\n              #items: []\n              #escapeWhenCastingToString: false\n            }\n            \"media\" => Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection {#2757\n              #items: array:2 [\n                0 => Spatie\\MediaLibrary\\MediaCollections\\Models\\Media {#2753\n                  #connection: \"mysql\"\n                  #table: \"media\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: true\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:18 [\n                    \"id\" => 32\n                    \"model_type\" => \"App\\Models\\Vendor\"\n                    \"model_id\" => 1\n                    \"uuid\" => \"9d68ed2c-51ee-4d12-a08e-d4f5e622b25c\"\n                    \"collection_name\" => \"logo\"\n                    \"name\" => \"QxrUuXyLKDrufppaf1W9jYsJw47NMZ-metaU2NyZWVuc2hvdF8yMDI1MDYwOV8wNjE2MDkuanBn-\"\n                    \"file_name\" => \"FWxWJ-1749490719.jpg\"\n                    \"mime_type\" => \"image/jpeg\"\n                    \"disk\" => \"public\"\n                    \"conversions_disk\" => \"public\"\n                    \"size\" => 1027868\n                    \"manipulations\" => \"[]\"\n                    \"custom_properties\" => \"[]\"\n                    \"responsive_images\" => \"[]\"\n                    \"order_column\" => 1\n                    \"created_at\" => \"2025-06-09 23:38:39\"\n                    \"updated_at\" => \"2025-06-09 23:38:39\"\n                    \"generated_conversions\" => \"[]\"\n                  ]\n                  #original: array:18 [\n                    \"id\" => 32\n                    \"model_type\" => \"App\\Models\\Vendor\"\n                    \"model_id\" => 1\n                    \"uuid\" => \"9d68ed2c-51ee-4d12-a08e-d4f5e622b25c\"\n                    \"collection_name\" => \"logo\"\n                    \"name\" => \"QxrUuXyLKDrufppaf1W9jYsJw47NMZ-metaU2NyZWVuc2hvdF8yMDI1MDYwOV8wNjE2MDkuanBn-\"\n                    \"file_name\" => \"FWxWJ-1749490719.jpg\"\n                    \"mime_type\" => \"image/jpeg\"\n                    \"disk\" => \"public\"\n                    \"conversions_disk\" => \"public\"\n                    \"size\" => 1027868\n                    \"manipulations\" => \"[]\"\n                    \"custom_properties\" => \"[]\"\n                    \"responsive_images\" => \"[]\"\n                    \"order_column\" => 1\n                    \"created_at\" => \"2025-06-09 23:38:39\"\n                    \"updated_at\" => \"2025-06-09 23:38:39\"\n                    \"generated_conversions\" => \"[]\"\n                  ]\n                  #changes: []\n                  #casts: array:4 [\n                    \"manipulations\" => \"array\"\n                    \"custom_properties\" => \"array\"\n                    \"generated_conversions\" => \"array\"\n                    \"responsive_images\" => \"array\"\n                  ]\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: array:2 [\n                    0 => \"original_url\"\n                    1 => \"preview_url\"\n                  ]\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  +timestamps: true\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  #streamChunkSize: 1048576\n                }\n                1 => Spatie\\MediaLibrary\\MediaCollections\\Models\\Media {#2731\n                  #connection: \"mysql\"\n                  #table: \"media\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: true\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:18 [\n                    \"id\" => 33\n                    \"model_type\" => \"App\\Models\\Vendor\"\n                    \"model_id\" => 1\n                    \"uuid\" => \"0097f452-**************-d58fc3a4a761\"\n                    \"collection_name\" => \"feature_image\"\n                    \"name\" => \"EthLlaKBBzxl2cOWYyihv6b11bVJKO-metaU2NyZWVuc2hvdF8yMDI1MDYwOV8wNjE2MDkuanBn-\"\n                    \"file_name\" => \"deNKU-1749490719.jpg\"\n                    \"mime_type\" => \"image/jpeg\"\n                    \"disk\" => \"public\"\n                    \"conversions_disk\" => \"public\"\n                    \"size\" => 1027868\n                    \"manipulations\" => \"[]\"\n                    \"custom_properties\" => \"[]\"\n                    \"responsive_images\" => \"[]\"\n                    \"order_column\" => 2\n                    \"created_at\" => \"2025-06-09 23:38:39\"\n                    \"updated_at\" => \"2025-06-09 23:38:39\"\n                    \"generated_conversions\" => \"[]\"\n                  ]\n                  #original: array:18 [\n                    \"id\" => 33\n                    \"model_type\" => \"App\\Models\\Vendor\"\n                    \"model_id\" => 1\n                    \"uuid\" => \"0097f452-**************-d58fc3a4a761\"\n                    \"collection_name\" => \"feature_image\"\n                    \"name\" => \"EthLlaKBBzxl2cOWYyihv6b11bVJKO-metaU2NyZWVuc2hvdF8yMDI1MDYwOV8wNjE2MDkuanBn-\"\n                    \"file_name\" => \"deNKU-1749490719.jpg\"\n                    \"mime_type\" => \"image/jpeg\"\n                    \"disk\" => \"public\"\n                    \"conversions_disk\" => \"public\"\n                    \"size\" => 1027868\n                    \"manipulations\" => \"[]\"\n                    \"custom_properties\" => \"[]\"\n                    \"responsive_images\" => \"[]\"\n                    \"order_column\" => 2\n                    \"created_at\" => \"2025-06-09 23:38:39\"\n                    \"updated_at\" => \"2025-06-09 23:38:39\"\n                    \"generated_conversions\" => \"[]\"\n                  ]\n                  #changes: []\n                  #casts: array:4 [\n                    \"manipulations\" => \"array\"\n                    \"custom_properties\" => \"array\"\n                    \"generated_conversions\" => \"array\"\n                    \"responsive_images\" => \"array\"\n                  ]\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: array:2 [\n                    0 => \"original_url\"\n                    1 => \"preview_url\"\n                  ]\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  +timestamps: true\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  #streamChunkSize: 1048576\n                }\n              ]\n              #escapeWhenCastingToString: false\n              +collectionName: null\n              +formFieldName: null\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: array:1 [\n            0 => \"media\"\n          ]\n          #visible: []\n          #fillable: array:18 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"description\"\n            3 => \"delivery_fee\"\n            4 => \"delivery_range\"\n            5 => \"tax\"\n            6 => \"phone\"\n            7 => \"email\"\n            8 => \"address\"\n            9 => \"latitude\"\n            10 => \"longitude\"\n            11 => \"commission\"\n            12 => \"pickup\"\n            13 => \"delivery\"\n            14 => \"is_active\"\n            15 => \"charge_per_km\"\n            16 => \"is_open\"\n            17 => \"vendor_type_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n          #forceDeleting: false\n          #geographical_options: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"topCustomers\" => Illuminate\\Database\\Eloquent\\Collection {#2971\n      #items: array:6 [\n        0 => App\\Models\\User {#3097\n          #connection: \"mysql\"\n          #table: \"users\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: array:1 [\n            0 => \"myReferrals\"\n          ]\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:24 [\n            \"id\" => 1\n            \"code\" => null\n            \"name\" => \"Admin Account\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"+***********\"\n            \"country_code\" => \"QA\"\n            \"commission\" => \"0.00\"\n            \"email_verified_at\" => null\n            \"password\" => \"$2y$10$J.BU9XOx8RR4rHoSl/k9K.BhaRSsCvP479bnKEa1oBMGBZJNqGQ8K\"\n            \"two_factor_secret\" => null\n            \"two_factor_recovery_codes\" => null\n            \"two_factor_confirmed_at\" => null\n            \"vendor_id\" => null\n            \"is_active\" => 1\n            \"is_online\" => 0\n            \"creator_id\" => null\n            \"language\" => \"en\"\n            \"remember_token\" => \"RZFvnS3aNlH1wTyolS5zSPUV3TMxrN0M2u9SaTuy4fxJT97rc7wbOf80fhUz\"\n            \"created_at\" => \"2020-12-28 14:14:31\"\n            \"updated_at\" => \"2025-06-11 01:30:07\"\n            \"deleted_at\" => null\n            \"my_referrals_count\" => 0\n            \"orders_count\" => 3\n            \"successful_orders_count\" => 0\n          ]\n          #original: array:24 [\n            \"id\" => 1\n            \"code\" => null\n            \"name\" => \"Admin Account\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"+***********\"\n            \"country_code\" => \"QA\"\n            \"commission\" => \"0.00\"\n            \"email_verified_at\" => null\n            \"password\" => \"$2y$10$J.BU9XOx8RR4rHoSl/k9K.BhaRSsCvP479bnKEa1oBMGBZJNqGQ8K\"\n            \"two_factor_secret\" => null\n            \"two_factor_recovery_codes\" => null\n            \"two_factor_confirmed_at\" => null\n            \"vendor_id\" => null\n            \"is_active\" => 1\n            \"is_online\" => 0\n            \"creator_id\" => null\n            \"language\" => \"en\"\n            \"remember_token\" => \"RZFvnS3aNlH1wTyolS5zSPUV3TMxrN0M2u9SaTuy4fxJT97rc7wbOf80fhUz\"\n            \"created_at\" => \"2020-12-28 14:14:31\"\n            \"updated_at\" => \"2025-06-11 01:30:07\"\n            \"deleted_at\" => null\n            \"my_referrals_count\" => 0\n            \"orders_count\" => 3\n            \"successful_orders_count\" => 0\n          ]\n          #changes: []\n          #casts: array:3 [\n            \"email_verified_at\" => \"datetime\"\n            \"vendor_id\" => \"int\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: array:10 [\n            0 => \"role_name\"\n            1 => \"role_id\"\n            2 => \"formatted_date\"\n            3 => \"photo\"\n            4 => \"rating\"\n            5 => \"assigned_orders\"\n            6 => \"raw_phone\"\n            7 => \"is_taxi_driver\"\n            8 => \"document_requested\"\n            9 => \"pending_document_approval\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"media\" => Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection {#2740\n              #items: array:1 [\n                0 => Spatie\\MediaLibrary\\MediaCollections\\Models\\Media {#2720\n                  #connection: \"mysql\"\n                  #table: \"media\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: true\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:18 [\n                    \"id\" => 42\n                    \"model_type\" => \"App\\Models\\User\"\n                    \"model_id\" => 1\n                    \"uuid\" => \"5817b118-c11c-4f99-b148-2ab97b5526fe\"\n                    \"collection_name\" => \"profile\"\n                    \"name\" => \"1000072321\"\n                    \"file_name\" => \"1000072321.jpg\"\n                    \"mime_type\" => \"image/jpeg\"\n                    \"disk\" => \"public\"\n                    \"conversions_disk\" => \"public\"\n                    \"size\" => 221426\n                    \"manipulations\" => \"[]\"\n                    \"custom_properties\" => \"[]\"\n                    \"responsive_images\" => \"[]\"\n                    \"order_column\" => 1\n                    \"created_at\" => \"2025-06-11 01:30:07\"\n                    \"updated_at\" => \"2025-06-11 01:30:07\"\n                    \"generated_conversions\" => \"[]\"\n                  ]\n                  #original: array:18 [\n                    \"id\" => 42\n                    \"model_type\" => \"App\\Models\\User\"\n                    \"model_id\" => 1\n                    \"uuid\" => \"5817b118-c11c-4f99-b148-2ab97b5526fe\"\n                    \"collection_name\" => \"profile\"\n                    \"name\" => \"1000072321\"\n                    \"file_name\" => \"1000072321.jpg\"\n                    \"mime_type\" => \"image/jpeg\"\n                    \"disk\" => \"public\"\n                    \"conversions_disk\" => \"public\"\n                    \"size\" => 221426\n                    \"manipulations\" => \"[]\"\n                    \"custom_properties\" => \"[]\"\n                    \"responsive_images\" => \"[]\"\n                    \"order_column\" => 1\n                    \"created_at\" => \"2025-06-11 01:30:07\"\n                    \"updated_at\" => \"2025-06-11 01:30:07\"\n                    \"generated_conversions\" => \"[]\"\n                  ]\n                  #changes: []\n                  #casts: array:4 [\n                    \"manipulations\" => \"array\"\n                    \"custom_properties\" => \"array\"\n                    \"generated_conversions\" => \"array\"\n                    \"responsive_images\" => \"array\"\n                  ]\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: array:2 [\n                    0 => \"original_url\"\n                    1 => \"preview_url\"\n                  ]\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  +timestamps: true\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  #streamChunkSize: 1048576\n                }\n              ]\n              #escapeWhenCastingToString: false\n              +collectionName: null\n              +formFieldName: null\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: array:4 [\n            0 => \"password\"\n            1 => \"remember_token\"\n            2 => \"two_factor_secret\"\n            3 => \"two_factor_recovery_codes\"\n          ]\n          #visible: []\n          #fillable: array:4 [\n            0 => \"name\"\n            1 => \"email\"\n            2 => \"password\"\n            3 => \"country_code\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #rememberTokenName: \"remember_token\"\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n          -roleClass: null\n          -permissionClass: null\n          -wildcardClass: null\n          -wildcardPermissionsIndex: ? array\n          #accessToken: null\n          #forceDeleting: false\n        }\n        1 => App\\Models\\User {#3098\n          #connection: \"mysql\"\n          #table: \"users\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: array:1 [\n            0 => \"myReferrals\"\n          ]\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:24 [\n            \"id\" => 2\n            \"code\" => null\n            \"name\" => \"Manager Account\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"**********\"\n            \"country_code\" => null\n            \"commission\" => \"0.00\"\n            \"email_verified_at\" => null\n            \"password\" => \"$2y$10$iKWFwyMmvymjU2/9KC8MSOza6lAzLAjKb3bPcg.WNVxUNXJVXTW/e\"\n            \"two_factor_secret\" => null\n            \"two_factor_recovery_codes\" => null\n            \"two_factor_confirmed_at\" => null\n            \"vendor_id\" => null\n            \"is_active\" => 1\n            \"is_online\" => 0\n            \"creator_id\" => null\n            \"language\" => \"en\"\n            \"remember_token\" => \"4Vpdv7vm9L2m6ywZMd4cIZcHK3eUGYI1CvZkW6oGwKQFIrHX4889S31xJzgv\"\n            \"created_at\" => \"2021-01-08 15:15:16\"\n            \"updated_at\" => \"2021-03-09 13:49:29\"\n            \"deleted_at\" => null\n            \"my_referrals_count\" => 0\n            \"orders_count\" => 0\n            \"successful_orders_count\" => 0\n          ]\n          #original: array:24 [\n            \"id\" => 2\n            \"code\" => null\n            \"name\" => \"Manager Account\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"**********\"\n            \"country_code\" => null\n            \"commission\" => \"0.00\"\n            \"email_verified_at\" => null\n            \"password\" => \"$2y$10$iKWFwyMmvymjU2/9KC8MSOza6lAzLAjKb3bPcg.WNVxUNXJVXTW/e\"\n            \"two_factor_secret\" => null\n            \"two_factor_recovery_codes\" => null\n            \"two_factor_confirmed_at\" => null\n            \"vendor_id\" => null\n            \"is_active\" => 1\n            \"is_online\" => 0\n            \"creator_id\" => null\n            \"language\" => \"en\"\n            \"remember_token\" => \"4Vpdv7vm9L2m6ywZMd4cIZcHK3eUGYI1CvZkW6oGwKQFIrHX4889S31xJzgv\"\n            \"created_at\" => \"2021-01-08 15:15:16\"\n            \"updated_at\" => \"2021-03-09 13:49:29\"\n            \"deleted_at\" => null\n            \"my_referrals_count\" => 0\n            \"orders_count\" => 0\n            \"successful_orders_count\" => 0\n          ]\n          #changes: []\n          #casts: array:3 [\n            \"email_verified_at\" => \"datetime\"\n            \"vendor_id\" => \"int\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: array:10 [\n            0 => \"role_name\"\n            1 => \"role_id\"\n            2 => \"formatted_date\"\n            3 => \"photo\"\n            4 => \"rating\"\n            5 => \"assigned_orders\"\n            6 => \"raw_phone\"\n            7 => \"is_taxi_driver\"\n            8 => \"document_requested\"\n            9 => \"pending_document_approval\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"media\" => Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection {#2700\n              #items: []\n              #escapeWhenCastingToString: false\n              +collectionName: null\n              +formFieldName: null\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: array:4 [\n            0 => \"password\"\n            1 => \"remember_token\"\n            2 => \"two_factor_secret\"\n            3 => \"two_factor_recovery_codes\"\n          ]\n          #visible: []\n          #fillable: array:4 [\n            0 => \"name\"\n            1 => \"email\"\n            2 => \"password\"\n            3 => \"country_code\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #rememberTokenName: \"remember_token\"\n          +mediaConversions: []\n          +mediaCollections: array:1 [\n            \"profile\" => Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2702\n              +diskName: \"\"\n              +conversionsDiskName: \"\"\n              +mediaConversionRegistrations: Closure() {#2738\n                class: \"Spatie\\MediaLibrary\\MediaCollections\\MediaCollection\"\n                this: Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2702}\n                file: \"C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaCollection.php\"\n                line: \"40 to 40\"\n              }\n              +generateResponsiveImages: false\n              +acceptsFile: Closure() {#2705\n                class: \"Spatie\\MediaLibrary\\MediaCollections\\MediaCollection\"\n                this: Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2702}\n                file: \"C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaCollection.php\"\n                line: \"42 to 42\"\n              }\n              +acceptsMimeTypes: []\n              +collectionSizeLimit: false\n              +singleFile: false\n              +fallbackUrls: array:1 [\n                \"default\" => \"http://localhost/images/user.png\"\n              ]\n              +fallbackPaths: array:1 [\n                \"default\" => \"C:\\xampp\\htdocs\\public\\/images/user.png\"\n              ]\n              +name: \"profile\"\n            }\n          ]\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n          -roleClass: null\n          -permissionClass: null\n          -wildcardClass: null\n          -wildcardPermissionsIndex: ? array\n          #accessToken: null\n          #forceDeleting: false\n        }\n        2 => App\\Models\\User {#3100\n          #connection: \"mysql\"\n          #table: \"users\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: array:1 [\n            0 => \"myReferrals\"\n          ]\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:24 [\n            \"id\" => 3\n            \"code\" => null\n            \"name\" => \"Driver account\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"**********\"\n            \"country_code\" => null\n            \"commission\" => \"0.00\"\n            \"email_verified_at\" => null\n            \"password\" => \"$2y$10$vB0SIBOw6lXSvmvkPV8rZuNwr9BDbp9hdxQ8vQE8YwXWf69LK0nXG\"\n            \"two_factor_secret\" => null\n            \"two_factor_recovery_codes\" => null\n            \"two_factor_confirmed_at\" => null\n            \"vendor_id\" => null\n            \"is_active\" => 1\n            \"is_online\" => 0\n            \"creator_id\" => null\n            \"language\" => \"en\"\n            \"remember_token\" => null\n            \"created_at\" => \"2021-01-08 15:15:16\"\n            \"updated_at\" => \"2025-06-11 02:35:02\"\n            \"deleted_at\" => null\n            \"my_referrals_count\" => 0\n            \"orders_count\" => 0\n            \"successful_orders_count\" => 0\n          ]\n          #original: array:24 [\n            \"id\" => 3\n            \"code\" => null\n            \"name\" => \"Driver account\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"**********\"\n            \"country_code\" => null\n            \"commission\" => \"0.00\"\n            \"email_verified_at\" => null\n            \"password\" => \"$2y$10$vB0SIBOw6lXSvmvkPV8rZuNwr9BDbp9hdxQ8vQE8YwXWf69LK0nXG\"\n            \"two_factor_secret\" => null\n            \"two_factor_recovery_codes\" => null\n            \"two_factor_confirmed_at\" => null\n            \"vendor_id\" => null\n            \"is_active\" => 1\n            \"is_online\" => 0\n            \"creator_id\" => null\n            \"language\" => \"en\"\n            \"remember_token\" => null\n            \"created_at\" => \"2021-01-08 15:15:16\"\n            \"updated_at\" => \"2025-06-11 02:35:02\"\n            \"deleted_at\" => null\n            \"my_referrals_count\" => 0\n            \"orders_count\" => 0\n            \"successful_orders_count\" => 0\n          ]\n          #changes: []\n          #casts: array:3 [\n            \"email_verified_at\" => \"datetime\"\n            \"vendor_id\" => \"int\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: array:10 [\n            0 => \"role_name\"\n            1 => \"role_id\"\n            2 => \"formatted_date\"\n            3 => \"photo\"\n            4 => \"rating\"\n            5 => \"assigned_orders\"\n            6 => \"raw_phone\"\n            7 => \"is_taxi_driver\"\n            8 => \"document_requested\"\n            9 => \"pending_document_approval\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"media\" => Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection {#2684\n              #items: []\n              #escapeWhenCastingToString: false\n              +collectionName: null\n              +formFieldName: null\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: array:4 [\n            0 => \"password\"\n            1 => \"remember_token\"\n            2 => \"two_factor_secret\"\n            3 => \"two_factor_recovery_codes\"\n          ]\n          #visible: []\n          #fillable: array:4 [\n            0 => \"name\"\n            1 => \"email\"\n            2 => \"password\"\n            3 => \"country_code\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #rememberTokenName: \"remember_token\"\n          +mediaConversions: []\n          +mediaCollections: array:1 [\n            \"profile\" => Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2686\n              +diskName: \"\"\n              +conversionsDiskName: \"\"\n              +mediaConversionRegistrations: Closure() {#2704\n                class: \"Spatie\\MediaLibrary\\MediaCollections\\MediaCollection\"\n                this: Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2686}\n                file: \"C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaCollection.php\"\n                line: \"40 to 40\"\n              }\n              +generateResponsiveImages: false\n              +acceptsFile: Closure() {#2689\n                class: \"Spatie\\MediaLibrary\\MediaCollections\\MediaCollection\"\n                this: Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2686}\n                file: \"C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaCollection.php\"\n                line: \"42 to 42\"\n              }\n              +acceptsMimeTypes: []\n              +collectionSizeLimit: false\n              +singleFile: false\n              +fallbackUrls: array:1 [\n                \"default\" => \"http://localhost/images/user.png\"\n              ]\n              +fallbackPaths: array:1 [\n                \"default\" => \"C:\\xampp\\htdocs\\public\\/images/user.png\"\n              ]\n              +name: \"profile\"\n            }\n          ]\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n          -roleClass: null\n          -permissionClass: null\n          -wildcardClass: null\n          -wildcardPermissionsIndex: ? array\n          #accessToken: null\n          #forceDeleting: false\n        }\n        3 => App\\Models\\User {#3101\n          #connection: \"mysql\"\n          #table: \"users\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: array:1 [\n            0 => \"myReferrals\"\n          ]\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:24 [\n            \"id\" => 4\n            \"code\" => null\n            \"name\" => \"Client Account\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"**********\"\n            \"country_code\" => null\n            \"commission\" => \"0.00\"\n            \"email_verified_at\" => null\n            \"password\" => \"$2y$10$IhajMcz6AFXAAKS3zIAie.ujsA0LH.FSz9ACY/S8dSbjVSeCJW/xm\"\n            \"two_factor_secret\" => null\n            \"two_factor_recovery_codes\" => null\n            \"two_factor_confirmed_at\" => null\n            \"vendor_id\" => null\n            \"is_active\" => 1\n            \"is_online\" => 0\n            \"creator_id\" => null\n            \"language\" => \"en\"\n            \"remember_token\" => null\n            \"created_at\" => \"2021-02-01 16:14:26\"\n            \"updated_at\" => \"2021-02-01 16:14:26\"\n            \"deleted_at\" => null\n            \"my_referrals_count\" => 0\n            \"orders_count\" => 1\n            \"successful_orders_count\" => 0\n          ]\n          #original: array:24 [\n            \"id\" => 4\n            \"code\" => null\n            \"name\" => \"Client Account\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"**********\"\n            \"country_code\" => null\n            \"commission\" => \"0.00\"\n            \"email_verified_at\" => null\n            \"password\" => \"$2y$10$IhajMcz6AFXAAKS3zIAie.ujsA0LH.FSz9ACY/S8dSbjVSeCJW/xm\"\n            \"two_factor_secret\" => null\n            \"two_factor_recovery_codes\" => null\n            \"two_factor_confirmed_at\" => null\n            \"vendor_id\" => null\n            \"is_active\" => 1\n            \"is_online\" => 0\n            \"creator_id\" => null\n            \"language\" => \"en\"\n            \"remember_token\" => null\n            \"created_at\" => \"2021-02-01 16:14:26\"\n            \"updated_at\" => \"2021-02-01 16:14:26\"\n            \"deleted_at\" => null\n            \"my_referrals_count\" => 0\n            \"orders_count\" => 1\n            \"successful_orders_count\" => 0\n          ]\n          #changes: []\n          #casts: array:3 [\n            \"email_verified_at\" => \"datetime\"\n            \"vendor_id\" => \"int\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: array:10 [\n            0 => \"role_name\"\n            1 => \"role_id\"\n            2 => \"formatted_date\"\n            3 => \"photo\"\n            4 => \"rating\"\n            5 => \"assigned_orders\"\n            6 => \"raw_phone\"\n            7 => \"is_taxi_driver\"\n            8 => \"document_requested\"\n            9 => \"pending_document_approval\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"media\" => Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection {#2662\n              #items: []\n              #escapeWhenCastingToString: false\n              +collectionName: null\n              +formFieldName: null\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: array:4 [\n            0 => \"password\"\n            1 => \"remember_token\"\n            2 => \"two_factor_secret\"\n            3 => \"two_factor_recovery_codes\"\n          ]\n          #visible: []\n          #fillable: array:4 [\n            0 => \"name\"\n            1 => \"email\"\n            2 => \"password\"\n            3 => \"country_code\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #rememberTokenName: \"remember_token\"\n          +mediaConversions: []\n          +mediaCollections: array:1 [\n            \"profile\" => Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2664\n              +diskName: \"\"\n              +conversionsDiskName: \"\"\n              +mediaConversionRegistrations: Closure() {#2688\n                class: \"Spatie\\MediaLibrary\\MediaCollections\\MediaCollection\"\n                this: Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2664}\n                file: \"C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaCollection.php\"\n                line: \"40 to 40\"\n              }\n              +generateResponsiveImages: false\n              +acceptsFile: Closure() {#2667\n                class: \"Spatie\\MediaLibrary\\MediaCollections\\MediaCollection\"\n                this: Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2664}\n                file: \"C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaCollection.php\"\n                line: \"42 to 42\"\n              }\n              +acceptsMimeTypes: []\n              +collectionSizeLimit: false\n              +singleFile: false\n              +fallbackUrls: array:1 [\n                \"default\" => \"http://localhost/images/user.png\"\n              ]\n              +fallbackPaths: array:1 [\n                \"default\" => \"C:\\xampp\\htdocs\\public\\/images/user.png\"\n              ]\n              +name: \"profile\"\n            }\n          ]\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n          -roleClass: null\n          -permissionClass: null\n          -wildcardClass: null\n          -wildcardPermissionsIndex: ? array\n          #accessToken: null\n          #forceDeleting: false\n        }\n        4 => App\\Models\\User {#3099\n          #connection: \"mysql\"\n          #table: \"users\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: array:1 [\n            0 => \"myReferrals\"\n          ]\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:24 [\n            \"id\" => 5\n            \"code\" => null\n            \"name\" => \"Manager Account Package Vendor\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"**********\"\n            \"country_code\" => null\n            \"commission\" => \"0.00\"\n            \"email_verified_at\" => null\n            \"password\" => \"$2y$10$7kzbDpQNyYd5sFnv4hubnebBBx1WbkGuv2tnIa5QHf7/0n.Kwesv6\"\n            \"two_factor_secret\" => null\n            \"two_factor_recovery_codes\" => null\n            \"two_factor_confirmed_at\" => null\n            \"vendor_id\" => null\n            \"is_active\" => 1\n            \"is_online\" => 0\n            \"creator_id\" => null\n            \"language\" => \"en\"\n            \"remember_token\" => null\n            \"created_at\" => \"2021-01-08 15:15:16\"\n            \"updated_at\" => \"2021-03-09 13:49:14\"\n            \"deleted_at\" => null\n            \"my_referrals_count\" => 0\n            \"orders_count\" => 0\n            \"successful_orders_count\" => 0\n          ]\n          #original: array:24 [\n            \"id\" => 5\n            \"code\" => null\n            \"name\" => \"Manager Account Package Vendor\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"**********\"\n            \"country_code\" => null\n            \"commission\" => \"0.00\"\n            \"email_verified_at\" => null\n            \"password\" => \"$2y$10$7kzbDpQNyYd5sFnv4hubnebBBx1WbkGuv2tnIa5QHf7/0n.Kwesv6\"\n            \"two_factor_secret\" => null\n            \"two_factor_recovery_codes\" => null\n            \"two_factor_confirmed_at\" => null\n            \"vendor_id\" => null\n            \"is_active\" => 1\n            \"is_online\" => 0\n            \"creator_id\" => null\n            \"language\" => \"en\"\n            \"remember_token\" => null\n            \"created_at\" => \"2021-01-08 15:15:16\"\n            \"updated_at\" => \"2021-03-09 13:49:14\"\n            \"deleted_at\" => null\n            \"my_referrals_count\" => 0\n            \"orders_count\" => 0\n            \"successful_orders_count\" => 0\n          ]\n          #changes: []\n          #casts: array:3 [\n            \"email_verified_at\" => \"datetime\"\n            \"vendor_id\" => \"int\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: array:10 [\n            0 => \"role_name\"\n            1 => \"role_id\"\n            2 => \"formatted_date\"\n            3 => \"photo\"\n            4 => \"rating\"\n            5 => \"assigned_orders\"\n            6 => \"raw_phone\"\n            7 => \"is_taxi_driver\"\n            8 => \"document_requested\"\n            9 => \"pending_document_approval\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"media\" => Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection {#2640\n              #items: []\n              #escapeWhenCastingToString: false\n              +collectionName: null\n              +formFieldName: null\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: array:4 [\n            0 => \"password\"\n            1 => \"remember_token\"\n            2 => \"two_factor_secret\"\n            3 => \"two_factor_recovery_codes\"\n          ]\n          #visible: []\n          #fillable: array:4 [\n            0 => \"name\"\n            1 => \"email\"\n            2 => \"password\"\n            3 => \"country_code\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #rememberTokenName: \"remember_token\"\n          +mediaConversions: []\n          +mediaCollections: array:1 [\n            \"profile\" => Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2642\n              +diskName: \"\"\n              +conversionsDiskName: \"\"\n              +mediaConversionRegistrations: Closure() {#2666\n                class: \"Spatie\\MediaLibrary\\MediaCollections\\MediaCollection\"\n                this: Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2642}\n                file: \"C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaCollection.php\"\n                line: \"40 to 40\"\n              }\n              +generateResponsiveImages: false\n              +acceptsFile: Closure() {#2645\n                class: \"Spatie\\MediaLibrary\\MediaCollections\\MediaCollection\"\n                this: Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2642}\n                file: \"C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaCollection.php\"\n                line: \"42 to 42\"\n              }\n              +acceptsMimeTypes: []\n              +collectionSizeLimit: false\n              +singleFile: false\n              +fallbackUrls: array:1 [\n                \"default\" => \"http://localhost/images/user.png\"\n              ]\n              +fallbackPaths: array:1 [\n                \"default\" => \"C:\\xampp\\htdocs\\public\\/images/user.png\"\n              ]\n              +name: \"profile\"\n            }\n          ]\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n          -roleClass: null\n          -permissionClass: null\n          -wildcardClass: null\n          -wildcardPermissionsIndex: ? array\n          #accessToken: null\n          #forceDeleting: false\n        }\n        5 => App\\Models\\User {#3103\n          #connection: \"mysql\"\n          #table: \"users\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: array:1 [\n            0 => \"myReferrals\"\n          ]\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:24 [\n            \"id\" => 6\n            \"code\" => null\n            \"name\" => \"Taxi Driver\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"0557484192\"\n            \"country_code\" => null\n            \"commission\" => \"0.00\"\n            \"email_verified_at\" => null\n            \"password\" => \"$2y$10$wcDlXo4hUoDXAq7pp6zwmeYD3XkCPD7D4iBHY8qj2FjIRzgBc8VLu\"\n            \"two_factor_secret\" => null\n            \"two_factor_recovery_codes\" => null\n            \"two_factor_confirmed_at\" => null\n            \"vendor_id\" => null\n            \"is_active\" => 1\n            \"is_online\" => 0\n            \"creator_id\" => null\n            \"language\" => \"en\"\n            \"remember_token\" => null\n            \"created_at\" => \"2021-01-08 15:15:16\"\n            \"updated_at\" => \"2021-03-09 13:49:14\"\n            \"deleted_at\" => null\n            \"my_referrals_count\" => 0\n            \"orders_count\" => 0\n            \"successful_orders_count\" => 0\n          ]\n          #original: array:24 [\n            \"id\" => 6\n            \"code\" => null\n            \"name\" => \"Taxi Driver\"\n            \"email\" => \"<EMAIL>\"\n            \"phone\" => \"0557484192\"\n            \"country_code\" => null\n            \"commission\" => \"0.00\"\n            \"email_verified_at\" => null\n            \"password\" => \"$2y$10$wcDlXo4hUoDXAq7pp6zwmeYD3XkCPD7D4iBHY8qj2FjIRzgBc8VLu\"\n            \"two_factor_secret\" => null\n            \"two_factor_recovery_codes\" => null\n            \"two_factor_confirmed_at\" => null\n            \"vendor_id\" => null\n            \"is_active\" => 1\n            \"is_online\" => 0\n            \"creator_id\" => null\n            \"language\" => \"en\"\n            \"remember_token\" => null\n            \"created_at\" => \"2021-01-08 15:15:16\"\n            \"updated_at\" => \"2021-03-09 13:49:14\"\n            \"deleted_at\" => null\n            \"my_referrals_count\" => 0\n            \"orders_count\" => 0\n            \"successful_orders_count\" => 0\n          ]\n          #changes: []\n          #casts: array:3 [\n            \"email_verified_at\" => \"datetime\"\n            \"vendor_id\" => \"int\"\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: array:10 [\n            0 => \"role_name\"\n            1 => \"role_id\"\n            2 => \"formatted_date\"\n            3 => \"photo\"\n            4 => \"rating\"\n            5 => \"assigned_orders\"\n            6 => \"raw_phone\"\n            7 => \"is_taxi_driver\"\n            8 => \"document_requested\"\n            9 => \"pending_document_approval\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:1 [\n            \"media\" => Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection {#2618\n              #items: []\n              #escapeWhenCastingToString: false\n              +collectionName: null\n              +formFieldName: null\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: array:4 [\n            0 => \"password\"\n            1 => \"remember_token\"\n            2 => \"two_factor_secret\"\n            3 => \"two_factor_recovery_codes\"\n          ]\n          #visible: []\n          #fillable: array:4 [\n            0 => \"name\"\n            1 => \"email\"\n            2 => \"password\"\n            3 => \"country_code\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #rememberTokenName: \"remember_token\"\n          +mediaConversions: []\n          +mediaCollections: array:1 [\n            \"profile\" => Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2620\n              +diskName: \"\"\n              +conversionsDiskName: \"\"\n              +mediaConversionRegistrations: Closure() {#2644\n                class: \"Spatie\\MediaLibrary\\MediaCollections\\MediaCollection\"\n                this: Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2620}\n                file: \"C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaCollection.php\"\n                line: \"40 to 40\"\n              }\n              +generateResponsiveImages: false\n              +acceptsFile: Closure() {#2623\n                class: \"Spatie\\MediaLibrary\\MediaCollections\\MediaCollection\"\n                this: Spatie\\MediaLibrary\\MediaCollections\\MediaCollection {#2620}\n                file: \"C:\\xampp\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaCollection.php\"\n                line: \"42 to 42\"\n              }\n              +acceptsMimeTypes: []\n              +collectionSizeLimit: false\n              +singleFile: false\n              +fallbackUrls: array:1 [\n                \"default\" => \"http://localhost/images/user.png\"\n              ]\n              +fallbackPaths: array:1 [\n                \"default\" => \"C:\\xampp\\htdocs\\public\\/images/user.png\"\n              ]\n              +name: \"profile\"\n            }\n          ]\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n          -roleClass: null\n          -permissionClass: null\n          -wildcardClass: null\n          -wildcardPermissionsIndex: ? array\n          #accessToken: null\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"myTopSellingProducts\" => null\n    \"topSellingProducts\" => Illuminate\\Database\\Eloquent\\Collection {#2978\n      #items: array:1 [\n        0 => App\\Models\\Product {#3033\n          #connection: \"mysql\"\n          #table: \"products\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: array:4 [\n            0 => \"vendor\"\n            1 => \"tags\"\n            2 => \"timings\"\n            3 => \"brand\"\n          ]\n          #withCount: array:1 [\n            0 => \"reviews\"\n          ]\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:27 [\n            \"id\" => 1\n            \"brand_id\" => null\n            \"name\" => \"{\"en\":\"Abali Plain Yogurt, 32 oz\"}\"\n            \"sku\" => \"040087005017\"\n            \"barcode\" => \"**********\"\n            \"description\" => \"{\"en\":\"<p>Abali Plain Yogurt, 32 oz. Enjoy creamy, probiotic-rich yogurt with no added sugars. Perfect for smoothies, cooking, or as a healthy snack option!<\\/p>\"}\"\n            \"price\" => 6.45\n            \"discount_price\" => 4.79\n            \"capacity\" => \"30\"\n            \"unit\" => \"Pc\"\n            \"package_count\" => null\n            \"available_qty\" => null\n            \"featured\" => 1\n            \"deliverable\" => 1\n            \"is_active\" => 1\n            \"plus_option\" => 1\n            \"digital\" => 0\n            \"age_restricted\" => 0\n            \"vendor_id\" => 1\n            \"in_order\" => 1\n            \"approved\" => 1\n            \"created_at\" => \"2025-06-09 23:51:39\"\n            \"updated_at\" => \"2025-06-09 23:51:39\"\n            \"deleted_at\" => null\n            \"reviews_count\" => 0\n            \"successful_sales_sum_quantity\" => null\n            \"successful_sales_count\" => 0\n          ]\n          #original: array:27 [\n            \"id\" => 1\n            \"brand_id\" => null\n            \"name\" => \"{\"en\":\"Abali Plain Yogurt, 32 oz\"}\"\n            \"sku\" => \"040087005017\"\n            \"barcode\" => \"**********\"\n            \"description\" => \"{\"en\":\"<p>Abali Plain Yogurt, 32 oz. Enjoy creamy, probiotic-rich yogurt with no added sugars. Perfect for smoothies, cooking, or as a healthy snack option!<\\/p>\"}\"\n            \"price\" => 6.45\n            \"discount_price\" => 4.79\n            \"capacity\" => \"30\"\n            \"unit\" => \"Pc\"\n            \"package_count\" => null\n            \"available_qty\" => null\n            \"featured\" => 1\n            \"deliverable\" => 1\n            \"is_active\" => 1\n            \"plus_option\" => 1\n            \"digital\" => 0\n            \"age_restricted\" => 0\n            \"vendor_id\" => 1\n            \"in_order\" => 1\n            \"approved\" => 1\n            \"created_at\" => \"2025-06-09 23:51:39\"\n            \"updated_at\" => \"2025-06-09 23:51:39\"\n            \"deleted_at\" => null\n            \"reviews_count\" => 0\n            \"successful_sales_sum_quantity\" => null\n            \"successful_sales_count\" => 0\n          ]\n          #changes: []\n          #casts: array:4 [\n            \"age_restricted\" => \"bool\"\n            \"deleted_at\" => \"datetime\"\n            \"name\" => \"array\"\n            \"description\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: array:10 [\n            0 => \"formatted_date\"\n            1 => \"sell_price\"\n            2 => \"photo\"\n            3 => \"is_favourite\"\n            4 => \"rating\"\n            5 => \"option_groups\"\n            6 => \"photos\"\n            7 => \"digital_files\"\n            8 => \"token\"\n            9 => \"description_url\"\n          ]\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:5 [\n            \"vendor\" => App\\Models\\Vendor {#2980\n              #connection: \"mysql\"\n              #table: \"vendors\"\n              #primaryKey: \"id\"\n              #keyType: \"int\"\n              +incrementing: true\n              #with: array:2 [\n                0 => \"vendor_type\"\n                1 => \"fees\"\n              ]\n              #withCount: array:1 [\n                0 => \"reviews\"\n              ]\n              +preventsLazyLoading: false\n              #perPage: 15\n              +exists: true\n              +wasRecentlyCreated: false\n              #escapeWhenCastingToString: false\n              #attributes: array:38 [\n                \"id\" => 1\n                \"name\" => \"Nafiss Market\"\n                \"description\" => \"<p>Nafiss demo market</p>\"\n                \"base_delivery_fee\" => 0.0\n                \"delivery_fee\" => 0.0\n                \"delivery_range\" => null\n                \"tax\" => null\n                \"phone\" => \"+***********\"\n                \"email\" => \"<EMAIL>\"\n                \"address\" => \"Doha\"\n                \"latitude\" => \"0\"\n                \"longitude\" => \"0\"\n                \"commission\" => 5.0\n                \"pickup\" => 1\n                \"delivery\" => 1\n                \"is_active\" => 1\n                \"charge_per_km\" => null\n                \"is_open\" => 1\n                \"vendor_type_id\" => 3\n                \"auto_assignment\" => 1\n                \"auto_accept\" => 1\n                \"allow_schedule_order\" => 0\n                \"has_sub_categories\" => 0\n                \"min_order\" => 50.0\n                \"max_order\" => 1000.0\n                \"use_subscription\" => 0\n                \"has_drivers\" => 1\n                \"prepare_time\" => null\n                \"prepare_time_unit\" => \"minutes\"\n                \"delivery_time\" => null\n                \"delivery_time_unit\" => \"minutes\"\n                \"in_order\" => 1\n                \"featured\" => 1\n                \"created_at\" => \"2025-06-09 23:38:38\"\n                \"updated_at\" => \"2025-06-09 23:38:38\"\n                \"deleted_at\" => null\n                \"creator_id\" => 1\n                \"reviews_count\" => 0\n              ]\n              #original: array:38 [\n                \"id\" => 1\n                \"name\" => \"Nafiss Market\"\n                \"description\" => \"<p>Nafiss demo market</p>\"\n                \"base_delivery_fee\" => 0.0\n                \"delivery_fee\" => 0.0\n                \"delivery_range\" => null\n                \"tax\" => null\n                \"phone\" => \"+***********\"\n                \"email\" => \"<EMAIL>\"\n                \"address\" => \"Doha\"\n                \"latitude\" => \"0\"\n                \"longitude\" => \"0\"\n                \"commission\" => 5.0\n                \"pickup\" => 1\n                \"delivery\" => 1\n                \"is_active\" => 1\n                \"charge_per_km\" => null\n                \"is_open\" => 1\n                \"vendor_type_id\" => 3\n                \"auto_assignment\" => 1\n                \"auto_accept\" => 1\n                \"allow_schedule_order\" => 0\n                \"has_sub_categories\" => 0\n                \"min_order\" => 50.0\n                \"max_order\" => 1000.0\n                \"use_subscription\" => 0\n                \"has_drivers\" => 1\n                \"prepare_time\" => null\n                \"prepare_time_unit\" => \"minutes\"\n                \"delivery_time\" => null\n                \"delivery_time_unit\" => \"minutes\"\n                \"in_order\" => 1\n                \"featured\" => 1\n                \"created_at\" => \"2025-06-09 23:38:38\"\n                \"updated_at\" => \"2025-06-09 23:38:38\"\n                \"deleted_at\" => null\n                \"creator_id\" => 1\n                \"reviews_count\" => 0\n              ]\n              #changes: []\n              #casts: array:12 [\n                \"id\" => \"integer\"\n                \"allow_schedule_order\" => \"boolean\"\n                \"has_sub_categories\" => \"boolean\"\n                \"has_subscription\" => \"boolean\"\n                \"use_subscription\" => \"boolean\"\n                \"vendor_type_id\" => \"int\"\n                \"pickup\" => \"int\"\n                \"delivery\" => \"int\"\n                \"is_active\" => \"int\"\n                \"reviews_count\" => \"int\"\n                \"is_open\" => \"boolean\"\n                \"deleted_at\" => \"datetime\"\n              ]\n              #classCastCache: []\n              #attributeCastCache: []\n              #dateFormat: null\n              #appends: array:12 [\n                0 => \"formatted_date\"\n                1 => \"logo\"\n                2 => \"feature_image\"\n                3 => \"rating\"\n                4 => \"can_rate\"\n                5 => \"slots\"\n                6 => \"is_package_vendor\"\n                7 => \"is_favourite\"\n                8 => \"has_subscription\"\n                9 => \"document_requested\"\n                10 => \"pending_document_approval\"\n                11 => \"description_url\"\n              ]\n              #dispatchesEvents: []\n              #observables: []\n              #relations: array:2 [\n                \"vendor_type\" => App\\Models\\VendorType {#2942\n                  #connection: \"mysql\"\n                  #table: \"vendor_types\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: true\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:10 [\n                    \"id\" => 3\n                    \"name\" => \"{\"en\":\"Grocery\"}\"\n                    \"color\" => \"#45b12f\"\n                    \"description\" => \"{\"en\":\"Buy grocery from your nearby markets\"}\"\n                    \"slug\" => \"grocery\"\n                    \"is_active\" => 1\n                    \"in_order\" => 3\n                    \"created_at\" => \"2021-06-30 16:59:15\"\n                    \"updated_at\" => \"2024-05-01 13:50:34\"\n                    \"deleted_at\" => null\n                  ]\n                  #original: array:10 [\n                    \"id\" => 3\n                    \"name\" => \"{\"en\":\"Grocery\"}\"\n                    \"color\" => \"#45b12f\"\n                    \"description\" => \"{\"en\":\"Buy grocery from your nearby markets\"}\"\n                    \"slug\" => \"grocery\"\n                    \"is_active\" => 1\n                    \"in_order\" => 3\n                    \"created_at\" => \"2021-06-30 16:59:15\"\n                    \"updated_at\" => \"2024-05-01 13:50:34\"\n                    \"deleted_at\" => null\n                  ]\n                  #changes: []\n                  #casts: array:5 [\n                    \"id\" => \"int\"\n                    \"is_active\" => \"int\"\n                    \"deleted_at\" => \"datetime\"\n                    \"name\" => \"array\"\n                    \"description\" => \"array\"\n                  ]\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: array:4 [\n                    0 => \"formatted_date\"\n                    1 => \"logo\"\n                    2 => \"website_header\"\n                    3 => \"has_banners\"\n                  ]\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  +timestamps: true\n                  +usesUniqueIds: false\n                  #hidden: array:1 [\n                    0 => \"media\"\n                  ]\n                  #visible: []\n                  #fillable: array:5 [\n                    0 => \"name\"\n                    1 => \"description\"\n                    2 => \"slug\"\n                    3 => \"is_active\"\n                    4 => \"color\"\n                  ]\n                  #guarded: array:1 [\n                    0 => \"*\"\n                  ]\n                  +mediaConversions: []\n                  +mediaCollections: []\n                  #deletePreservingMedia: false\n                  #unAttachedMediaLibraryItems: []\n                  #forceDeleting: false\n                  +translatable: array:2 [\n                    0 => \"name\"\n                    1 => \"description\"\n                  ]\n                  #translationLocale: null\n                }\n                \"fees\" => Illuminate\\Database\\Eloquent\\Collection {#2993\n                  #items: []\n                  #escapeWhenCastingToString: false\n                }\n              ]\n              #touches: []\n              +timestamps: true\n              +usesUniqueIds: false\n              #hidden: array:1 [\n                0 => \"media\"\n              ]\n              #visible: []\n              #fillable: array:18 [\n                0 => \"id\"\n                1 => \"name\"\n                2 => \"description\"\n                3 => \"delivery_fee\"\n                4 => \"delivery_range\"\n                5 => \"tax\"\n                6 => \"phone\"\n                7 => \"email\"\n                8 => \"address\"\n                9 => \"latitude\"\n                10 => \"longitude\"\n                11 => \"commission\"\n                12 => \"pickup\"\n                13 => \"delivery\"\n                14 => \"is_active\"\n                15 => \"charge_per_km\"\n                16 => \"is_open\"\n                17 => \"vendor_type_id\"\n              ]\n              #guarded: array:1 [\n                0 => \"*\"\n              ]\n              +mediaConversions: []\n              +mediaCollections: []\n              #deletePreservingMedia: false\n              #unAttachedMediaLibraryItems: []\n              #forceDeleting: false\n              #geographical_options: null\n            }\n            \"tags\" => Illuminate\\Database\\Eloquent\\Collection {#3000\n              #items: []\n              #escapeWhenCastingToString: false\n            }\n            \"timings\" => Illuminate\\Database\\Eloquent\\Collection {#2992\n              #items: []\n              #escapeWhenCastingToString: false\n            }\n            \"brand\" => null\n            \"media\" => Spatie\\MediaLibrary\\MediaCollections\\Models\\Collections\\MediaCollection {#2603\n              #items: array:1 [\n                0 => Spatie\\MediaLibrary\\MediaCollections\\Models\\Media {#2590\n                  #connection: \"mysql\"\n                  #table: \"media\"\n                  #primaryKey: \"id\"\n                  #keyType: \"int\"\n                  +incrementing: true\n                  #with: []\n                  #withCount: []\n                  +preventsLazyLoading: false\n                  #perPage: 15\n                  +exists: true\n                  +wasRecentlyCreated: false\n                  #escapeWhenCastingToString: false\n                  #attributes: array:18 [\n                    \"id\" => 38\n                    \"model_type\" => \"App\\Models\\Product\"\n                    \"model_id\" => 1\n                    \"uuid\" => \"8b9e6a7b-9c8b-4dc5-8c65-4a5eb13e2be2\"\n                    \"collection_name\" => \"default\"\n                    \"name\" => \"040087005017\"\n                    \"file_name\" => \"qgJfN-1749491499.jpg\"\n                    \"mime_type\" => \"image/jpeg\"\n                    \"disk\" => \"public\"\n                    \"conversions_disk\" => \"public\"\n                    \"size\" => 24027\n                    \"manipulations\" => \"[]\"\n                    \"custom_properties\" => \"[]\"\n                    \"responsive_images\" => \"[]\"\n                    \"order_column\" => 1\n                    \"created_at\" => \"2025-06-09 23:51:39\"\n                    \"updated_at\" => \"2025-06-09 23:51:39\"\n                    \"generated_conversions\" => \"[]\"\n                  ]\n                  #original: array:18 [\n                    \"id\" => 38\n                    \"model_type\" => \"App\\Models\\Product\"\n                    \"model_id\" => 1\n                    \"uuid\" => \"8b9e6a7b-9c8b-4dc5-8c65-4a5eb13e2be2\"\n                    \"collection_name\" => \"default\"\n                    \"name\" => \"040087005017\"\n                    \"file_name\" => \"qgJfN-1749491499.jpg\"\n                    \"mime_type\" => \"image/jpeg\"\n                    \"disk\" => \"public\"\n                    \"conversions_disk\" => \"public\"\n                    \"size\" => 24027\n                    \"manipulations\" => \"[]\"\n                    \"custom_properties\" => \"[]\"\n                    \"responsive_images\" => \"[]\"\n                    \"order_column\" => 1\n                    \"created_at\" => \"2025-06-09 23:51:39\"\n                    \"updated_at\" => \"2025-06-09 23:51:39\"\n                    \"generated_conversions\" => \"[]\"\n                  ]\n                  #changes: []\n                  #casts: array:4 [\n                    \"manipulations\" => \"array\"\n                    \"custom_properties\" => \"array\"\n                    \"generated_conversions\" => \"array\"\n                    \"responsive_images\" => \"array\"\n                  ]\n                  #classCastCache: []\n                  #attributeCastCache: []\n                  #dateFormat: null\n                  #appends: array:2 [\n                    0 => \"original_url\"\n                    1 => \"preview_url\"\n                  ]\n                  #dispatchesEvents: []\n                  #observables: []\n                  #relations: []\n                  #touches: []\n                  +timestamps: true\n                  +usesUniqueIds: false\n                  #hidden: []\n                  #visible: []\n                  #fillable: []\n                  #guarded: []\n                  #streamChunkSize: 1048576\n                }\n              ]\n              #escapeWhenCastingToString: false\n              +collectionName: null\n              +formFieldName: null\n            }\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: array:1 [\n            0 => \"media\"\n          ]\n          #visible: []\n          #fillable: array:15 [\n            0 => \"id\"\n            1 => \"name\"\n            2 => \"description\"\n            3 => \"price\"\n            4 => \"discount_price\"\n            5 => \"capacity\"\n            6 => \"unit\"\n            7 => \"package_count\"\n            8 => \"available_qty\"\n            9 => \"featured\"\n            10 => \"deliverable\"\n            11 => \"is_active\"\n            12 => \"vendor_id\"\n            13 => \"approved\"\n            14 => \"brand_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n          #forceDeleting: false\n          +translatable: array:2 [\n            0 => \"name\"\n            1 => \"description\"\n          ]\n          +digitalFilePath: \"secure/product/files\"\n          #translationLocale: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"userRolesSummary\" => null\n  ]\n  \"name\" => \"dashboard-livewire\"\n  \"view\" => \"livewire.dashboard\"\n  \"component\" => \"App\\Http\\Livewire\\DashboardLivewire\"\n  \"id\" => \"1W8rYiprddVIVFEQTPvU\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/livewire/message/dashboard-livewire", "action_name": "livewire.message", "controller_action": "Livewire\\Controllers\\HttpConnectionHandler", "uri": "POST livewire/message/{name}", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "middleware": "web", "duration": "3.39s", "peak_memory": "60MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2085009386 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2085009386\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-339313451 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">1W8rYiprddVIVFEQTPvU</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"18 characters\">dashboard-livewire</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"9 characters\">dashboard</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>l1715326798-0</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">3PVDklAsGnxE0G0VKMHX</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>l1715326798-1</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">UwI8FjKOot0HRfGxK9s2</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>l1715326798-2</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">JAWnP1mQ9n1GNA4aMfqr</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>l1715326798-3</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">eJsCQxnFodPyZnbEKKga</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>l1715326798-4</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">QBY2NlJzzVYutwTy5HYi</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">af081763</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>topSellingVendors</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>topRatedVendors</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>topCustomers</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>myTopSellingProducts</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>topSellingProducts</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>userRolesSummary</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">3484f620f03e7a8242a311048efcf8e420030c9c25b3b5aed78c2a3da1a17e77</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">smy6</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"22 characters\">fetchTopSellingVendors</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">j61t</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">fetchTopRatedVendors</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3cmh</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"17 characters\">fetchTopCustomers</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"5 characters\">h56gk</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"23 characters\">fetchTopSellingProducts</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-339313451\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-941075791 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">a2LbHGWQNNLmY6gVCne5FKh7HQikLG4WvBxpDdTi</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1105</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1252 characters\">XSRF-TOKEN=eyJpdiI6Imo1WGhLY2hCaS9ldEpZTWZJRVJuWkE9PSIsInZhbHVlIjoiUkpnTHVGVGh5UEIwMXZGNUUzcmw1ZDFoTElLK1N5cXVwYmVVN1NiS3JRbHc4YU5JbGZvTGgyb1A0d1YxN0IvbDlQdUlSeXZrbTg0YzBXcmxWdUZDVlFlelpDNFBzVDVMSllESWJaOXBiQnlzZ3RmUG9ITTV1TEtiNnNXc1YxV2IiLCJtYWMiOiJjOWVmMDQzMGE4MGI3NGQ5YmQ0Mjk2OTczNTgzN2JkZmVmZmJlODcwMTlmODUwYWIzNzZmNDY0Yjg1NzJkZGNiIiwidGFnIjoiIn0%3D; mart_session=eyJpdiI6Imc5ZDVRMlFxbUtKZHRqVTRkNmJlUFE9PSIsInZhbHVlIjoibmhGRWM0STkrbjJRVzIvY3Nwblp6UHZXeTNRczg3WXVMZUI4S01yaVMwZHBjcXNyc2haM215dnFIc3VuWlQxdnVsbXVBdkkydmprOEVhSnAzRWRtUGlGSFlPdG5kdmtNdDdIZGxwUWlobDU5Nk42eHZHTE81MjJLTSt5VHYrMG8iLCJtYWMiOiJiMTRlNjE3N2FmYmMwMTQxYzdhNWM2Yjc4YWM5ZmZjMjJjMmUwYzFkODdlOGE0MDkyY2NhODEwNDE5OTY1YzQwIiwidGFnIjoiIn0%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InRwaElMYWErVGI2Yy9OVWduTzBZbFE9PSIsInZhbHVlIjoiTm9JNFdxRlY2cHBLZnExay94NXF5d3IxYUx3c0F4U3RrV0ZDV3VCOWtGOUlWanhqSE1rYURxd0dvQ0lmN0czTmFZSEpPTkt5Q0EvVlRiUzI0QUh2dWxZQTdTVXY3R1YwckJINHFOMTA0Nml1d2VVc3VUUHlGdm9DYlhIQXlsdlhISmRKZUZvZjQ4WnpncGhOSWk0Y3RIRWJsRVVBL1VPWmk4NW9PVkNOdGhXdVhMMmgydEV6bDdIdzRlOFE5MXZoWmV2Smluejc4dys4V2ZKTWliREp2bThMa2lFK3JqOWhieXNZRlZNSjNLVT0iLCJtYWMiOiJmMzhjMTE1MGI3Yjg0YTcyZTUzODQzMjFlODUxZTA1ODU5ZDc3NDkxYjZjZmQxMTcwZDY0ZGJhNTI4NmQyYWE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">u=4</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941075791\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1177364510 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">a2LbHGWQNNLmY6gVCne5FKh7HQikLG4WvBxpDdTi</span>\"\n  \"<span class=sf-dump-key>mart_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Vc7UsPPH5JrGBTvJKavbUk09ZX9Bh1nJUvzzmLx7</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|RZFvnS3aNlH1wTyolS5zSPUV3TMxrN0M2u9SaTuy4fxJT97rc7wbOf80fhUz|$2y$10$J.BU9XOx8RR4rHoSl/k9K.BhaRSsCvP479bnKEa1oBMGBZJNqGQ8K</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1177364510\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-327314696 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 11 Jun 2025 12:21:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-327314696\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1115056679 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">a2LbHGWQNNLmY6gVCne5FKh7HQikLG4WvBxpDdTi</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>lan</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>c_link</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1115056679\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/livewire/message/dashboard-livewire", "action_name": "livewire.message", "controller_action": "Livewire\\Controllers\\HttpConnectionHandler"}, "badge": null}}