<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\UserToken;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class FCMTokenController extends Controller
{
    /**
     * Register or update FCM token for the authenticated user
     */
    public function register(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'token' => 'required|string|min:10',
                'device_type' => 'sometimes|string|in:android,ios,web',
                'device_id' => 'sometimes|string|max:255',
                'app_version' => 'sometimes|string|max:50',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $token = $request->input('token');
            $deviceType = $request->input('device_type', 'unknown');
            $deviceId = $request->input('device_id');
            $appVersion = $request->input('app_version');

            // Check if token already exists for this user
            $existingToken = UserToken::where('user_id', $user->id)
                ->where('token', $token)
                ->first();

            if ($existingToken) {
                // Update existing token
                $existingToken->update([
                    'device_type' => $deviceType,
                    'device_id' => $deviceId,
                    'app_version' => $appVersion,
                    'updated_at' => now(),
                ]);

                Log::info('FCM token updated', [
                    'user_id' => $user->id,
                    'token_id' => $existingToken->id,
                    'device_type' => $deviceType
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'FCM token updated successfully',
                    'data' => [
                        'token_id' => $existingToken->id,
                        'registered_at' => $existingToken->updated_at
                    ]
                ]);
            }

            // Create new token record
            $userToken = UserToken::create([
                'user_id' => $user->id,
                'token' => $token,
                'device_type' => $deviceType,
                'device_id' => $deviceId,
                'app_version' => $appVersion,
            ]);

            Log::info('FCM token registered', [
                'user_id' => $user->id,
                'token_id' => $userToken->id,
                'device_type' => $deviceType
            ]);

            return response()->json([
                'success' => true,
                'message' => 'FCM token registered successfully',
                'data' => [
                    'token_id' => $userToken->id,
                    'registered_at' => $userToken->created_at
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('FCM token registration failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to register FCM token'
            ], 500);
        }
    }

    /**
     * Remove FCM token for the authenticated user
     */
    public function remove(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'token' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            $token = $request->input('token');

            $deleted = UserToken::where('user_id', $user->id)
                ->where('token', $token)
                ->delete();

            if ($deleted) {
                Log::info('FCM token removed', [
                    'user_id' => $user->id,
                    'token' => substr($token, 0, 20) . '...'
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'FCM token removed successfully'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'FCM token not found'
            ], 404);

        } catch (\Exception $e) {
            Log::error('FCM token removal failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to remove FCM token'
            ], 500);
        }
    }

    /**
     * Get all FCM tokens for the authenticated user
     */
    public function list(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $tokens = UserToken::where('user_id', $user->id)
                ->whereNotNull('token')
                ->select(['id', 'device_type', 'device_id', 'app_version', 'created_at', 'updated_at'])
                ->orderBy('updated_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $tokens->map(function ($token) {
                    return [
                        'id' => $token->id,
                        'device_type' => $token->device_type,
                        'device_id' => $token->device_id,
                        'app_version' => $token->app_version,
                        'registered_at' => $token->created_at,
                        'last_updated' => $token->updated_at,
                    ];
                })
            ]);

        } catch (\Exception $e) {
            Log::error('FCM token list failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve FCM tokens'
            ], 500);
        }
    }

    /**
     * Test FCM notification for the authenticated user
     */
    public function test(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $tokens = UserToken::where('user_id', $user->id)
                ->whereNotNull('token')
                ->pluck('token')
                ->toArray();

            if (empty($tokens)) {
                return response()->json([
                    'success' => false,
                    'message' => 'No FCM tokens found for your account'
                ], 404);
            }

            $fcmService = app(\App\Services\FCMService::class);
            
            $success = $fcmService->sendCallNotification(
                $tokens,
                'Test Notification',
                'This is a test notification from your video call app',
                [
                    'type' => 'test',
                    'timestamp' => now()->toISOString(),
                    'user_id' => $user->id
                ],
                false
            );

            if ($success) {
                Log::info('Test FCM notification sent', [
                    'user_id' => $user->id,
                    'tokens_count' => count($tokens)
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Test notification sent successfully',
                    'data' => [
                        'tokens_count' => count($tokens),
                        'sent_at' => now()
                    ]
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test notification'
            ], 500);

        } catch (\Exception $e) {
            Log::error('Test FCM notification failed', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test notification'
            ], 500);
        }
    }
}
