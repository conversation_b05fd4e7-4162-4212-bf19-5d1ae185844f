version: '3.8'

services:
  webrtc-signaling:
    build: .
    container_name: webrtc-signaling-server
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - LOG_LEVEL=info
      - ENABLE_FILE_LOGGING=true
      - LARAVEL_API_URL=http://laravel-app:8000/api
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - webrtc-network
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    container_name: webrtc-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - webrtc-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    container_name: webrtc-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - webrtc-signaling
    restart: unless-stopped
    networks:
      - webrtc-network

volumes:
  redis-data:
    driver: local

networks:
  webrtc-network:
    driver: bridge
