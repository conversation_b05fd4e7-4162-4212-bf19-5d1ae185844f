# Frontend Integration Example

This document provides examples of how to integrate the video call FCM notifications with your frontend application.

## JavaScript/Web Integration

### 1. Firebase Configuration

```javascript
// firebase-config.js
import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';

const firebaseConfig = {
    apiKey: "your-api-key",
    authDomain: "your-project.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project.appspot.com",
    messagingSenderId: "123456789",
    appId: "your-app-id"
};

const app = initializeApp(firebaseConfig);
const messaging = getMessaging(app);

export { messaging };
```

### 2. FCM Token Registration

```javascript
// fcm-service.js
import { messaging } from './firebase-config.js';
import { getToken, onMessage } from 'firebase/messaging';

class FCMService {
    constructor() {
        this.token = null;
        this.apiBaseUrl = '/api';
    }

    async requestPermission() {
        try {
            const permission = await Notification.requestPermission();
            if (permission === 'granted') {
                console.log('Notification permission granted');
                return true;
            } else {
                console.log('Notification permission denied');
                return false;
            }
        } catch (error) {
            console.error('Error requesting notification permission:', error);
            return false;
        }
    }

    async getToken() {
        try {
            const token = await getToken(messaging, {
                vapidKey: 'your-vapid-key'
            });
            
            if (token) {
                this.token = token;
                await this.registerToken(token);
                return token;
            } else {
                console.log('No registration token available');
                return null;
            }
        } catch (error) {
            console.error('Error getting FCM token:', error);
            return null;
        }
    }

    async registerToken(token) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/fcm/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({
                    token: token,
                    device_type: 'web',
                    device_id: this.getDeviceId(),
                    app_version: '1.0.0'
                })
            });

            const result = await response.json();
            
            if (result.success) {
                console.log('FCM token registered successfully');
            } else {
                console.error('Failed to register FCM token:', result.message);
            }
        } catch (error) {
            console.error('Error registering FCM token:', error);
        }
    }

    setupMessageListener() {
        onMessage(messaging, (payload) => {
            console.log('Message received:', payload);
            this.handleNotification(payload);
        });
    }

    handleNotification(payload) {
        const { notification, data } = payload;
        
        switch (data.type) {
            case 'incoming_call':
                this.handleIncomingCall(data);
                break;
            case 'call_accepted':
                this.handleCallAccepted(data);
                break;
            case 'call_rejected':
                this.handleCallRejected(data);
                break;
            case 'call_ended':
                this.handleCallEnded(data);
                break;
            default:
                this.showGenericNotification(notification);
        }
    }

    handleIncomingCall(data) {
        // Show incoming call UI
        const callModal = document.getElementById('incoming-call-modal');
        const callerName = document.getElementById('caller-name');
        const callerPhoto = document.getElementById('caller-photo');
        
        callerName.textContent = data.caller_name;
        callerPhoto.src = data.caller_photo;
        callModal.style.display = 'block';
        
        // Play ringtone
        this.playRingtone();
        
        // Set up call actions
        this.setupCallActions(data.call_id);
    }

    setupCallActions(callId) {
        const acceptBtn = document.getElementById('accept-call-btn');
        const rejectBtn = document.getElementById('reject-call-btn');
        
        acceptBtn.onclick = () => this.acceptCall(callId);
        rejectBtn.onclick = () => this.rejectCall(callId);
    }

    async acceptCall(callId) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/calls/${callId}/accept`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                }
            });

            const result = await response.json();
            
            if (result.success) {
                this.hideIncomingCallModal();
                this.startVideoCall(callId);
            }
        } catch (error) {
            console.error('Error accepting call:', error);
        }
    }

    async rejectCall(callId) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/calls/${callId}/reject`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({
                    reason: 'User declined'
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.hideIncomingCallModal();
            }
        } catch (error) {
            console.error('Error rejecting call:', error);
        }
    }

    playRingtone() {
        const audio = new Audio('/sounds/ringtone.mp3');
        audio.loop = true;
        audio.play();
        
        // Store reference to stop later
        this.ringtoneAudio = audio;
    }

    stopRingtone() {
        if (this.ringtoneAudio) {
            this.ringtoneAudio.pause();
            this.ringtoneAudio = null;
        }
    }

    hideIncomingCallModal() {
        const callModal = document.getElementById('incoming-call-modal');
        callModal.style.display = 'none';
        this.stopRingtone();
    }

    getAuthToken() {
        // Return your authentication token
        return localStorage.getItem('auth_token');
    }

    getDeviceId() {
        // Generate or retrieve device ID
        let deviceId = localStorage.getItem('device_id');
        if (!deviceId) {
            deviceId = 'web_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('device_id', deviceId);
        }
        return deviceId;
    }
}

// Initialize FCM service
const fcmService = new FCMService();
export default fcmService;
```

### 3. HTML Structure

```html
<!-- incoming-call-modal.html -->
<div id="incoming-call-modal" class="call-modal" style="display: none;">
    <div class="call-modal-content">
        <div class="caller-info">
            <img id="caller-photo" src="" alt="Caller" class="caller-photo">
            <h2 id="caller-name">Caller Name</h2>
            <p>Incoming Video Call</p>
        </div>
        
        <div class="call-actions">
            <button id="reject-call-btn" class="btn btn-reject">
                <i class="icon-phone-hangup"></i>
                Decline
            </button>
            <button id="accept-call-btn" class="btn btn-accept">
                <i class="icon-phone"></i>
                Accept
            </button>
        </div>
    </div>
</div>
```

### 4. CSS Styling

```css
/* call-modal.css */
.call-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.call-modal-content {
    background: white;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.caller-info {
    margin-bottom: 30px;
}

.caller-photo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-bottom: 15px;
    object-fit: cover;
}

.call-actions {
    display: flex;
    justify-content: space-around;
    gap: 20px;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
}

.btn-accept {
    background: #4CAF50;
    color: white;
}

.btn-reject {
    background: #f44336;
    color: white;
}

.btn:hover {
    transform: scale(1.05);
}
```

### 5. Application Initialization

```javascript
// app.js
import fcmService from './fcm-service.js';

class VideoCallApp {
    constructor() {
        this.init();
    }

    async init() {
        // Request notification permission
        const permissionGranted = await fcmService.requestPermission();
        
        if (permissionGranted) {
            // Get FCM token and register it
            await fcmService.getToken();
            
            // Setup message listener
            fcmService.setupMessageListener();
        }

        // Setup other app functionality
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Setup call initiation button
        const callBtn = document.getElementById('initiate-call-btn');
        if (callBtn) {
            callBtn.addEventListener('click', this.initiateCall.bind(this));
        }
    }

    async initiateCall() {
        const receiverId = document.getElementById('receiver-select').value;
        
        try {
            const response = await fetch('/api/calls/initiate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${fcmService.getAuthToken()}`
                },
                body: JSON.stringify({
                    receiver_id: receiverId,
                    call_type: 'video'
                })
            });

            const result = await response.json();
            
            if (result.success) {
                console.log('Call initiated:', result.data.call_id);
                this.showOutgoingCallUI(result.data);
            }
        } catch (error) {
            console.error('Error initiating call:', error);
        }
    }

    showOutgoingCallUI(callData) {
        // Show outgoing call interface
        console.log('Showing outgoing call UI for:', callData.call_id);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new VideoCallApp();
});
```

## React Native Integration

### 1. FCM Setup

```javascript
// FCMService.js
import messaging from '@react-native-firebase/messaging';
import { Alert } from 'react-native';

class FCMService {
    constructor() {
        this.token = null;
        this.init();
    }

    async init() {
        await this.requestPermission();
        await this.getToken();
        this.setupMessageListener();
        this.setupBackgroundHandler();
    }

    async requestPermission() {
        const authStatus = await messaging().requestPermission();
        const enabled =
            authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
            authStatus === messaging.AuthorizationStatus.PROVISIONAL;

        if (enabled) {
            console.log('Authorization status:', authStatus);
            return true;
        }
        return false;
    }

    async getToken() {
        try {
            const token = await messaging().getToken();
            if (token) {
                this.token = token;
                await this.registerToken(token);
                return token;
            }
        } catch (error) {
            console.error('Error getting FCM token:', error);
        }
        return null;
    }

    async registerToken(token) {
        try {
            // Call your API to register the token
            const response = await fetch('/api/fcm/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.getAuthToken()}`
                },
                body: JSON.stringify({
                    token: token,
                    device_type: Platform.OS,
                    device_id: DeviceInfo.getUniqueId(),
                    app_version: DeviceInfo.getVersion()
                })
            });

            const result = await response.json();
            console.log('Token registration result:', result);
        } catch (error) {
            console.error('Error registering token:', error);
        }
    }

    setupMessageListener() {
        // Foreground message handler
        messaging().onMessage(async remoteMessage => {
            console.log('FCM message received:', remoteMessage);
            this.handleNotification(remoteMessage);
        });
    }

    setupBackgroundHandler() {
        // Background message handler
        messaging().setBackgroundMessageHandler(async remoteMessage => {
            console.log('Message handled in the background!', remoteMessage);
        });
    }

    handleNotification(remoteMessage) {
        const { notification, data } = remoteMessage;
        
        switch (data.type) {
            case 'incoming_call':
                this.showIncomingCallScreen(data);
                break;
            case 'call_accepted':
                this.handleCallAccepted(data);
                break;
            case 'call_rejected':
                this.handleCallRejected(data);
                break;
            case 'call_ended':
                this.handleCallEnded(data);
                break;
            default:
                Alert.alert(notification.title, notification.body);
        }
    }

    showIncomingCallScreen(data) {
        // Navigate to incoming call screen
        // This depends on your navigation setup
        NavigationService.navigate('IncomingCall', { callData: data });
    }
}

export default new FCMService();
```

### 2. Incoming Call Screen Component

```javascript
// IncomingCallScreen.js
import React, { useEffect } from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import Sound from 'react-native-sound';

const IncomingCallScreen = ({ route, navigation }) => {
    const { callData } = route.params;
    const [ringtone, setRingtone] = useState(null);

    useEffect(() => {
        // Play ringtone
        const sound = new Sound('ringtone.mp3', Sound.MAIN_BUNDLE, (error) => {
            if (error) {
                console.log('Failed to load sound', error);
                return;
            }
            sound.setNumberOfLoops(-1); // Loop indefinitely
            sound.play();
            setRingtone(sound);
        });

        return () => {
            if (ringtone) {
                ringtone.stop();
                ringtone.release();
            }
        };
    }, []);

    const acceptCall = async () => {
        try {
            const response = await fetch(`/api/calls/${callData.call_id}/accept`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${getAuthToken()}`
                }
            });

            const result = await response.json();
            
            if (result.success) {
                if (ringtone) {
                    ringtone.stop();
                }
                navigation.replace('VideoCall', { callId: callData.call_id });
            }
        } catch (error) {
            console.error('Error accepting call:', error);
        }
    };

    const rejectCall = async () => {
        try {
            const response = await fetch(`/api/calls/${callData.call_id}/reject`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${getAuthToken()}`
                },
                body: JSON.stringify({
                    reason: 'User declined'
                })
            });

            if (ringtone) {
                ringtone.stop();
            }
            navigation.goBack();
        } catch (error) {
            console.error('Error rejecting call:', error);
        }
    };

    return (
        <View style={styles.container}>
            <View style={styles.callerInfo}>
                <Image 
                    source={{ uri: callData.caller_photo }} 
                    style={styles.callerPhoto}
                />
                <Text style={styles.callerName}>{callData.caller_name}</Text>
                <Text style={styles.callType}>Incoming {callData.call_type} call</Text>
            </View>

            <View style={styles.actions}>
                <TouchableOpacity style={styles.rejectButton} onPress={rejectCall}>
                    <Text style={styles.buttonText}>Decline</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.acceptButton} onPress={acceptCall}>
                    <Text style={styles.buttonText}>Accept</Text>
                </TouchableOpacity>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#000',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: 50,
    },
    callerInfo: {
        alignItems: 'center',
        marginTop: 100,
    },
    callerPhoto: {
        width: 150,
        height: 150,
        borderRadius: 75,
        marginBottom: 20,
    },
    callerName: {
        fontSize: 28,
        color: 'white',
        fontWeight: 'bold',
        marginBottom: 10,
    },
    callType: {
        fontSize: 18,
        color: '#ccc',
    },
    actions: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        width: '100%',
        paddingHorizontal: 50,
    },
    acceptButton: {
        backgroundColor: '#4CAF50',
        width: 80,
        height: 80,
        borderRadius: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    rejectButton: {
        backgroundColor: '#f44336',
        width: 80,
        height: 80,
        borderRadius: 40,
        justifyContent: 'center',
        alignItems: 'center',
    },
    buttonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: 'bold',
    },
});

export default IncomingCallScreen;
```

This integration example shows how to:

1. **Register FCM tokens** with your Laravel backend
2. **Handle incoming notifications** in both web and mobile environments
3. **Display incoming call UI** with accept/reject functionality
4. **Integrate with your video call system** by calling the appropriate API endpoints

The key points are:
- Register FCM tokens when the app starts
- Listen for incoming notifications
- Handle different notification types appropriately
- Provide a smooth user experience for call management
