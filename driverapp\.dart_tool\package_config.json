{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_fe_analyzer_shared-82.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "_flutterfire_internals", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.54", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "adaptive_theme", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/adaptive_theme-3.7.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "analyzer", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/analyzer-7.4.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "ansicolor", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ansicolor-2.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "archive", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/archive-3.6.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "args", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "assets_audio_player_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/assets_audio_player_plus-3.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.12.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "audio_session", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/audio_session-0.1.25", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "auto_size_text_pk", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/auto_size_text_pk-3.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "awesome_notifications", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/awesome_notifications-0.10.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "barcode", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/barcode-2.2.9", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "basic_utils", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/basic_utils-5.8.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "bg_launcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/bg_launcher-0.1.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "bidi", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/bidi-2.0.13", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "boolean_selector", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "build", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build-2.4.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "build_config", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/build_config-1.1.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "built_collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/built_collection-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "cached_network_image", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cached_network_image_web-1.3.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "characters", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "checked_yaml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/checked_yaml-2.0.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "chewie", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/chewie-1.11.3", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "circular_countdown_timer", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/circular_countdown_timer-0.2.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cli_util", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cli_util-0.4.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cloud_firestore", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore-5.6.7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "cloud_firestore_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.7", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "cloud_firestore_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_web-4.4.7", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "colornames", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/colornames-0.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "contained_tab_bar_view", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/contained_tab_bar_view-0.8.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "container_tab_indicator", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/container_tab_indicator-0.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "convert", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/convert-3.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "country_picker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/country_picker-2.0.27", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "cross_file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "csslib", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/csslib-1.0.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "cupertino_icons", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "currency_formatter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/currency_formatter-2.2.3", "packageUri": "lib/", "languageVersion": "2.13"}, {"name": "custom_faqs", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/custom_faqs-3b21d689b16ef301f60b7d5c5d8bc7b1821852f2/", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "custom_sliding_segmented_control", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/custom_sliding_segmented_control-1.8.5", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "dart_either", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dart_either-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "dart_style", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dart_style-3.0.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "dartx", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dartx-1.2.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "dash_chat_2", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dash_chat_2-0.0.21", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "dbus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dbus-0.7.11", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "device_info_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus-9.1.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "device_info_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dio", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dio-5.8.0+1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "dio_http_cache_lts", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dio_http_cache_lts-0.4.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "dio_web_adapter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dio_web_adapter-2.1.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "disposebag", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/disposebag-1.5.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "dotted_line", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/dotted_line-3.2.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "double_back_to_close", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/double_back_to_close-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "easy_refresh", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/easy_refresh-3.4.0", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "enum_to_string", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/enum_to_string-2.2.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "equatable", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/equatable-2.0.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "eva_icons_flutter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/eva_icons_flutter-3.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "fake_async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_picker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_picker-10.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "file_selector_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_linux-0.9.3+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_macos-0.9.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file_selector_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_platform_interface-2.6.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "file_selector_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_selector_windows-0.9.3+4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "file_sizes", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file_sizes-1.0.6", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "firebase_auth", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-5.5.3", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_auth_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-7.6.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_auth_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.14.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_core", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-3.13.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-5.4.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.22.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_crashlytics", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_crashlytics-4.3.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_crashlytics_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_crashlytics_platform_interface-3.8.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_messaging", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging-15.2.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_messaging_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging_platform_interface-4.6.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_messaging_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_messaging_web-3.10.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_storage", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_storage-11.7.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "firebase_storage_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_storage_platform_interface-5.2.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_storage_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_storage_web-3.10.12", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firestore_chat", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firestore_chat-0.1.2", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "fixnum", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flag", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flag-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter", "rootUri": "file:///C:/dev/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_application_id", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_application_id-2.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_background", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_background-1.3.0+1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter_cache_manager", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_cache_manager-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_dropdown_alert", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_dropdown_alert-1.0.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_fgbg", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_fgbg-0.7.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_form_builder", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_form_builder-10.0.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_icons", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/git/flutter-icons-c83e143e940e2ed400b0201a5d8c97cf3bacabc3/", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_inappwebview", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview-6.1.5", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_internal_annotations", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_inappwebview_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_isolate", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_isolate-2.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_keyboard_visibility-6.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_platform_interface-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_keyboard_visibility_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_launcher_icons", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_launcher_icons-0.13.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "flutter_localizations", "rootUri": "file:///C:/dev/flutter/packages/flutter_localizations", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_luban", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_luban-0.1.15", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_markdown", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_markdown-0.7.7", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter_native_splash", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_native_splash-2.4.6", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_overboard", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_overboard-3.1.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_overlay_window", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_overlay_window-0.5.0", "packageUri": "lib/", "languageVersion": "2.16"}, {"name": "flutter_parsed_text", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_parsed_text-2.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_plugin_android_lifecycle", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_polyline_points", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_polyline_points-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "flutter_rating_bar", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_rating_bar-4.0.1", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "flutter_reverb", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_reverb-0.0.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "flutter_staggered_animations", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_staggered_animations-1.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_svg", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_svg-2.1.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter_test", "rootUri": "file:///C:/dev/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_typeahead", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_typeahead-5.2.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_web_plugins", "rootUri": "file:///C:/dev/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_widget_from_html", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_widget_from_html-0.10.6", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "flutter_widget_from_html_core", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_widget_from_html_core-0.10.6", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fwfh_cached_network_image", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fwfh_cached_network_image-0.7.0+7", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fwfh_chewie", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fwfh_chewie-0.7.1+4", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fwfh_just_audio", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fwfh_just_audio-0.9.0+3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fwfh_svg", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fwfh_svg-0.8.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fwfh_url_launcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fwfh_url_launcher-0.9.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "fwfh_webview", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fwfh_webview-0.9.0+2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "geoflutterfire2", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geoflutterfire2-2.3.15", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "geolocator", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator-10.1.1", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "geolocator_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_android-4.6.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_apple", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_apple-2.3.13", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_platform_interface-4.2.6", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geolocator_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_web-2.2.1", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "geolocator_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/geolocator_windows-0.2.5", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "geor<PERSON>e", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/georange-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "get_it", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/get_it-7.7.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "glob", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/glob-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "google_fonts", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_fonts-5.1.0", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "google_maps", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps-8.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_maps_flutter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter-2.12.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "google_maps_flutter_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_android-2.16.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "google_maps_flutter_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_ios-2.15.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_maps_flutter_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_platform_interface-2.11.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_maps_flutter_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_maps_flutter_web-0.5.12", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "google_nav_bar", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/google_nav_bar-5.0.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "hand_signature", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hand_signature-3.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "html", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/html-0.15.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-0.13.6", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "http_parser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "hugeicons", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/hugeicons-0.0.11", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "image", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image-4.5.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "image_picker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker-1.1.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "image_picker_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_android-0.8.12+23", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "image_picker_for_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_for_web-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_ios-0.8.12+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_linux-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_macos-0.2.1+2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_platform_interface-2.10.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image_picker_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/image_picker_windows-0.2.1+1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "in_app_review", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_review-2.0.10", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "in_app_review_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/in_app_review_platform_interface-2.0.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "inspection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/inspection-0.0.13", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "intl", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.18.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "ionicons", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ionicons-0.2.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "jiffy", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/jiffy-6.4.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "js", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.7.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "json_annotation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_annotation-4.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "json_serializable", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/json_serializable-6.9.5", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "just_audio", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/just_audio-0.9.46", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "just_audio_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/just_audio_platform_interface-4.5.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "just_audio_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/just_audio_web-0.4.15", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "jwt_decoder", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/jwt_decoder-2.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "laravel_echo_null", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/laravel_echo_null-0.1.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "leak_tracker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.8", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lecho", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lecho-0.0.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "line_icons", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/line_icons-2.0.3", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "localize_and_translate", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/localize_and_translate-4.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "logger", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logger-2.5.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "logging", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "map_launcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/map_launcher-3.5.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "markdown", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/markdown-7.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "matcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "measure_size", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/measure_size-4.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "meta", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "mime", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/mime-2.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "nested", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "octo_image", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/octo_image-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "open_file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/open_file-3.5.10", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "open_file_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/open_file_android-1.0.6", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "open_file_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/open_file_ios-1.0.3", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "open_file_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/open_file_linux-0.0.5", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "open_file_mac", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/open_file_mac-1.0.3", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "open_file_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/open_file_platform_interface-1.0.3", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "open_file_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/open_file_web-0.0.4", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "open_file_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/open_file_windows-0.0.3", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "os_detect", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/os_detect-2.0.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "package_config", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_config-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "package_info_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus-8.3.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "package_info_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "path", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_drawing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_drawing-1.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path_parsing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_parsing-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pdf", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pdf-3.11.3", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "permission_handler", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler-12.0.0+1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_android-13.0.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_apple", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_apple-9.4.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler_html", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_html-0.1.3+5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "petitparser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "pin_code_fields", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pin_code_fields-8.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "pinenacl", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pinenacl-0.6.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "platform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pointer_interceptor", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pointer_interceptor-0.10.1+2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pointer_interceptor_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pointer_interceptor_ios-0.10.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pointer_interceptor_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pointer_interceptor_platform_interface-0.10.0+1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "pointer_interceptor_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pointer_interceptor_web-0.10.2+1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "pointycastle", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pointycastle-4.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "pretty_dio_logger", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pretty_dio_logger-1.4.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "provider", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "pub_semver", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pub_semver-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "pubspec_parse", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pubspec_parse-1.5.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "pull_to_refresh_flutter3", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pull_to_refresh_flutter3-2.0.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pusher_channels_flutter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pusher_channels_flutter-2.5.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "pusher_client_socket", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pusher_client_socket-0.0.5", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "qr", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr-3.0.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "qr_code_scanner_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/qr_code_scanner_plus-2.0.10+1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "quickalert", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/quickalert-1.1.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "quiver", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/quiver-3.2.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "random_string", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/random_string-2.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "rolling_switch", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rolling_switch-0.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "rx_shared_preferences", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rx_shared_preferences-3.1.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "rx_storage", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rx_storage-2.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "rxdart", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rxdart-0.27.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "rxdart_ext", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/rxdart_ext-0.2.9", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sanitize_html", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sanitize_html-2.1.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "schedulers", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/schedulers-1.0.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "shared_preferences", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.10", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "shared_preferences_foundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "simple_flutter_reverb", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/simple_flutter_reverb-0.0.3", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "singleton", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/singleton-0.0.3-nullsafety", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sky_engine", "rootUri": "file:///C:/dev/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sliding_up_panel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sliding_up_panel-2.0.0+1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "smooth_page_indicator", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/smooth_page_indicator-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "socket_io_client", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/socket_io_client-3.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "socket_io_common", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/socket_io_common-3.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "source_gen", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_gen-2.0.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "source_helper", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_helper-1.3.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "source_span", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sprintf", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sqflite", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_android-2.4.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_common", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_common-2.5.5", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_darwin", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_darwin-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/sqflite_platform_interface-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "stack_trace", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stacked", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stacked-3.4.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "stacked_shared", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stacked_shared-1.4.1", "packageUri": "lib/", "languageVersion": "2.16"}, {"name": "stream_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "stream_transform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_transform-2.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "string_scanner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "supercharged", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/supercharged-2.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "supercharged_dart", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/supercharged_dart-2.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "syncfusion_flutter_core", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/syncfusion_flutter_core-27.2.5", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "syncfusion_flutter_datagrid", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/syncfusion_flutter_datagrid-27.2.5", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "synchronized", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/synchronized-3.3.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "term_glyph", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "time", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/time-2.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "timelines_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/timelines_plus-1.0.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "translator", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/translator-0.1.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "typed_data", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "universal_io", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/universal_io-2.2.2", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "upgrader", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/upgrader-11.3.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "url_launcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher-6.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_android-6.3.16", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_ios", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_ios-6.3.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "url_launcher_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_linux-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_macos", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_macos-3.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_web-2.4.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/url_launcher_windows-3.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "uuid", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_graphics", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics-1.1.18", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_codec", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics_codec-1.1.13", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_compiler", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics_compiler-1.1.16", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_math", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "velocity_x", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/velocity_x-4.3.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "version", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/version-3.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "video_player", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/video_player-2.9.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "video_player_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/video_player_android-2.8.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "video_player_avfoundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/video_player_avfoundation-2.7.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "video_player_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/video_player_platform_interface-6.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "video_player_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/video_player_web-2.3.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vm_service", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-14.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "vxstate", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vxstate-2.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "wakelock_plus", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/wakelock_plus-1.3.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "wakelock_plus_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/wakelock_plus_platform_interface-1.2.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "watcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/watcher-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket-1.0.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_channel-3.0.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web_socket_client", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web_socket_client-0.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "webview_flutter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/webview_flutter-4.11.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "webview_flutter_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/webview_flutter_android-4.4.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "webview_flutter_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/webview_flutter_platform_interface-2.11.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "webview_flutter_wkwebview", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/webview_flutter_wkwebview-3.20.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "win32", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32-5.12.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "win32_registry", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/win32_registry-1.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "xdg_directories", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "yaml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "fuodz", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.7"}], "generated": "2025-06-11T11:31:31.657361Z", "generator": "pub", "generatorVersion": "3.7.2", "flutterRoot": "file:///C:/dev/flutter", "flutterVersion": "3.29.2", "pubCache": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache"}