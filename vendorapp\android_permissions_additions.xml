<!-- Add these permissions to your existing vendorapp/android/app/src/main/AndroidManifest.xml -->
<!-- Add these BEFORE the <application> tag -->

<!-- WebRTC Permissions -->
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

<!-- Network Permissions (if not already present) -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

<!-- Wake Lock Permission -->
<uses-permission android:name="android.permission.WAKE_LOCK" />

<!-- Bluetooth Permissions (for audio routing) -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

<!-- Hardware Features -->
<uses-feature android:name="android.hardware.camera" android:required="true" />
<uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />
<uses-feature android:name="android.hardware.microphone" android:required="true" />

<!-- Also add this to your existing <application> tag -->
<!-- android:usesCleartextTraffic="true" (for development) -->
<!-- android:hardwareAccelerated="true" (if not already present) -->
