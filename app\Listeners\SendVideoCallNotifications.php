<?php

namespace App\Listeners;

use App\Events\CallInitiated;
use App\Events\CallStatusChanged;
use App\Models\VideoCall;
use App\Notifications\VideoCallNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendVideoCallNotifications implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle call initiated events.
     */
    public function handleCallInitiated(CallInitiated $event)
    {
        $videoCall = $event->videoCall;
        
        // Notify the receiver about incoming call
        $videoCall->receiver->notify(
            new VideoCallNotification($videoCall, 'incoming_call')
        );
    }

    /**
     * Handle call status changed events.
     */
    public function handleCallStatusChanged(CallStatusChanged $event)
    {
        $videoCall = $event->videoCall;
        $previousStatus = $event->previousStatus;

        switch ($videoCall->status) {
            case VideoCall::STATUS_ACCEPTED:
                // Notify caller that call was accepted
                $videoCall->caller->notify(
                    new VideoCallNotification($videoCall, 'call_accepted')
                );
                break;

            case VideoCall::STATUS_REJECTED:
                // Notify caller that call was rejected
                $videoCall->caller->notify(
                    new VideoCallNotification($videoCall, 'call_rejected')
                );
                break;

            case VideoCall::STATUS_ENDED:
                // Notify both participants that call ended
                $videoCall->caller->notify(
                    new VideoCallNotification($videoCall, 'call_ended')
                );
                $videoCall->receiver->notify(
                    new VideoCallNotification($videoCall, 'call_ended')
                );
                break;

            case VideoCall::STATUS_MISSED:
                // Notify caller that call was missed
                $videoCall->caller->notify(
                    new VideoCallNotification($videoCall, 'call_missed')
                );
                break;
        }
    }

    /**
     * Register the listeners for the subscriber.
     */
    public function subscribe($events)
    {
        $events->listen(
            CallInitiated::class,
            [SendVideoCallNotifications::class, 'handleCallInitiated']
        );

        $events->listen(
            CallStatusChanged::class,
            [SendVideoCallNotifications::class, 'handleCallStatusChanged']
        );
    }
}
