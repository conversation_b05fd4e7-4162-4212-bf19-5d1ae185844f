<?php

return [
    'driver' => env('FCM_PROTOCOL', 'http'),
    'log_enabled' => false,

    'http' => [
        'server_key' => "",
        'sender_id' => "",
        'server_send_url' => 'https://fcm.googleapis.com/fcm/send',
        'server_group_url' => 'https://android.googleapis.com/gcm/notification',
        'timeout' => 30.0, // in second
    ],

    /*
    |--------------------------------------------------------------------------
    | Video Call Notification Settings
    |--------------------------------------------------------------------------
    */
    'send_in_local' => env('FCM_SEND_IN_LOCAL', false),

    'video_calls' => [
        'channels' => [
            'incoming_call_channel' => [
                'name' => 'Incoming Calls',
                'description' => 'Notifications for incoming video/audio calls',
                'importance' => 'high',
                'sound' => 'call_ringtone',
            ],
            'call_status_channel' => [
                'name' => 'Call Status',
                'description' => 'Notifications for call status updates',
                'importance' => 'default',
                'sound' => 'default',
            ],
        ],

        'sounds' => [
            'android' => [
                'incoming_call' => 'call_ringtone',
                'status_change' => 'default',
            ],
            'ios' => [
                'incoming_call' => 'call_ringtone.caf',
                'status_change' => 'default',
            ],
        ],

        'colors' => [
            'incoming_call' => '#4CAF50',
            'call_status' => '#2196F3',
            'missed_call' => '#FF5722',
        ],

        'timeouts' => [
            'incoming_call_timeout' => env('CALL_TIMEOUT_SECONDS', 60),
            'missed_call_threshold' => env('MISSED_CALL_THRESHOLD_SECONDS', 300),
        ],
    ],
];
