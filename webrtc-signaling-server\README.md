# WebRTC Signaling Server

A dedicated Node.js WebSocket signaling server using Socket.IO for real-time WebRTC communication between Flutter clients.

## Features

- 🚀 **Real-time WebRTC signaling** - <PERSON><PERSON> offers, answers, and ICE candidates
- 👥 **Multi-user support** - Manages multiple concurrent video calls
- 🔐 **Authentication** - JWT token validation with Laravel integration
- 📊 **Metrics & Monitoring** - Built-in performance and usage analytics
- 🛡️ **Security** - Rate limiting, input validation, and sanitization
- 🐳 **Docker support** - Easy deployment with Docker and Docker Compose
- 📝 **Comprehensive logging** - Winston-based logging with multiple transports
- 🔄 **Auto-cleanup** - Automatic cleanup of inactive calls and connections

## Architecture

```
Flutter App A ←→ WebSocket Server ←→ Flutter App B
     ↓                                    ↑
     └────── Direct WebRTC P2P ──────────┘
```

The server acts as a pure signaling relay without storing any persistent data.

## Quick Start

### Prerequisites

- Node.js 16+ 
- npm or yarn
- Redis (optional, for scaling)

### Installation

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd webrtc-signaling-server
npm install
```

2. **Configure environment:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Start the server:**
```bash
# Development
npm run dev

# Production
npm start
```

The server will start on `http://localhost:3001`

### Docker Deployment

1. **Using Docker Compose (recommended):**
```bash
docker-compose up -d
```

2. **Using Docker only:**
```bash
docker build -t webrtc-signaling-server .
docker run -p 3001:3001 webrtc-signaling-server
```

## API Endpoints

### Health Check
```
GET /health
```
Returns server status and metrics.

### Metrics
```
GET /metrics
```
Returns detailed performance metrics.

## WebSocket Events

### Client → Server

#### Join Call
```javascript
socket.emit('join-call', {
  call_id: 'call_123'
});
```

#### Send Offer
```javascript
socket.emit('webrtc-offer', {
  call_id: 'call_123',
  target_user_id: 'user_456',
  offer: {
    type: 'offer',
    sdp: '...'
  }
});
```

#### Send Answer
```javascript
socket.emit('webrtc-answer', {
  call_id: 'call_123',
  target_user_id: 'user_123',
  answer: {
    type: 'answer',
    sdp: '...'
  }
});
```

#### Send ICE Candidate
```javascript
socket.emit('ice-candidate', {
  call_id: 'call_123',
  target_user_id: 'user_456',
  candidate: {
    candidate: 'candidate:...',
    sdpMid: '0',
    sdpMLineIndex: 0
  }
});
```

#### Leave Call
```javascript
socket.emit('leave-call', {
  call_id: 'call_123'
});
```

### Server → Client

#### Call Joined
```javascript
socket.on('call-joined', (data) => {
  // User successfully joined call
  console.log('Joined call:', data.call_id);
});
```

#### User Joined
```javascript
socket.on('user-joined', (data) => {
  // Another user joined the call
  console.log('User joined:', data.user_id);
});
```

#### Offer Received
```javascript
socket.on('webrtc-offer-received', (data) => {
  // Received WebRTC offer
  const { from_user_id, offer } = data;
  // Handle offer...
});
```

#### Answer Received
```javascript
socket.on('webrtc-answer-received', (data) => {
  // Received WebRTC answer
  const { from_user_id, answer } = data;
  // Handle answer...
});
```

#### ICE Candidate Received
```javascript
socket.on('ice-candidate-received', (data) => {
  // Received ICE candidate
  const { from_user_id, candidate } = data;
  // Handle ICE candidate...
});
```

#### User Left
```javascript
socket.on('user-left', (data) => {
  // User left the call
  console.log('User left:', data.user_id);
});
```

#### Error
```javascript
socket.on('error', (error) => {
  // Handle error
  console.error('Socket error:', error);
});
```

## Authentication

The server supports multiple authentication methods:

### 1. JWT Token (Recommended)
```javascript
const socket = io('ws://localhost:3001', {
  auth: {
    token: 'your-jwt-token'
  }
});
```

### 2. Query Parameter
```javascript
const socket = io('ws://localhost:3001?token=your-jwt-token');
```

### 3. Authorization Header
```javascript
const socket = io('ws://localhost:3001', {
  extraHeaders: {
    'Authorization': 'Bearer your-jwt-token'
  }
});
```

### 4. Development Mode (Simple User ID)
```javascript
// Only when ALLOW_DEV_TOKENS=true
const socket = io('ws://localhost:3001', {
  auth: {
    token: '123' // User ID as string
  }
});
```

## Configuration

### Environment Variables

Key configuration options:

```env
# Server
NODE_ENV=production
PORT=3001

# Authentication
JWT_SECRET=your-secret
LARAVEL_API_URL=http://localhost:8000/api
ALLOW_DEV_TOKENS=false

# Security
ALLOWED_ORIGINS=http://localhost:3000
MAX_CONNECTIONS_PER_IP=10
MAX_REQUESTS_PER_MINUTE=100

# Logging
LOG_LEVEL=info
ENABLE_FILE_LOGGING=true
```

See `.env.example` for all available options.

## Flutter Integration

### 1. Add Dependencies
```yaml
dependencies:
  socket_io_client: ^2.0.3+1
  flutter_webrtc: ^0.9.48
```

### 2. Connect to Server
```dart
import 'package:socket_io_client/socket_io_client.dart' as IO;

class SignalingService {
  late IO.Socket socket;
  
  void connect(String userId, String token) {
    socket = IO.io('ws://localhost:3001', 
      IO.OptionBuilder()
        .setAuth({'token': token})
        .setTransports(['websocket'])
        .build()
    );
    
    socket.connect();
    setupEventListeners();
  }
  
  void setupEventListeners() {
    socket.on('connect', (_) => print('Connected'));
    socket.on('webrtc-offer-received', handleOffer);
    socket.on('webrtc-answer-received', handleAnswer);
    socket.on('ice-candidate-received', handleIceCandidate);
  }
  
  void joinCall(String callId) {
    socket.emit('join-call', {'call_id': callId});
  }
  
  void sendOffer(String callId, String targetUserId, Map<String, dynamic> offer) {
    socket.emit('webrtc-offer', {
      'call_id': callId,
      'target_user_id': targetUserId,
      'offer': offer
    });
  }
}
```

## Monitoring

### Health Check
```bash
curl http://localhost:3001/health
```

### Metrics
```bash
curl http://localhost:3001/metrics
```

### Logs
```bash
# View logs
tail -f logs/combined.log

# View errors only
tail -f logs/error.log
```

## Scaling

### Horizontal Scaling with Redis

1. **Enable Redis in environment:**
```env
REDIS_HOST=localhost
REDIS_PORT=6379
```

2. **Use Redis adapter:**
```javascript
// Add to server.js
const redisAdapter = require('@socket.io/redis-adapter');
const { createClient } = require('redis');

const pubClient = createClient({ host: 'localhost', port: 6379 });
const subClient = pubClient.duplicate();

io.adapter(redisAdapter(pubClient, subClient));
```

### Load Balancing

Use nginx or a load balancer with sticky sessions:

```nginx
upstream signaling_servers {
    ip_hash;
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
    server 127.0.0.1:3003;
}

server {
    listen 80;
    location / {
        proxy_pass http://signaling_servers;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## Security

### Rate Limiting
- Connection limits per IP
- Message rate limits per user
- Message size limits

### Input Validation
- All messages validated with Joi schemas
- XSS and injection prevention
- Message sanitization

### Authentication
- JWT token validation
- Laravel API integration
- User permission checks

## Development

### Running Tests
```bash
npm test
```

### Linting
```bash
npm run lint
```

### Development Mode
```bash
npm run dev
```

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check authentication token
   - Verify CORS settings
   - Check firewall/network

2. **Messages Not Delivered**
   - Verify user is in call
   - Check target user connection
   - Review server logs

3. **High Memory Usage**
   - Enable cleanup intervals
   - Check for connection leaks
   - Monitor metrics endpoint

### Debug Mode
```env
DEBUG_MODE=true
LOG_LEVEL=debug
```

## Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new features
4. Ensure all tests pass
5. Submit pull request

## License

MIT License - see LICENSE file for details.
