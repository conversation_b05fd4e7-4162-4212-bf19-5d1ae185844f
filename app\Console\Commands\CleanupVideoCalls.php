<?php

namespace App\Console\Commands;

use App\Models\VideoCall;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CleanupVideoCalls extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'calls:cleanup 
                            {--days=30 : Number of days to keep call records}
                            {--dry-run : Show what would be deleted without actually deleting}
                            {--mark-missed : Mark pending calls older than 5 minutes as missed}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old video call records and mark abandoned calls as missed';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = $this->option('days');
        $dryRun = $this->option('dry-run');
        $markMissed = $this->option('mark-missed');

        $this->info("Video Call Cleanup - " . ($dryRun ? 'DRY RUN MODE' : 'LIVE MODE'));
        $this->info("Keeping records from the last {$days} days");

        if ($markMissed) {
            $this->markMissedCalls($dryRun);
        }

        $this->cleanupOldRecords($days, $dryRun);

        $this->info('Cleanup completed!');
    }

    /**
     * Mark pending calls older than 5 minutes as missed
     */
    private function markMissedCalls($dryRun = false)
    {
        $this->info("\n--- Marking Missed Calls ---");

        $cutoffTime = Carbon::now()->subMinutes(5);
        
        $missedCallsQuery = VideoCall::where('status', VideoCall::STATUS_PENDING)
            ->where('initiated_at', '<', $cutoffTime);

        $count = $missedCallsQuery->count();

        if ($count > 0) {
            $this->info("Found {$count} pending calls older than 5 minutes");

            if (!$dryRun) {
                $updated = $missedCallsQuery->update([
                    'status' => VideoCall::STATUS_MISSED,
                    'ended_at' => Carbon::now(),
                ]);

                $this->info("Marked {$updated} calls as missed");
            } else {
                $this->warn("DRY RUN: Would mark {$count} calls as missed");
            }
        } else {
            $this->info("No pending calls found that need to be marked as missed");
        }
    }

    /**
     * Clean up old call records
     */
    private function cleanupOldRecords($days, $dryRun = false)
    {
        $this->info("\n--- Cleaning Up Old Records ---");

        $cutoffDate = Carbon::now()->subDays($days);
        
        $oldCallsQuery = VideoCall::where('initiated_at', '<', $cutoffDate);
        $count = $oldCallsQuery->count();

        if ($count > 0) {
            $this->info("Found {$count} call records older than {$days} days");

            if (!$dryRun) {
                // Show breakdown by status before deletion
                $statusBreakdown = VideoCall::where('initiated_at', '<', $cutoffDate)
                    ->selectRaw('status, COUNT(*) as count')
                    ->groupBy('status')
                    ->get();

                $this->table(['Status', 'Count'], $statusBreakdown->map(function ($item) {
                    return [$item->status, $item->count];
                })->toArray());

                $deleted = $oldCallsQuery->delete();
                $this->info("Deleted {$deleted} old call records");
            } else {
                // Show what would be deleted
                $statusBreakdown = VideoCall::where('initiated_at', '<', $cutoffDate)
                    ->selectRaw('status, COUNT(*) as count')
                    ->groupBy('status')
                    ->get();

                $this->warn("DRY RUN: Would delete {$count} records:");
                $this->table(['Status', 'Count'], $statusBreakdown->map(function ($item) {
                    return [$item->status, $item->count];
                })->toArray());
            }
        } else {
            $this->info("No old call records found to clean up");
        }
    }
}
