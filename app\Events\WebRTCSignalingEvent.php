<?php

namespace App\Events;

use App\Models\VideoCall;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WebRTCSignalingEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $videoCall;
    public $signalingType;
    public $signalingData;

    /**
     * Create a new event instance.
     */
    public function __construct(VideoCall $videoCall, string $signalingType, array $signalingData)
    {
        $this->videoCall = $videoCall;
        $this->signalingType = $signalingType;
        $this->signalingData = $signalingData;
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        // Broadcast to call-specific channel
        return [
            new PrivateChannel('call.' . $this->videoCall->call_id),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'webrtc.' . $this->signalingType;
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        $data = [
            'call_id' => $this->videoCall->call_id,
            'type' => $this->signalingType,
            'data' => $this->signalingData,
            'timestamp' => now()->toISOString(),
        ];

        // Add participant information based on signaling type
        switch ($this->signalingType) {
            case 'offer':
                $data['from'] = [
                    'user_id' => $this->videoCall->caller_id,
                    'name' => $this->videoCall->caller->name,
                ];
                $data['to'] = [
                    'user_id' => $this->videoCall->receiver_id,
                    'name' => $this->videoCall->receiver->name,
                ];
                break;

            case 'answer':
                $data['from'] = [
                    'user_id' => $this->videoCall->receiver_id,
                    'name' => $this->videoCall->receiver->name,
                ];
                $data['to'] = [
                    'user_id' => $this->videoCall->caller_id,
                    'name' => $this->videoCall->caller->name,
                ];
                break;

            case 'ice-candidate':
                $fromUserId = $this->signalingData['from_user_id'] ?? null;
                $toUserId = $fromUserId === $this->videoCall->caller_id 
                    ? $this->videoCall->receiver_id 
                    : $this->videoCall->caller_id;

                $data['from'] = [
                    'user_id' => $fromUserId,
                ];
                $data['to'] = [
                    'user_id' => $toUserId,
                ];
                break;

            case 'connection-state':
                $data['updated_by'] = $this->signalingData['updated_by'] ?? null;
                break;
        }

        return $data;
    }

    /**
     * Determine if this event should broadcast.
     */
    public function broadcastWhen(): bool
    {
        // Only broadcast if call is in active states
        return in_array($this->videoCall->status, [
            VideoCall::STATUS_PENDING,
            VideoCall::STATUS_ACCEPTED,
        ]);
    }
}
