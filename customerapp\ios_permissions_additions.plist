<!-- Add these entries to your existing customerapp/ios/Runner/Info.plist -->
<!-- Add these inside the <dict> tag -->

<!-- WebRTC Permissions -->
<key>NSCameraUsageDescription</key>
<string>This app needs access to camera for video calls with vendors</string>
<key>NSMicrophoneUsageDescription</key>
<string>This app needs access to microphone for voice and video calls with vendors</string>

<!-- Local Network Permission -->
<key>NSLocalNetworkUsageDescription</key>
<string>This app needs access to local network for peer-to-peer video calls</string>

<!-- Background Modes (add to existing array or create new) -->
<key>UIBackgroundModes</key>
<array>
	<string>audio</string>
	<string>voip</string>
	<string>background-fetch</string>
	<string>remote-notification</string>
</array>

<!-- App Transport Security (for development) -->
<key>NSAppTransportSecurity</key>
<dict>
	<key>NSAllowsArbitraryLoads</key>
	<true/>
	<key>NSAllowsLocalNetworking</key>
	<true/>
</dict>
