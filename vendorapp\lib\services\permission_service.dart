import 'package:permission_handler/permission_handler.dart';
import 'package:logger/logger.dart';
import 'package:flutter/material.dart';

class PermissionService {
  static final Logger _logger = Logger();

  /// Request all necessary permissions for video calling
  static Future<bool> requestAllPermissions() async {
    try {
      _logger.i('Vendor requesting permissions...');
      
      final permissions = await [
        Permission.camera,
        Permission.microphone,
        Permission.notification,
      ].request();

      final cameraGranted = permissions[Permission.camera]?.isGranted ?? false;
      final microphoneGranted = permissions[Permission.microphone]?.isGranted ?? false;
      final notificationGranted = permissions[Permission.notification]?.isGranted ?? false;

      _logger.i('Vendor permissions status:');
      _logger.i('Camera: $cameraGranted');
      _logger.i('Microphone: $microphoneGranted');
      _logger.i('Notification: $notificationGranted');

      // Camera and microphone are essential for video calls
      final essentialPermissionsGranted = cameraGranted && microphoneGranted;
      
      if (!essentialPermissionsGranted) {
        _logger.w('Vendor essential permissions not granted');
      }

      return essentialPermissionsGranted;
    } catch (e) {
      _logger.e('Error requesting vendor permissions: $e');
      return false;
    }
  }

  /// Check if camera and microphone permissions are granted
  static Future<bool> checkEssentialPermissions() async {
    try {
      final cameraStatus = await Permission.camera.status;
      final microphoneStatus = await Permission.microphone.status;

      return cameraStatus.isGranted && microphoneStatus.isGranted;
    } catch (e) {
      _logger.e('Error checking vendor permissions: $e');
      return false;
    }
  }

  /// Check if any permission is permanently denied
  static Future<bool> hasPermissionsPermanentlyDenied() async {
    try {
      final cameraStatus = await Permission.camera.status;
      final microphoneStatus = await Permission.microphone.status;

      return cameraStatus.isPermanentlyDenied || microphoneStatus.isPermanentlyDenied;
    } catch (e) {
      _logger.e('Error checking permanently denied permissions: $e');
      return false;
    }
  }

  /// Show permission dialog for vendors
  static Future<bool> showPermissionDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Camera & Microphone Access'),
          content: const Text(
            'To make video calls with customers, we need access to your camera and microphone. '
            'This allows you to show products and communicate with customers during consultations.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Not Now'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Allow Access'),
            ),
          ],
        );
      },
    );

    if (result == true) {
      return await requestAllPermissions();
    }

    return false;
  }

  /// Show settings dialog for permanently denied permissions
  static Future<void> showSettingsDialog(BuildContext context) async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Permissions Required'),
          content: const Text(
            'Camera and microphone permissions are required for video calls with customers. '
            'Please enable them in your device settings to use this feature.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await openAppSettings();
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  /// Handle permission request with UI feedback
  static Future<bool> handlePermissionRequest(BuildContext context) async {
    // Check if permissions are already granted
    if (await checkEssentialPermissions()) {
      return true;
    }

    // Check if permissions are permanently denied
    if (await hasPermissionsPermanentlyDenied()) {
      await showSettingsDialog(context);
      return false;
    }

    // Show permission dialog and request
    return await showPermissionDialog(context);
  }

  /// Get detailed permission status
  static Future<Map<String, PermissionStatus>> getDetailedPermissionStatus() async {
    try {
      return {
        'camera': await Permission.camera.status,
        'microphone': await Permission.microphone.status,
        'notification': await Permission.notification.status,
      };
    } catch (e) {
      _logger.e('Error getting detailed permission status: $e');
      return {};
    }
  }
}
