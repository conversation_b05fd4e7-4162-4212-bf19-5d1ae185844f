import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:logger/logger.dart';
import 'dart:async';

enum WebSocketState {
  disconnected,
  connecting,
  connected,
  reconnecting,
  error,
}

class WebSocketService {
  static final WebSocketService _instance = WebSocketService._internal();
  factory WebSocketService() => _instance;
  WebSocketService._internal();

  final Logger _logger = Logger();
  IO.Socket? _socket;
  String? _currentUserId;
  WebSocketState _state = WebSocketState.disconnected;
  
  // Stream controllers for events
  final StreamController<WebSocketState> _stateController = StreamController<WebSocketState>.broadcast();
  final StreamController<Map<String, dynamic>> _messageController = StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<String> _errorController = StreamController<String>.broadcast();

  // Getters for streams
  Stream<WebSocketState> get stateStream => _stateController.stream;
  Stream<Map<String, dynamic>> get messageStream => _messageController.stream;
  Stream<String> get errorStream => _errorController.stream;

  /// Connect to WebSocket signaling server
  Future<void> connect({
    required String serverUrl,
    required String authToken,
    required String userId,
    Map<String, dynamic>? extraHeaders,
  }) async {
    try {
      if (_socket?.connected == true) {
        _logger.w('Vendor already connected to WebSocket server');
        return;
      }

      _currentUserId = userId;
      _updateState(WebSocketState.connecting);

      _socket = IO.io(serverUrl, 
        IO.OptionBuilder()
          .setAuth({'token': authToken})
          .setTransports(['websocket'])
          .enableAutoConnect()
          .enableReconnection()
          .setReconnectionAttempts(5)
          .setReconnectionDelay(1000)
          .setReconnectionDelayMax(5000)
          .setRandomizationFactor(0.5)
          .setTimeout(20000)
          .setExtraHeaders(extraHeaders ?? {})
          .build()
      );

      _setupEventListeners();
      _socket!.connect();
      
      _logger.i('Vendor connecting to WebSocket server: $serverUrl');
    } catch (e) {
      _logger.e('Vendor failed to connect to WebSocket server: $e');
      _updateState(WebSocketState.error);
      _errorController.add('Connection failed: $e');
      rethrow;
    }
  }

  /// Set up WebSocket event listeners
  void _setupEventListeners() {
    _socket!.on('connect', (_) {
      _logger.i('Vendor connected to signaling server');
      _updateState(WebSocketState.connected);
    });

    _socket!.on('disconnect', (reason) {
      _logger.w('Vendor disconnected from signaling server: $reason');
      _updateState(WebSocketState.disconnected);
    });

    _socket!.on('reconnect', (attemptNumber) {
      _logger.i('Vendor reconnected to signaling server (attempt: $attemptNumber)');
      _updateState(WebSocketState.connected);
    });

    _socket!.on('reconnecting', (attemptNumber) {
      _logger.i('Vendor reconnecting to signaling server (attempt: $attemptNumber)');
      _updateState(WebSocketState.reconnecting);
    });

    _socket!.on('connect_error', (error) {
      _logger.e('Vendor WebSocket connection error: $error');
      _updateState(WebSocketState.error);
      _errorController.add('Connection error: $error');
    });

    _socket!.on('error', (error) {
      _logger.e('Vendor WebSocket error: $error');
      final errorMessage = error is Map ? error['message'] ?? error.toString() : error.toString();
      _errorController.add('Socket error: $errorMessage');
    });

    // Call-related events
    _socket!.on('call-joined', (data) {
      _logger.i('Vendor joined call: ${data['call_id']}');
      _messageController.add({
        'type': 'call-joined',
        'data': data,
      });
    });

    _socket!.on('user-joined', (data) {
      _logger.i('Customer joined call: ${data['user_id']}');
      _messageController.add({
        'type': 'user-joined',
        'data': data,
      });
    });

    _socket!.on('user-left', (data) {
      _logger.i('Customer left call: ${data['user_id']}');
      _messageController.add({
        'type': 'user-left',
        'data': data,
      });
    });

    // WebRTC signaling events
    _socket!.on('webrtc-offer-received', (data) {
      _logger.i('Vendor received WebRTC offer from customer: ${data['from_user_id']}');
      _messageController.add({
        'type': 'webrtc-offer-received',
        'data': data,
      });
    });

    _socket!.on('webrtc-answer-received', (data) {
      _logger.i('Vendor received WebRTC answer from customer: ${data['from_user_id']}');
      _messageController.add({
        'type': 'webrtc-answer-received',
        'data': data,
      });
    });

    _socket!.on('ice-candidate-received', (data) {
      _logger.d('Vendor received ICE candidate from customer: ${data['from_user_id']}');
      _messageController.add({
        'type': 'ice-candidate-received',
        'data': data,
      });
    });

    _socket!.on('connection-state-update', (data) {
      _logger.d('Vendor received connection state update: ${data['state']}');
      _messageController.add({
        'type': 'connection-state-update',
        'data': data,
      });
    });
  }

  /// Join a call room
  Future<void> joinCall(String callId) async {
    if (_socket?.connected != true) {
      throw Exception('Vendor not connected to WebSocket server');
    }

    _socket!.emit('join-call', {'call_id': callId});
    _logger.i('Vendor joining call: $callId');
  }

  /// Send WebRTC offer to customer
  Future<void> sendOffer({
    required String callId,
    required String customerId,
    required RTCSessionDescription offer,
  }) async {
    if (_socket?.connected != true) {
      throw Exception('Vendor not connected to WebSocket server');
    }

    _socket!.emit('webrtc-offer', {
      'call_id': callId,
      'target_user_id': customerId,
      'offer': {
        'type': offer.type,
        'sdp': offer.sdp,
      }
    });
    
    _logger.i('Vendor sent WebRTC offer to customer: $customerId');
  }

  /// Send WebRTC answer to customer
  Future<void> sendAnswer({
    required String callId,
    required String customerId,
    required RTCSessionDescription answer,
  }) async {
    if (_socket?.connected != true) {
      throw Exception('Vendor not connected to WebSocket server');
    }

    _socket!.emit('webrtc-answer', {
      'call_id': callId,
      'target_user_id': customerId,
      'answer': {
        'type': answer.type,
        'sdp': answer.sdp,
      }
    });
    
    _logger.i('Vendor sent WebRTC answer to customer: $customerId');
  }

  /// Send ICE candidate to customer
  Future<void> sendIceCandidate({
    required String callId,
    required String customerId,
    required RTCIceCandidate candidate,
  }) async {
    if (_socket?.connected != true) {
      _logger.w('Vendor cannot send ICE candidate - not connected');
      return;
    }

    _socket!.emit('ice-candidate', {
      'call_id': callId,
      'target_user_id': customerId,
      'candidate': {
        'candidate': candidate.candidate,
        'sdpMid': candidate.sdpMid,
        'sdpMLineIndex': candidate.sdpMLineIndex,
      }
    });
    
    _logger.d('Vendor sent ICE candidate to customer: $customerId');
  }

  /// Leave current call
  Future<void> leaveCall(String callId) async {
    if (_socket?.connected == true) {
      _socket!.emit('leave-call', {'call_id': callId});
      _logger.i('Vendor left call: $callId');
    }
  }

  /// Update connection state
  Future<void> updateConnectionState({
    required String callId,
    required String state,
    String? customerId,
  }) async {
    if (_socket?.connected != true) return;

    _socket!.emit('connection-state', {
      'call_id': callId,
      'state': state,
      if (customerId != null) 'target_user_id': customerId,
    });
    
    _logger.d('Vendor updated connection state: $state');
  }

  /// Disconnect from WebSocket server
  Future<void> disconnect() async {
    try {
      _socket?.disconnect();
      _socket?.dispose();
      _socket = null;
      _currentUserId = null;
      _updateState(WebSocketState.disconnected);
      _logger.i('Vendor disconnected from WebSocket server');
    } catch (e) {
      _logger.e('Error disconnecting vendor from WebSocket: $e');
    }
  }

  /// Update WebSocket state and notify listeners
  void _updateState(WebSocketState newState) {
    if (_state != newState) {
      _state = newState;
      _stateController.add(_state);
      _logger.d('Vendor WebSocket state changed to: $newState');
    }
  }

  /// Dispose resources
  void dispose() {
    disconnect();
    _stateController.close();
    _messageController.close();
    _errorController.close();
  }

  // Getters
  bool get isConnected => _socket?.connected == true;
  String? get currentUserId => _currentUserId;
  WebSocketState get state => _state;
}
