<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         processIsolation="false"
         stopOnFailure="false"
         cacheDirectory=".phpunit.cache"
         backupGlobals="false"
         backupStaticAttributes="false">
    <testsuites>
        <testsuite name="Video Call Unit Tests">
            <directory suffix="Test.php">./tests/Unit</directory>
            <file>./tests/Unit/VideoCallModelTest.php</file>
        </testsuite>
        <testsuite name="Video Call Feature Tests">
            <directory suffix="Test.php">./tests/Feature</directory>
            <file>./tests/Feature/VideoCallApiTest.php</file>
            <file>./tests/Feature/VideoCallApiIntegrationTest.php</file>
        </testsuite>
        <testsuite name="Video Call Integration Tests">
            <file>./tests/Feature/VideoCallApiIntegrationTest.php</file>
        </testsuite>
    </testsuites>
    <coverage>
        <include>
            <directory suffix=".php">./app/Http/Controllers</directory>
            <directory suffix=".php">./app/Models</directory>
            <directory suffix=".php">./app/Services</directory>
            <directory suffix=".php">./app/Events</directory>
            <directory suffix=".php">./app/Listeners</directory>
        </include>
        <exclude>
            <directory>./app/Http/Controllers/Auth</directory>
            <file>./app/Http/Controllers/Controller.php</file>
        </exclude>
        <report>
            <html outputDirectory="coverage/html"/>
            <text outputFile="coverage/coverage.txt"/>
            <clover outputFile="coverage/clover.xml"/>
        </report>
    </coverage>
    <php>
        <server name="APP_ENV" value="testing"/>
        <server name="BCRYPT_ROUNDS" value="4"/>
        <server name="CACHE_DRIVER" value="array"/>
        <server name="DB_CONNECTION" value="sqlite"/>
        <server name="DB_DATABASE" value=":memory:"/>
        <server name="MAIL_MAILER" value="array"/>
        <server name="QUEUE_CONNECTION" value="sync"/>
        <server name="SESSION_DRIVER" value="array"/>
        <server name="TELESCOPE_ENABLED" value="false"/>
        
        <!-- Video Call Test Configuration -->
        <server name="WEBRTC_STUN_SERVERS" value="stun:stun.l.google.com:19302"/>
        <server name="SIGNALING_SERVER_URL" value="ws://localhost:3001"/>
        <server name="CALL_TIMEOUT_SECONDS" value="60"/>
        <server name="MAX_CALL_DURATION_SECONDS" value="3600"/>
    </php>
    <logging>
        <junit outputFile="coverage/junit.xml"/>
    </logging>
</phpunit>
