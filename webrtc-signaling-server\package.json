{"name": "webrtc-signaling-server", "version": "1.0.0", "description": "Dedicated WebSocket signaling server for WebRTC video calls", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "docker:build": "docker build -t webrtc-signaling-server .", "docker:run": "docker run -p 3001:3001 webrtc-signaling-server"}, "keywords": ["webrtc", "signaling", "websocket", "socket.io", "video-call", "real-time"], "author": "Your Name", "license": "MIT", "dependencies": {"socket.io": "^4.7.5", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "winston": "^3.11.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "axios": "^1.6.2"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "socket.io-client": "^4.7.5"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/webrtc-signaling-server.git"}, "bugs": {"url": "https://github.com/your-username/webrtc-signaling-server/issues"}, "homepage": "https://github.com/your-username/webrtc-signaling-server#readme"}