import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../services/call_manager.dart';
import '../services/api_service.dart';
import '../widgets/incoming_call_notification.dart';
import '../screens/video_call_screen.dart';

class VendorListScreen extends StatefulWidget {
  const VendorListScreen({Key? key}) : super(key: key);

  @override
  State<VendorListScreen> createState() => _VendorListScreenState();
}

class _VendorListScreenState extends State<VendorListScreen> {
  final Logger _logger = Logger();
  final CallManager _callManager = CallManager();
  final ApiService _apiService = ApiService();
  
  List<Map<String, dynamic>> _vendors = [];
  bool _isLoading = true;
  String? _error;
  OverlayEntry? _incomingCallOverlay;

  @override
  void initState() {
    super.initState();
    _initializeCallManager();
    _loadVendors();
  }

  @override
  void dispose() {
    _removeIncomingCallOverlay();
    super.dispose();
  }

  Future<void> _initializeCallManager() async {
    try {
      // Initialize call manager with your configuration
      await _callManager.initialize(
        signalingServerUrl: 'ws://your-signaling-server:3001',
        apiBaseUrl: 'http://your-api-server:8000/api',
        authToken: 'your-auth-token', // Get from your auth service
        userId: 'current-customer-id', // Get from your auth service
      );

      // Set up incoming call listener
      _callManager.onIncomingCall = (callId, vendorId, vendorInfo) {
        _showIncomingCallNotification(callId, vendorId, vendorInfo);
      };

      _callManager.onError = (error) {
        _showErrorSnackBar(error);
      };

    } catch (e) {
      _logger.e('Failed to initialize call manager: $e');
      _showErrorSnackBar('Failed to initialize video calling: $e');
    }
  }

  Future<void> _loadVendors() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final vendors = await _apiService.getVendors();
      
      setState(() {
        _vendors = vendors;
        _isLoading = false;
      });

    } catch (e) {
      _logger.e('Failed to load vendors: $e');
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _startVideoCall(Map<String, dynamic> vendor) async {
    try {
      _logger.i('Customer starting video call with vendor: ${vendor['id']}');
      
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Start the call
      await _callManager.startCall(vendor['id'].toString());

      // Close loading indicator
      if (mounted) {
        Navigator.of(context).pop();
        
        // Navigate to video call screen
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => VideoCallScreen(
              vendorId: vendor['id'].toString(),
              callId: _callManager.currentCallId,
              isIncomingCall: false,
              vendorInfo: vendor,
            ),
          ),
        );
      }

    } catch (e) {
      _logger.e('Failed to start video call: $e');
      
      // Close loading indicator
      if (mounted) {
        Navigator.of(context).pop();
      }
      
      _showErrorSnackBar('Failed to start video call: $e');
    }
  }

  void _showIncomingCallNotification(String callId, String vendorId, Map<String, dynamic> vendorInfo) {
    _removeIncomingCallOverlay();
    
    _incomingCallOverlay = OverlayEntry(
      builder: (context) => IncomingCallNotification(
        callId: callId,
        vendorId: vendorId,
        vendorInfo: vendorInfo,
        onDismiss: _removeIncomingCallOverlay,
      ),
    );
    
    Overlay.of(context).insert(_incomingCallOverlay!);
  }

  void _removeIncomingCallOverlay() {
    _incomingCallOverlay?.remove();
    _incomingCallOverlay = null;
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Available Vendors'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadVendors,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load vendors',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadVendors,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_vendors.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.store_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No vendors available',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadVendors,
      child: ListView.builder(
        itemCount: _vendors.length,
        itemBuilder: (context, index) {
          final vendor = _vendors[index];
          return _buildVendorCard(vendor);
        },
      ),
    );
  }

  Widget _buildVendorCard(Map<String, dynamic> vendor) {
    final isOnline = vendor['is_online'] == true;
    final isAvailable = vendor['is_available'] == true;
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListTile(
        leading: Stack(
          children: [
            CircleAvatar(
              radius: 25,
              backgroundImage: vendor['avatar'] != null
                  ? NetworkImage(vendor['avatar'])
                  : null,
              child: vendor['avatar'] == null
                  ? const Icon(Icons.store)
                  : null,
            ),
            if (isOnline)
              Positioned(
                right: 0,
                bottom: 0,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: isAvailable ? Colors.green : Colors.orange,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                ),
              ),
          ],
        ),
        title: Text(
          vendor['name'] ?? 'Unknown Vendor',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (vendor['business_name'] != null)
              Text(vendor['business_name']),
            const SizedBox(height: 4),
            Text(
              isOnline
                  ? (isAvailable ? 'Available for video calls' : 'Busy')
                  : 'Offline',
              style: TextStyle(
                color: isOnline
                    ? (isAvailable ? Colors.green : Colors.orange)
                    : Colors.grey,
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: isOnline && isAvailable
            ? IconButton(
                icon: const Icon(Icons.videocam, color: Colors.blue),
                onPressed: () => _startVideoCall(vendor),
                tooltip: 'Start Video Call',
              )
            : Icon(
                Icons.videocam_off,
                color: Colors.grey[400],
              ),
        onTap: isOnline && isAvailable
            ? () => _startVideoCall(vendor)
            : null,
      ),
    );
  }
}
