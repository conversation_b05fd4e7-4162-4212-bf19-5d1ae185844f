import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:logger/logger.dart';
import 'dart:convert';

import '../screens/video_call_screen.dart';
import '../services/call_manager.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final Logger _logger = Logger();
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  final CallManager _callManager = CallManager();
  
  BuildContext? _context;
  bool _isInitialized = false;

  /// Initialize notification service
  Future<void> initialize(BuildContext context) async {
    try {
      _context = context;

      // Android initialization settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // iOS initialization settings
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // Create notification channels
      await _createNotificationChannels();

      _isInitialized = true;
      _logger.i('Customer notification service initialized');

    } catch (e) {
      _logger.e('Failed to initialize notification service: $e');
      rethrow;
    }
  }

  /// Create notification channels for Android
  Future<void> _createNotificationChannels() async {
    // Incoming call channel
    const AndroidNotificationChannel incomingCallChannel =
        AndroidNotificationChannel(
      'incoming_calls',
      'Incoming Calls',
      description: 'Notifications for incoming video calls',
      importance: Importance.max,
      enableVibration: true,
      playSound: true,
      sound: RawResourceAndroidNotificationSound('call_ringtone'),
    );

    // Call status channel
    const AndroidNotificationChannel callStatusChannel =
        AndroidNotificationChannel(
      'call_status',
      'Call Status',
      description: 'Notifications for call status updates',
      importance: Importance.high,
      enableVibration: false,
      playSound: false,
    );

    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(incomingCallChannel);

    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(callStatusChannel);
  }

  /// Show incoming call notification
  Future<void> showIncomingCallNotification({
    required String callId,
    required String vendorId,
    required Map<String, dynamic> vendorInfo,
  }) async {
    if (!_isInitialized) {
      _logger.w('Notification service not initialized');
      return;
    }

    try {
      final vendorName = vendorInfo['name'] ?? 'Vendor';

      // Prepare notification payload
      final payload = jsonEncode({
        'type': 'incoming_call',
        'call_id': callId,
        'vendor_id': vendorId,
        'vendor_info': vendorInfo,
      });

      // Android notification details
      final AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
        'incoming_calls',
        'Incoming Calls',
        channelDescription: 'Notifications for incoming video calls',
        importance: Importance.max,
        priority: Priority.high,
        category: AndroidNotificationCategory.call,
        fullScreenIntent: true,
        autoCancel: false,
        ongoing: true,
        showWhen: true,
        when: DateTime.now().millisecondsSinceEpoch,
        largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        actions: [
          const AndroidNotificationAction(
            'decline',
            'Decline',
            icon: DrawableResourceAndroidBitmap('@drawable/ic_call_end'),
            cancelNotification: true,
          ),
          const AndroidNotificationAction(
            'accept',
            'Accept',
            icon: DrawableResourceAndroidBitmap('@drawable/ic_videocam'),
            cancelNotification: true,
          ),
        ],
      );

      // iOS notification details
      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        categoryIdentifier: 'incoming_call',
        interruptionLevel: InterruptionLevel.critical,
        sound: 'call_ringtone.aiff',
      );

      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _flutterLocalNotificationsPlugin.show(
        callId.hashCode,
        'Incoming Video Call',
        'Video call from $vendorName',
        notificationDetails,
        payload: payload,
      );

      _logger.i('Incoming call notification shown for vendor: $vendorName');

    } catch (e) {
      _logger.e('Failed to show incoming call notification: $e');
    }
  }

  /// Show call status notification
  Future<void> showCallStatusNotification({
    required String callId,
    required String title,
    required String body,
    String? vendorName,
  }) async {
    if (!_isInitialized) return;

    try {
      final payload = jsonEncode({
        'type': 'call_status',
        'call_id': callId,
      });

      const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
        'call_status',
        'Call Status',
        channelDescription: 'Notifications for call status updates',
        importance: Importance.high,
        priority: Priority.high,
        autoCancel: true,
        ongoing: false,
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        categoryIdentifier: 'call_status',
      );

      const NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _flutterLocalNotificationsPlugin.show(
        callId.hashCode + 1000, // Different ID from incoming call
        title,
        body,
        notificationDetails,
        payload: payload,
      );

    } catch (e) {
      _logger.e('Failed to show call status notification: $e');
    }
  }

  /// Cancel incoming call notification
  Future<void> cancelIncomingCallNotification(String callId) async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(callId.hashCode);
      _logger.i('Cancelled incoming call notification for call: $callId');
    } catch (e) {
      _logger.e('Failed to cancel notification: $e');
    }
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) async {
    try {
      if (response.payload == null) return;

      final payload = jsonDecode(response.payload!);
      final type = payload['type'] as String;

      switch (type) {
        case 'incoming_call':
          await _handleIncomingCallNotificationTap(payload, response.actionId);
          break;
        case 'call_status':
          await _handleCallStatusNotificationTap(payload);
          break;
      }

    } catch (e) {
      _logger.e('Failed to handle notification tap: $e');
    }
  }

  /// Handle incoming call notification tap
  Future<void> _handleIncomingCallNotificationTap(
    Map<String, dynamic> payload,
    String? actionId,
  ) async {
    final callId = payload['call_id'] as String;
    final vendorId = payload['vendor_id'] as String;
    final vendorInfo = payload['vendor_info'] as Map<String, dynamic>;

    if (actionId == 'decline') {
      // Reject the call
      await _callManager.rejectCall();
      return;
    }

    if (actionId == 'accept') {
      // Accept the call
      await _callManager.acceptCall();
    }

    // Navigate to video call screen
    if (_context != null && _context!.mounted) {
      Navigator.of(_context!).push(
        MaterialPageRoute(
          builder: (context) => VideoCallScreen(
            vendorId: vendorId,
            callId: callId,
            isIncomingCall: true,
            vendorInfo: vendorInfo,
          ),
        ),
      );
    }
  }

  /// Handle call status notification tap
  Future<void> _handleCallStatusNotificationTap(Map<String, dynamic> payload) async {
    final callId = payload['call_id'] as String;
    
    // If there's an active call, navigate to video call screen
    if (_callManager.isInCall && _callManager.currentCallId == callId) {
      if (_context != null && _context!.mounted) {
        Navigator.of(_context!).push(
          MaterialPageRoute(
            builder: (context) => VideoCallScreen(
              vendorId: _callManager.vendorId,
              callId: callId,
              isIncomingCall: false,
              vendorInfo: {}, // You might want to store this info
            ),
          ),
        );
      }
    }
  }

  /// Request notification permissions
  Future<bool> requestPermissions() async {
    try {
      final result = await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();

      return result ?? false;
    } catch (e) {
      _logger.e('Failed to request notification permissions: $e');
      return false;
    }
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    try {
      final result = await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.areNotificationsEnabled();

      return result ?? false;
    } catch (e) {
      _logger.e('Failed to check notification status: $e');
      return false;
    }
  }

  // Getters
  bool get isInitialized => _isInitialized;
}
