<component name="libraryTable">
  <library name="Gradle: org.robolectric:utils-reflector:4.3.1">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.robolectric/utils-reflector/4.3.1/2e8444b02656d03df82334c6f2b062a3da46f0bc/utils-reflector-4.3.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.robolectric/utils-reflector/4.3.1/8e0118cb94da58c20ed9ace0e4b07705262da85/utils-reflector-4.3.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.robolectric/utils-reflector/4.3.1/97827e960c26c0b9a6de8faebed9ea53663b37a8/utils-reflector-4.3.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>