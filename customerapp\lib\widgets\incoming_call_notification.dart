import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../services/call_manager.dart';
import '../screens/video_call_screen.dart';

class IncomingCallNotification extends StatefulWidget {
  final String callId;
  final String vendorId;
  final Map<String, dynamic> vendorInfo;
  final VoidCallback onDismiss;

  const IncomingCallNotification({
    Key? key,
    required this.callId,
    required this.vendorId,
    required this.vendorInfo,
    required this.onDismiss,
  }) : super(key: key);

  @override
  State<IncomingCallNotification> createState() => _IncomingCallNotificationState();
}

class _IncomingCallNotificationState extends State<IncomingCallNotification>
    with TickerProviderStateMixin {
  final Logger _logger = Logger();
  final CallManager _callManager = CallManager();
  
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _acceptCall() async {
    try {
      _logger.i('Customer accepting call: ${widget.callId}');
      
      // Accept the call
      await _callManager.acceptCall();
      
      // Navigate to video call screen
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => VideoCallScreen(
              vendorId: widget.vendorId,
              callId: widget.callId,
              isIncomingCall: true,
              vendorInfo: widget.vendorInfo,
            ),
          ),
        );
        
        widget.onDismiss();
      }
    } catch (e) {
      _logger.e('Failed to accept call: $e');
      _showErrorSnackBar('Failed to accept call: $e');
    }
  }

  Future<void> _rejectCall() async {
    try {
      _logger.i('Customer rejecting call: ${widget.callId}');
      
      // Reject the call
      await _callManager.rejectCall();
      
      widget.onDismiss();
    } catch (e) {
      _logger.e('Failed to reject call: $e');
      _showErrorSnackBar('Failed to reject call: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black54,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Opacity(
            opacity: _opacityAnimation.value,
            child: Transform.scale(
              scale: _scaleAnimation.value,
              child: Center(
                child: Container(
                  margin: const EdgeInsets.all(20),
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Vendor avatar
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.blue, width: 3),
                        ),
                        child: ClipOval(
                          child: widget.vendorInfo['avatar'] != null
                              ? Image.network(
                                  widget.vendorInfo['avatar'],
                                  fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                    return const Icon(
                                      Icons.store,
                                      size: 40,
                                      color: Colors.grey,
                                    );
                                  },
                                )
                              : const Icon(
                                  Icons.store,
                                  size: 40,
                                  color: Colors.grey,
                                ),
                        ),
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Incoming call text
                      const Text(
                        'Incoming Video Call',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // Vendor name
                      Text(
                        widget.vendorInfo['name'] ?? 'Vendor',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      
                      const SizedBox(height: 4),
                      
                      // Vendor business/category
                      if (widget.vendorInfo['business_name'] != null)
                        Text(
                          widget.vendorInfo['business_name'],
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      
                      const SizedBox(height: 32),
                      
                      // Action buttons
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          // Reject button
                          Container(
                            width: 70,
                            height: 70,
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              onPressed: _rejectCall,
                              icon: const Icon(
                                Icons.call_end,
                                color: Colors.white,
                                size: 30,
                              ),
                            ),
                          ),
                          
                          // Accept button
                          Container(
                            width: 70,
                            height: 70,
                            decoration: const BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                            ),
                            child: IconButton(
                              onPressed: _acceptCall,
                              icon: const Icon(
                                Icons.videocam,
                                color: Colors.white,
                                size: 30,
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Action labels
                      const Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Text(
                            'Decline',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                          Text(
                            'Accept',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
