# Video Call API Documentation

This document describes the API endpoints for managing video calls between customers and vendors.

## Authentication

All endpoints require authentication using Laravel Sanctum. Include the bearer token in the Authorization header:

```
Authorization: Bearer {your-token}
```

## Base URL

```
/api/calls
```

## Endpoints

### 1. Initiate Call

**POST** `/api/calls/initiate`

Initiates a new video call between the authenticated user (caller) and another user (receiver).

#### Request Body

```json
{
    "receiver_id": 123,
    "call_type": "video",
    "webrtc_data": {
        "offer": "...",
        "ice_candidates": []
    },
    "room_id": "room_abc123"
}
```

#### Parameters

- `receiver_id` (required, integer): ID of the user to call
- `call_type` (optional, string): "audio" or "video" (default: "video")
- `webrtc_data` (optional, object): WebRTC signaling data
- `room_id` (optional, string): WebRTC room identifier

#### Response

```json
{
    "success": true,
    "message": "Call initiated successfully.",
    "data": {
        "call_id": "call_550e8400-e29b-41d4-a716-446655440000",
        "status": "pending",
        "caller": {
            "id": 1,
            "name": "John Doe",
            "photo": "https://example.com/photo.jpg"
        },
        "receiver": {
            "id": 123,
            "name": "Jane Smith",
            "photo": "https://example.com/photo2.jpg"
        },
        "call_type": "video",
        "room_id": "room_abc123",
        "initiated_at": "2024-01-15T10:30:00Z",
        "webrtc_data": {...}
    }
}
```

### 2. Accept Call

**POST** `/api/calls/{call_id}/accept`

Accepts a pending video call. Only the receiver can accept the call.

#### Request Body

```json
{
    "webrtc_data": {
        "answer": "...",
        "ice_candidates": []
    }
}
```

#### Parameters

- `webrtc_data` (optional, object): WebRTC signaling data for the answer

#### Response

```json
{
    "success": true,
    "message": "Call accepted successfully.",
    "data": {
        "call_id": "call_550e8400-e29b-41d4-a716-446655440000",
        "status": "accepted",
        "accepted_at": "2024-01-15T10:30:30Z",
        "webrtc_data": {...}
    }
}
```

### 3. Reject Call

**POST** `/api/calls/{call_id}/reject`

Rejects a pending video call. Only the receiver can reject the call.

#### Request Body

```json
{
    "reason": "Busy with another call"
}
```

#### Parameters

- `reason` (optional, string): Reason for rejecting the call (max 500 characters)

#### Response

```json
{
    "success": true,
    "message": "Call rejected successfully.",
    "data": {
        "call_id": "call_550e8400-e29b-41d4-a716-446655440000",
        "status": "rejected",
        "rejection_reason": "Busy with another call",
        "ended_at": "2024-01-15T10:30:45Z"
    }
}
```

### 4. End Call

**POST** `/api/calls/{call_id}/end`

Ends an active video call. Both caller and receiver can end the call.

#### Request Body

```json
{
    "reason": "Call completed",
    "call_quality_metrics": {
        "bandwidth": 1024,
        "latency": 50,
        "packet_loss": 0.5,
        "audio_quality": 5,
        "video_quality": 4
    }
}
```

#### Parameters

- `reason` (optional, string): Reason for ending the call (max 500 characters)
- `call_quality_metrics` (optional, object): Call quality metrics
  - `bandwidth` (optional, number): Bandwidth in kbps
  - `latency` (optional, number): Latency in milliseconds
  - `packet_loss` (optional, number): Packet loss percentage (0-100)
  - `audio_quality` (optional, integer): Audio quality rating (1-5)
  - `video_quality` (optional, integer): Video quality rating (1-5)

#### Response

```json
{
    "success": true,
    "message": "Call ended successfully.",
    "data": {
        "call_id": "call_550e8400-e29b-41d4-a716-446655440000",
        "status": "ended",
        "duration": 180,
        "formatted_duration": "03:00",
        "ended_at": "2024-01-15T10:33:30Z",
        "end_reason": "Call completed",
        "call_quality_metrics": {...}
    }
}
```

### 5. Cancel Call

**POST** `/api/calls/{call_id}/cancel`

Cancels a pending video call. Only the caller can cancel the call.

#### Request Body

```json
{
    "reason": "Changed my mind"
}
```

#### Parameters

- `reason` (optional, string): Reason for cancelling the call (max 500 characters)

#### Response

```json
{
    "success": true,
    "message": "Call cancelled successfully.",
    "data": {
        "call_id": "call_550e8400-e29b-41d4-a716-446655440000",
        "status": "cancelled",
        "end_reason": "Changed my mind",
        "ended_at": "2024-01-15T10:30:15Z"
    }
}
```

### 6. Get Call Status

**GET** `/api/calls/{call_id}/status`

Retrieves the current status and details of a video call.

#### Response

```json
{
    "success": true,
    "data": {
        "call_id": "call_550e8400-e29b-41d4-a716-446655440000",
        "status": "accepted",
        "call_type": "video",
        "caller": {
            "id": 1,
            "name": "John Doe",
            "photo": "https://example.com/photo.jpg"
        },
        "receiver": {
            "id": 123,
            "name": "Jane Smith",
            "photo": "https://example.com/photo2.jpg"
        },
        "initiated_at": "2024-01-15T10:30:00Z",
        "accepted_at": "2024-01-15T10:30:30Z",
        "ended_at": null,
        "duration": null,
        "formatted_duration": null,
        "room_id": "room_abc123",
        "webrtc_data": {...},
        "rejection_reason": null,
        "end_reason": null,
        "call_quality_metrics": null,
        "is_active": true,
        "can_accept": false,
        "can_reject": false,
        "can_end": true
    }
}
```

### 7. Get Call History

**GET** `/api/calls/history`

Retrieves the call history for the authenticated user.

#### Query Parameters

- `per_page` (optional, integer): Number of calls per page (default: 15)
- `status` (optional, string): Filter by call status
- `call_type` (optional, string): Filter by call type ("audio" or "video")

#### Response

```json
{
    "success": true,
    "data": [
        {
            "call_id": "call_550e8400-e29b-41d4-a716-446655440000",
            "status": "ended",
            "call_type": "video",
            "other_participant": {
                "id": 123,
                "name": "Jane Smith",
                "photo": "https://example.com/photo2.jpg"
            },
            "is_caller": true,
            "initiated_at": "2024-01-15T10:30:00Z",
            "accepted_at": "2024-01-15T10:30:30Z",
            "ended_at": "2024-01-15T10:33:30Z",
            "duration": 180,
            "formatted_duration": "03:00"
        }
    ],
    "pagination": {
        "current_page": 1,
        "last_page": 1,
        "per_page": 15,
        "total": 1,
        "from": 1,
        "to": 1
    }
}
```

### 8. Get Active Calls

**GET** `/api/calls/active`

Retrieves all active calls for the authenticated user.

#### Response

```json
{
    "success": true,
    "data": [
        {
            "call_id": "call_550e8400-e29b-41d4-a716-446655440000",
            "status": "accepted",
            "call_type": "video",
            "other_participant": {
                "id": 123,
                "name": "Jane Smith",
                "photo": "https://example.com/photo2.jpg"
            },
            "is_caller": true,
            "room_id": "room_abc123",
            "webrtc_data": {...},
            "initiated_at": "2024-01-15T10:30:00Z",
            "accepted_at": "2024-01-15T10:30:30Z",
            "can_accept": false,
            "can_reject": false,
            "can_end": true
        }
    ]
}
```

## Call Status Values

- `pending`: Call has been initiated but not yet accepted
- `accepted`: Call has been accepted and is in progress
- `rejected`: Call was rejected by the receiver
- `ended`: Call was ended by either participant
- `missed`: Call was not answered (auto-status)
- `cancelled`: Call was cancelled by the caller

## Real-time Events

The API broadcasts real-time events using Laravel Broadcasting:

### Call Initiated Event

**Channel**: `user.{receiver_id}`
**Event**: `call.initiated`

Broadcasted when a new call is initiated.

### Call Status Changed Event

**Channel**: `user.{caller_id}` and `user.{receiver_id}`
**Event**: `call.status.changed`

Broadcasted when call status changes (accepted, rejected, ended, etc.).

## Error Responses

All endpoints return consistent error responses:

```json
{
    "success": false,
    "message": "Error description"
}
```

Common HTTP status codes:
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (authentication required)
- `403`: Forbidden (authorization failed)
- `404`: Not Found (call not found)
- `422`: Unprocessable Entity (validation failed)
- `500`: Internal Server Error

## Authorization Rules

- Users can only initiate calls if they are active
- Only the receiver can accept or reject a call
- Both caller and receiver can end an active call
- Only the caller can cancel a pending call
- Users can only view calls they are participants in
- Users cannot call themselves
- Only one active call is allowed between two users at a time
