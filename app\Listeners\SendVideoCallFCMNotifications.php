<?php

namespace App\Listeners;

use App\Events\CallInitiated;
use App\Events\CallStatusChanged;
use App\Models\VideoCall;
use App\Services\VideoCallNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Exception;

class SendVideoCallFCMNotifications implements ShouldQueue
{
    use InteractsWithQueue;

    protected $notificationService;

    /**
     * Create the event listener.
     */
    public function __construct(VideoCallNotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Handle call initiated events - Send FCM to receiver
     */
    public function handleCallInitiated(CallInitiated $event)
    {
        try {
            $videoCall = $event->videoCall;
            
            Log::info('Processing FCM notification for call initiation', [
                'call_id' => $videoCall->call_id,
                'caller_id' => $videoCall->caller_id,
                'receiver_id' => $videoCall->receiver_id
            ]);

            // Send incoming call notification to receiver
            $success = $this->notificationService->sendIncomingCallNotification($videoCall);

            if ($success) {
                Log::info('FCM notification sent successfully for call initiation', [
                    'call_id' => $videoCall->call_id
                ]);
            } else {
                Log::warning('FCM notification failed for call initiation', [
                    'call_id' => $videoCall->call_id
                ]);
            }

        } catch (Exception $e) {
            Log::error('Error sending FCM notification for call initiation', [
                'call_id' => $event->videoCall->call_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Don't fail the job, just log the error
            // The call should still proceed even if notification fails
        }
    }

    /**
     * Handle call status changed events - Send FCM based on status
     */
    public function handleCallStatusChanged(CallStatusChanged $event)
    {
        try {
            $videoCall = $event->videoCall;
            $previousStatus = $event->previousStatus;

            Log::info('Processing FCM notification for status change', [
                'call_id' => $videoCall->call_id,
                'previous_status' => $previousStatus,
                'new_status' => $videoCall->status
            ]);

            // Send appropriate notification based on status
            switch ($videoCall->status) {
                case VideoCall::STATUS_ACCEPTED:
                case VideoCall::STATUS_REJECTED:
                case VideoCall::STATUS_MISSED:
                    $success = $this->notificationService->sendCallStatusNotification($videoCall, $previousStatus);
                    break;

                case VideoCall::STATUS_ENDED:
                    $success = $this->notificationService->sendCallEndedNotification($videoCall);
                    break;

                default:
                    // No notification needed for other statuses
                    $success = true;
                    break;
            }

            if ($success) {
                Log::info('FCM notification sent successfully for status change', [
                    'call_id' => $videoCall->call_id,
                    'status' => $videoCall->status
                ]);
            } else {
                Log::warning('FCM notification failed for status change', [
                    'call_id' => $videoCall->call_id,
                    'status' => $videoCall->status
                ]);
            }

        } catch (Exception $e) {
            Log::error('Error sending FCM notification for status change', [
                'call_id' => $event->videoCall->call_id,
                'status' => $event->videoCall->status,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed($event, $exception)
    {
        Log::error('FCM notification job failed', [
            'event' => get_class($event),
            'call_id' => $event->videoCall->call_id ?? 'unknown',
            'error' => $exception->getMessage()
        ]);
    }

    /**
     * Register the listeners for the subscriber.
     */
    public function subscribe($events)
    {
        $events->listen(
            CallInitiated::class,
            [SendVideoCallFCMNotifications::class, 'handleCallInitiated']
        );

        $events->listen(
            CallStatusChanged::class,
            [SendVideoCallFCMNotifications::class, 'handleCallStatusChanged']
        );
    }
}
