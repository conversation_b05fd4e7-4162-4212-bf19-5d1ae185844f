import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

/// Test configuration and utilities for video call tests
class TestConfig {
  static const String testApiBaseUrl = 'http://localhost:8000/api';
  static const String testSignalingUrl = 'ws://localhost:3001';
  static const String testAuthToken = 'test-auth-token-123';
  static const String testUserId = 'test-user-123';
  static const String testVendorId = 'test-vendor-456';
  static const String testCallId = 'test-call-789';

  /// Setup method channel mocks for WebRTC
  static void setupMethodChannelMocks() {
    TestWidgetsFlutterBinding.ensureInitialized();
    
    // Mock WebRTC method channels
    const MethodChannel('FlutterWebRTC.Method')
        .setMockMethodCallHandler((MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'createPeerConnection':
          return {
            'peerConnectionId': 'test-peer-connection-id',
          };
        case 'getUserMedia':
          return {
            'streamId': 'test-stream-id',
            'audioTracks': [],
            'videoTracks': [],
          };
        case 'createOffer':
          return {
            'sdp': 'test-offer-sdp',
            'type': 'offer',
          };
        case 'createAnswer':
          return {
            'sdp': 'test-answer-sdp',
            'type': 'answer',
          };
        case 'setLocalDescription':
        case 'setRemoteDescription':
        case 'addCandidate':
          return null;
        default:
          return null;
      }
    });

    // Mock permission handler
    const MethodChannel('flutter.baseflow.com/permissions/methods')
        .setMockMethodCallHandler((MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'checkPermissionStatus':
          return 1; // PermissionStatus.granted
        case 'requestPermissions':
          return {7: 1, 5: 1}; // Camera and microphone granted
        default:
          return null;
      }
    });

    // Mock local notifications
    const MethodChannel('dexterous.com/flutter/local_notifications')
        .setMockMethodCallHandler((MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'initialize':
        case 'show':
        case 'cancel':
          return null;
        default:
          return null;
      }
    });

    // Mock wakelock
    const MethodChannel('wakelock_plus')
        .setMockMethodCallHandler((MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'enable':
        case 'disable':
          return null;
        default:
          return null;
      }
    });
  }

  /// Create mock call data
  static Map<String, dynamic> createMockCallData({
    String? callId,
    String? callerId,
    String? receiverId,
    String status = 'initiated',
    String callType = 'video',
  }) {
    return {
      'call_id': callId ?? testCallId,
      'caller_id': callerId ?? testUserId,
      'receiver_id': receiverId ?? testVendorId,
      'status': status,
      'call_type': callType,
      'created_at': DateTime.now().toIso8601String(),
      'metadata': {
        'test': true,
      },
    };
  }

  /// Create mock user data
  static Map<String, dynamic> createMockUserData({
    String? userId,
    String? name,
    String userType = 'customer',
    bool isAvailable = true,
  }) {
    return {
      'id': userId ?? testUserId,
      'name': name ?? 'Test User',
      'user_type': userType,
      'is_available': isAvailable,
      'is_online': true,
      'avatar': 'https://example.com/avatar.jpg',
      'email': '<EMAIL>',
    };
  }

  /// Create mock WebRTC offer
  static Map<String, dynamic> createMockOffer({
    String? sdp,
    String type = 'offer',
  }) {
    return {
      'sdp': sdp ?? 'v=0\r\no=- 123456789 2 IN IP4 127.0.0.1\r\n...',
      'type': type,
    };
  }

  /// Create mock WebRTC answer
  static Map<String, dynamic> createMockAnswer({
    String? sdp,
    String type = 'answer',
  }) {
    return {
      'sdp': sdp ?? 'v=0\r\no=- 987654321 2 IN IP4 127.0.0.1\r\n...',
      'type': type,
    };
  }

  /// Create mock ICE candidate
  static Map<String, dynamic> createMockIceCandidate({
    String? candidate,
    String? sdpMid,
    int? sdpMLineIndex,
  }) {
    return {
      'candidate': candidate ?? 'candidate:1 1 UDP 2130706431 ************* 54400 typ host',
      'sdpMid': sdpMid ?? '0',
      'sdpMLineIndex': sdpMLineIndex ?? 0,
    };
  }

  /// Create mock WebSocket message
  static Map<String, dynamic> createMockWebSocketMessage({
    required String type,
    required Map<String, dynamic> data,
  }) {
    return {
      'type': type,
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// Create mock incoming call message
  static Map<String, dynamic> createMockIncomingCallMessage({
    String? callId,
    String? fromUserId,
    Map<String, dynamic>? userInfo,
  }) {
    return createMockWebSocketMessage(
      type: 'incoming-call',
      data: {
        'call_id': callId ?? testCallId,
        'from_user_id': fromUserId ?? testVendorId,
        'vendor_info': userInfo ?? createMockUserData(userType: 'vendor'),
      },
    );
  }

  /// Create mock WebRTC offer message
  static Map<String, dynamic> createMockOfferMessage({
    String? callId,
    String? fromUserId,
    Map<String, dynamic>? offer,
  }) {
    return createMockWebSocketMessage(
      type: 'webrtc-offer-received',
      data: {
        'call_id': callId ?? testCallId,
        'from_user_id': fromUserId ?? testVendorId,
        'offer': offer ?? createMockOffer(),
      },
    );
  }

  /// Create mock WebRTC answer message
  static Map<String, dynamic> createMockAnswerMessage({
    String? callId,
    String? fromUserId,
    Map<String, dynamic>? answer,
  }) {
    return createMockWebSocketMessage(
      type: 'webrtc-answer-received',
      data: {
        'call_id': callId ?? testCallId,
        'from_user_id': fromUserId ?? testVendorId,
        'answer': answer ?? createMockAnswer(),
      },
    );
  }

  /// Create mock ICE candidate message
  static Map<String, dynamic> createMockIceCandidateMessage({
    String? callId,
    String? fromUserId,
    Map<String, dynamic>? candidate,
  }) {
    return createMockWebSocketMessage(
      type: 'ice-candidate-received',
      data: {
        'call_id': callId ?? testCallId,
        'from_user_id': fromUserId ?? testVendorId,
        'candidate': candidate ?? createMockIceCandidate(),
      },
    );
  }

  /// Create mock user left message
  static Map<String, dynamic> createMockUserLeftMessage({
    String? userId,
  }) {
    return createMockWebSocketMessage(
      type: 'user-left',
      data: {
        'user_id': userId ?? testVendorId,
      },
    );
  }

  /// Verify that a mock was called with specific parameters
  static void verifyMockCall(
    Mock mock,
    String method, {
    List<dynamic>? positionalArguments,
    Map<Symbol, dynamic>? namedArguments,
  }) {
    if (namedArguments != null) {
      verify(mock.noSuchMethod(
        Invocation.method(Symbol(method), positionalArguments ?? [], namedArguments),
      )).called(1);
    } else {
      verify(mock.noSuchMethod(
        Invocation.method(Symbol(method), positionalArguments ?? []),
      )).called(1);
    }
  }

  /// Wait for async operations to complete
  static Future<void> waitForAsync([int milliseconds = 10]) async {
    await Future.delayed(Duration(milliseconds: milliseconds));
  }

  /// Pump widget with material app wrapper
  static Future<void> pumpMaterialWidget(
    WidgetTester tester,
    Widget widget, {
    Duration? duration,
  }) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: widget,
        ),
      ),
    );
    
    if (duration != null) {
      await tester.pump(duration);
    }
  }
}
