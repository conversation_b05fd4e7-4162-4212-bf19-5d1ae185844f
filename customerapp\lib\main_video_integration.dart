import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';

import 'services/call_manager.dart';
import 'services/notification_service.dart';
import 'services/permission_service.dart';
import 'screens/home_screen.dart';
import 'screens/chat_screen.dart';
import 'screens/video_call_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize logger
  final logger = Logger();
  
  try {
    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    
    // Set system UI overlay style
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
    
    logger.i('Customer app initialized successfully');
    runApp(const CustomerApp());
    
  } catch (e, stackTrace) {
    logger.e('Failed to initialize customer app', error: e, stackTrace: stackTrace);
    runApp(ErrorApp(error: e.toString()));
  }
}

class CustomerApp extends StatelessWidget {
  const CustomerApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Customer App',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        fontFamily: 'Roboto',
        appBarTheme: const AppBarTheme(
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
          systemOverlayStyle: SystemUiOverlayStyle.dark,
        ),
      ),
      home: const AppWrapper(),
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/chat':
            final args = settings.arguments as Map<String, dynamic>;
            return MaterialPageRoute(
              builder: (context) => ChatScreen(
                vendorId: args['vendor_id'],
                vendorInfo: args['vendor_info'],
                chatId: args['chat_id'],
              ),
            );
          case '/video_call':
            final args = settings.arguments as Map<String, dynamic>;
            return MaterialPageRoute(
              builder: (context) => VideoCallScreen(
                vendorId: args['vendor_id'],
                callId: args['call_id'],
                isIncomingCall: args['is_incoming_call'] ?? false,
                vendorInfo: args['vendor_info'],
                chatId: args['chat_id'],
              ),
            );
          default:
            return MaterialPageRoute(
              builder: (context) => const HomeScreen(),
            );
        }
      },
    );
  }
}

class AppWrapper extends StatefulWidget {
  const AppWrapper({Key? key}) : super(key: key);

  @override
  State<AppWrapper> createState() => _AppWrapperState();
}

class _AppWrapperState extends State<AppWrapper> with WidgetsBindingObserver {
  final Logger _logger = Logger();
  final CallManager _callManager = CallManager();
  final NotificationService _notificationService = NotificationService();
  
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeApp();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        _logger.i('Customer app resumed');
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
        _logger.i('Customer app paused');
        _handleAppPaused();
        break;
      case AppLifecycleState.detached:
        _logger.i('Customer app detached');
        _handleAppDetached();
        break;
      default:
        break;
    }
  }

  Future<void> _initializeApp() async {
    try {
      // Request permissions
      await PermissionService.requestAllPermissions();
      
      // Initialize notification service
      await _notificationService.initialize(context);
      
      // Initialize call manager
      await _callManager.initialize(
        signalingServerUrl: 'ws://your-signaling-server:3001',
        apiBaseUrl: 'http://your-api-server:8000/api',
        authToken: 'your-auth-token', // Get from your auth service
        userId: 'current-customer-id', // Get from your auth service
      );

      // Set up call manager callbacks
      _callManager.onIncomingCall = (callId, vendorId, vendorInfo) {
        _handleIncomingCall(callId, vendorId, vendorInfo);
      };

      _callManager.onCallStateChanged = (state) {
        _handleCallStateChanged(state);
      };

      _callManager.onError = (error) {
        _showErrorSnackBar(error);
      };

      setState(() {
        _isInitialized = true;
      });
      
      _logger.i('Customer app fully initialized');
      
    } catch (e) {
      _logger.e('Failed to initialize customer app: $e');
      _showErrorSnackBar('Failed to initialize app: $e');
    }
  }

  void _handleIncomingCall(String callId, String vendorId, Map<String, dynamic> vendorInfo) {
    // Show push notification
    _notificationService.showIncomingCallNotification(
      callId: callId,
      vendorId: vendorId,
      vendorInfo: vendorInfo,
    );
  }

  void _handleCallStateChanged(CallState state) {
    switch (state) {
      case CallState.connected:
        _notificationService.showCallStatusNotification(
          callId: _callManager.currentCallId ?? '',
          title: 'Video Call Active',
          body: 'Tap to return to call',
        );
        break;
      case CallState.ended:
        _notificationService.cancelIncomingCallNotification(
          _callManager.currentCallId ?? '',
        );
        break;
      default:
        break;
    }
  }

  void _handleAppResumed() {
    // Handle app resume logic
  }

  void _handleAppPaused() {
    // Handle app pause logic
  }

  void _handleAppDetached() {
    // Clean up resources
    _callManager.dispose();
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Initializing app...'),
            ],
          ),
        ),
      );
    }

    return const HomeScreen();
  }
}

class ErrorApp extends StatelessWidget {
  final String error;
  
  const ErrorApp({Key? key, required this.error}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Error',
      home: Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Failed to start app',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  error,
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    // Restart app
                    main();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
