<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\VideoCall;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class VideoCallApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $caller;
    protected $receiver;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->caller = User::factory()->create(['is_active' => true]);
        $this->receiver = User::factory()->create(['is_active' => true]);
    }

    public function test_user_can_initiate_call()
    {
        Sanctum::actingAs($this->caller);

        $response = $this->postJson('/api/calls/initiate', [
            'receiver_id' => $this->receiver->id,
            'call_type' => 'video',
            'room_id' => 'test_room_123'
        ]);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'call_id',
                        'status',
                        'caller',
                        'receiver',
                        'call_type',
                        'room_id',
                        'initiated_at'
                    ]
                ]);

        $this->assertDatabaseHas('video_calls', [
            'caller_id' => $this->caller->id,
            'receiver_id' => $this->receiver->id,
            'status' => VideoCall::STATUS_PENDING,
            'call_type' => VideoCall::TYPE_VIDEO
        ]);
    }

    public function test_user_cannot_call_themselves()
    {
        Sanctum::actingAs($this->caller);

        $response = $this->postJson('/api/calls/initiate', [
            'receiver_id' => $this->caller->id,
            'call_type' => 'video'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['receiver_id']);
    }

    public function test_receiver_can_accept_call()
    {
        $call = VideoCall::factory()->create([
            'caller_id' => $this->caller->id,
            'receiver_id' => $this->receiver->id,
            'status' => VideoCall::STATUS_PENDING
        ]);

        Sanctum::actingAs($this->receiver);

        $response = $this->postJson("/api/calls/{$call->call_id}/accept");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'status' => VideoCall::STATUS_ACCEPTED
                    ]
                ]);

        $this->assertDatabaseHas('video_calls', [
            'id' => $call->id,
            'status' => VideoCall::STATUS_ACCEPTED
        ]);
    }

    public function test_caller_cannot_accept_call()
    {
        $call = VideoCall::factory()->create([
            'caller_id' => $this->caller->id,
            'receiver_id' => $this->receiver->id,
            'status' => VideoCall::STATUS_PENDING
        ]);

        Sanctum::actingAs($this->caller);

        $response = $this->postJson("/api/calls/{$call->call_id}/accept");

        $response->assertStatus(403);
    }

    public function test_receiver_can_reject_call()
    {
        $call = VideoCall::factory()->create([
            'caller_id' => $this->caller->id,
            'receiver_id' => $this->receiver->id,
            'status' => VideoCall::STATUS_PENDING
        ]);

        Sanctum::actingAs($this->receiver);

        $response = $this->postJson("/api/calls/{$call->call_id}/reject", [
            'reason' => 'Busy with another call'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'status' => VideoCall::STATUS_REJECTED,
                        'rejection_reason' => 'Busy with another call'
                    ]
                ]);
    }

    public function test_both_participants_can_end_call()
    {
        $call = VideoCall::factory()->create([
            'caller_id' => $this->caller->id,
            'receiver_id' => $this->receiver->id,
            'status' => VideoCall::STATUS_ACCEPTED,
            'accepted_at' => now()->subMinutes(5)
        ]);

        Sanctum::actingAs($this->caller);

        $response = $this->postJson("/api/calls/{$call->call_id}/end", [
            'reason' => 'Call completed'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'status' => VideoCall::STATUS_ENDED,
                        'end_reason' => 'Call completed'
                    ]
                ]);
    }

    public function test_user_can_get_call_status()
    {
        $call = VideoCall::factory()->create([
            'caller_id' => $this->caller->id,
            'receiver_id' => $this->receiver->id,
            'status' => VideoCall::STATUS_ACCEPTED
        ]);

        Sanctum::actingAs($this->caller);

        $response = $this->getJson("/api/calls/{$call->call_id}/status");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'call_id',
                        'status',
                        'call_type',
                        'caller',
                        'receiver',
                        'is_active',
                        'can_accept',
                        'can_reject',
                        'can_end'
                    ]
                ]);
    }

    public function test_user_can_get_call_history()
    {
        // Create some test calls
        VideoCall::factory()->count(3)->create([
            'caller_id' => $this->caller->id,
            'receiver_id' => $this->receiver->id,
            'status' => VideoCall::STATUS_ENDED
        ]);

        Sanctum::actingAs($this->caller);

        $response = $this->getJson('/api/calls/history');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'call_id',
                            'status',
                            'call_type',
                            'other_participant',
                            'is_caller'
                        ]
                    ],
                    'pagination'
                ]);
    }

    public function test_user_can_get_active_calls()
    {
        // Create an active call
        VideoCall::factory()->create([
            'caller_id' => $this->caller->id,
            'receiver_id' => $this->receiver->id,
            'status' => VideoCall::STATUS_ACCEPTED
        ]);

        Sanctum::actingAs($this->caller);

        $response = $this->getJson('/api/calls/active');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'call_id',
                            'status',
                            'other_participant',
                            'can_end'
                        ]
                    ]
                ]);
    }

    public function test_unauthorized_user_cannot_access_endpoints()
    {
        $response = $this->postJson('/api/calls/initiate', [
            'receiver_id' => $this->receiver->id
        ]);

        $response->assertStatus(401);
    }

    public function test_user_cannot_view_other_users_calls()
    {
        $otherUser = User::factory()->create();
        
        $call = VideoCall::factory()->create([
            'caller_id' => $this->caller->id,
            'receiver_id' => $this->receiver->id
        ]);

        Sanctum::actingAs($otherUser);

        $response = $this->getJson("/api/calls/{$call->call_id}/status");

        $response->assertStatus(403);
    }
}
