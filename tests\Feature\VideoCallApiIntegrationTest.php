<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\VideoCall;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use App\Events\VideoCallInitiated;
use App\Events\VideoCallAccepted;
use App\Events\VideoCallEnded;

class VideoCallApiIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $customer;
    protected $vendor;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->customer = User::factory()->create([
            'user_type' => 'customer',
            'email_verified_at' => now(),
        ]);
        
        $this->vendor = User::factory()->create([
            'user_type' => 'vendor',
            'email_verified_at' => now(),
            'is_available' => true,
        ]);

        Event::fake();
        Queue::fake();
    }

    /** @test */
    public function complete_video_call_flow_works_correctly()
    {
        // Step 1: Customer initiates call
        Sanctum::actingAs($this->customer);
        
        $initiateResponse = $this->postJson('/api/calls/initiate', [
            'receiver_id' => $this->vendor->id,
            'call_type' => 'video',
        ]);

        $initiateResponse->assertStatus(201);
        $callId = $initiateResponse->json('data.call_id');

        // Verify call initiated event was fired
        Event::assertDispatched(VideoCallInitiated::class);

        // Step 2: Vendor accepts call
        Sanctum::actingAs($this->vendor);
        
        $acceptResponse = $this->postJson("/api/calls/{$callId}/accept");
        $acceptResponse->assertStatus(200);

        // Verify call accepted event was fired
        Event::assertDispatched(VideoCallAccepted::class);

        // Step 3: Verify call is in accepted state
        $call = VideoCall::where('call_id', $callId)->first();
        $this->assertEquals('accepted', $call->status);
        $this->assertNotNull($call->accepted_at);

        // Step 4: Simulate call connection
        $call->update([
            'status' => 'connected',
            'connected_at' => now(),
        ]);

        // Step 5: Customer ends call after some time
        Sanctum::actingAs($this->customer);
        
        // Simulate 5 minutes call duration
        $call->update(['connected_at' => now()->subMinutes(5)]);
        
        $endResponse = $this->postJson("/api/calls/{$callId}/end");
        $endResponse->assertStatus(200);

        // Verify call ended event was fired
        Event::assertDispatched(VideoCallEnded::class);

        // Step 6: Verify final call state
        $call->refresh();
        $this->assertEquals('ended', $call->status);
        $this->assertNotNull($call->ended_at);
        $this->assertEquals(300, $call->duration); // 5 minutes = 300 seconds
    }

    /** @test */
    public function concurrent_calls_are_handled_correctly()
    {
        $vendor2 = User::factory()->create([
            'user_type' => 'vendor',
            'is_available' => true,
        ]);

        Sanctum::actingAs($this->customer);

        // Customer initiates call with first vendor
        $response1 = $this->postJson('/api/calls/initiate', [
            'receiver_id' => $this->vendor->id,
            'call_type' => 'video',
        ]);
        $response1->assertStatus(201);

        // Customer tries to initiate another call while first is active
        $response2 = $this->postJson('/api/calls/initiate', [
            'receiver_id' => $vendor2->id,
            'call_type' => 'video',
        ]);

        // Should fail because customer already has an active call
        $response2->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'You already have an active call',
            ]);
    }

    /** @test */
    public function call_timeout_is_handled_correctly()
    {
        Sanctum::actingAs($this->customer);

        $response = $this->postJson('/api/calls/initiate', [
            'receiver_id' => $this->vendor->id,
            'call_type' => 'video',
        ]);

        $callId = $response->json('data.call_id');
        $call = VideoCall::where('call_id', $callId)->first();

        // Simulate call timeout (no answer after 60 seconds)
        $call->update([
            'created_at' => now()->subSeconds(65),
            'status' => 'missed',
            'ended_at' => now(),
        ]);

        $this->assertDatabaseHas('video_calls', [
            'call_id' => $callId,
            'status' => 'missed',
        ]);

        $this->assertEquals(0, $call->fresh()->duration);
    }

    /** @test */
    public function call_statistics_are_calculated_correctly()
    {
        // Create various call scenarios
        $completedCall = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'status' => 'ended',
            'connected_at' => now()->subMinutes(10),
            'ended_at' => now()->subMinutes(5),
        ]);

        $missedCall = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'status' => 'missed',
            'created_at' => now()->subHour(),
            'ended_at' => now()->subHour(),
        ]);

        $rejectedCall = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'status' => 'rejected',
            'created_at' => now()->subHours(2),
            'ended_at' => now()->subHours(2),
        ]);

        Sanctum::actingAs($this->customer);

        $response = $this->getJson('/api/calls/statistics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'total_calls',
                    'completed_calls',
                    'missed_calls',
                    'rejected_calls',
                    'total_duration',
                    'average_duration',
                ]
            ]);

        $stats = $response->json('data');
        $this->assertEquals(3, $stats['total_calls']);
        $this->assertEquals(1, $stats['completed_calls']);
        $this->assertEquals(1, $stats['missed_calls']);
        $this->assertEquals(1, $stats['rejected_calls']);
    }

    /** @test */
    public function vendor_availability_affects_call_initiation()
    {
        // Set vendor as unavailable
        $this->vendor->update(['is_available' => false]);

        Sanctum::actingAs($this->customer);

        $response = $this->postJson('/api/calls/initiate', [
            'receiver_id' => $this->vendor->id,
            'call_type' => 'video',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Vendor is not available for calls',
            ]);

        // Set vendor as available
        $this->vendor->update(['is_available' => true]);

        $response = $this->postJson('/api/calls/initiate', [
            'receiver_id' => $this->vendor->id,
            'call_type' => 'video',
        ]);

        $response->assertStatus(201);
    }

    /** @test */
    public function call_permissions_are_enforced_correctly()
    {
        $call = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'status' => 'initiated',
        ]);

        $unauthorizedUser = User::factory()->create(['user_type' => 'customer']);

        // Unauthorized user tries to accept call
        Sanctum::actingAs($unauthorizedUser);

        $response = $this->postJson("/api/calls/{$call->call_id}/accept");

        $response->assertStatus(403)
            ->assertJson([
                'success' => false,
                'message' => 'Unauthorized to perform this action',
            ]);

        // Unauthorized user tries to end call
        $response = $this->postJson("/api/calls/{$call->call_id}/end");

        $response->assertStatus(403);

        // Authorized user can perform actions
        Sanctum::actingAs($this->vendor);

        $response = $this->postJson("/api/calls/{$call->call_id}/accept");
        $response->assertStatus(200);
    }

    /** @test */
    public function call_data_persistence_works_correctly()
    {
        Sanctum::actingAs($this->customer);

        // Create call with metadata
        $response = $this->postJson('/api/calls/initiate', [
            'receiver_id' => $this->vendor->id,
            'call_type' => 'video',
            'metadata' => [
                'product_id' => 123,
                'chat_id' => 'chat_456',
                'source' => 'product_page',
            ],
        ]);

        $callId = $response->json('data.call_id');
        $call = VideoCall::where('call_id', $callId)->first();

        // Verify metadata is stored correctly
        $this->assertNotNull($call->metadata);
        $this->assertEquals(123, $call->metadata['product_id']);
        $this->assertEquals('chat_456', $call->metadata['chat_id']);
        $this->assertEquals('product_page', $call->metadata['source']);

        // Accept call and verify timestamps
        Sanctum::actingAs($this->vendor);
        $this->postJson("/api/calls/{$callId}/accept");

        $call->refresh();
        $this->assertNotNull($call->accepted_at);
        $this->assertEquals('accepted', $call->status);

        // End call and verify duration calculation
        $call->update(['connected_at' => now()->subMinutes(3)]);
        
        Sanctum::actingAs($this->customer);
        $this->postJson("/api/calls/{$callId}/end");

        $call->refresh();
        $this->assertNotNull($call->ended_at);
        $this->assertEquals('ended', $call->status);
        $this->assertGreaterThan(0, $call->duration);
    }

    /** @test */
    public function authentication_is_required_for_all_endpoints()
    {
        $call = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
        ]);

        // Test all endpoints without authentication
        $endpoints = [
            ['POST', '/api/calls/initiate'],
            ['POST', "/api/calls/{$call->call_id}/accept"],
            ['POST', "/api/calls/{$call->call_id}/reject"],
            ['POST', "/api/calls/{$call->call_id}/end"],
            ['GET', "/api/calls/{$call->call_id}"],
            ['GET', '/api/calls/history'],
            ['GET', '/api/calls/statistics'],
        ];

        foreach ($endpoints as [$method, $url]) {
            $response = $this->json($method, $url);
            $response->assertStatus(401);
        }
    }
}
