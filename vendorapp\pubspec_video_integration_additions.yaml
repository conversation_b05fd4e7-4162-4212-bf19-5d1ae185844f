# Add these dependencies to your existing pubspec.yaml

dependencies:
  # Existing dependencies...
  
  # Video calling dependencies
  flutter_webrtc: ^0.9.48
  socket_io_client: ^2.0.3+1
  permission_handler: ^11.0.1
  wakelock_plus: ^1.1.4
  logger: ^2.0.2+1
  dio: ^5.3.2
  uuid: ^4.1.0
  
  # Push notifications
  flutter_local_notifications: ^16.1.0
  
  # UI enhancements
  cached_network_image: ^3.3.0

dev_dependencies:
  # Existing dev dependencies...
  
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

# Add these to your existing flutter section
flutter:
  uses-material-design: true
  
  # Add notification sounds
  assets:
    - assets/sounds/call_ringtone.mp3
    - assets/sounds/call_end.mp3
    - assets/sounds/call_connect.mp3
    
  # Add custom fonts if needed
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Bold.ttf
          weight: 700
