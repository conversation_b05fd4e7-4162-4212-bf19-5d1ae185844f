const jwt = require('jsonwebtoken');
const axios = require('axios');
const logger = require('../utils/logger');

/**
 * Authentication middleware for Socket.IO connections
 * Validates JWT tokens and user permissions
 */

/**
 * Authenticate socket connection
 * @param {Object} socket - Socket.IO socket object
 * @param {Function} next - Next middleware function
 */
async function authenticateSocket(socket, next) {
  try {
    const token = extractToken(socket);
    
    if (!token) {
      logger.warn('Socket connection attempted without token', {
        socketId: socket.id,
        ip: socket.handshake.address
      });
      return next(new Error('Authentication token required'));
    }

    // Validate token and get user info
    const user = await validateToken(token);
    
    if (!user) {
      logger.warn('Socket connection attempted with invalid token', {
        socketId: socket.id,
        ip: socket.handshake.address
      });
      return next(new Error('Invalid authentication token'));
    }

    // Check if user is active
    if (!user.is_active) {
      logger.warn('Inactive user attempted connection', {
        socketId: socket.id,
        userId: user.id,
        ip: socket.handshake.address
      });
      return next(new Error('User account is not active'));
    }

    // Attach user info to socket
    socket.userId = user.id;
    socket.userInfo = {
      id: user.id,
      name: user.name,
      email: user.email,
      is_active: user.is_active
    };

    logger.info('Socket authenticated successfully', {
      socketId: socket.id,
      userId: user.id,
      userName: user.name
    });

    next();

  } catch (error) {
    logger.error('Socket authentication error', {
      error: error.message,
      socketId: socket.id,
      ip: socket.handshake.address
    });
    next(new Error('Authentication failed'));
  }
}

/**
 * Extract authentication token from socket handshake
 * @param {Object} socket - Socket.IO socket object
 * @returns {string|null} Token or null if not found
 */
function extractToken(socket) {
  const { auth, query, headers } = socket.handshake;
  
  // Try different token sources
  let token = null;
  
  // 1. From auth object (recommended)
  if (auth && auth.token) {
    token = auth.token;
  }
  
  // 2. From query parameters
  else if (query && query.token) {
    token = query.token;
  }
  
  // 3. From Authorization header
  else if (headers && headers.authorization) {
    const authHeader = headers.authorization;
    if (authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }
  }
  
  return token;
}

/**
 * Validate JWT token and get user information
 * @param {string} token - JWT token
 * @returns {Object|null} User object or null if invalid
 */
async function validateToken(token) {
  try {
    // Method 1: Validate JWT locally (if you have the secret)
    if (process.env.JWT_SECRET) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // For Laravel Sanctum tokens, you might need to validate differently
      // This is a simplified example
      return {
        id: decoded.sub || decoded.user_id,
        name: decoded.name,
        email: decoded.email,
        is_active: decoded.is_active !== false
      };
    }
    
    // Method 2: Validate with Laravel API (recommended for Sanctum tokens)
    else if (process.env.LARAVEL_API_URL) {
      const response = await axios.get(`${process.env.LARAVEL_API_URL}/user`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json'
        },
        timeout: 5000 // 5 second timeout
      });

      if (response.status === 200 && response.data) {
        return response.data;
      }
    }
    
    // Method 3: Simple token validation (for development)
    else if (process.env.NODE_ENV === 'development' && process.env.ALLOW_DEV_TOKENS === 'true') {
      // For development, allow simple user ID tokens
      const userId = parseInt(token);
      if (!isNaN(userId) && userId > 0) {
        return {
          id: userId,
          name: `User ${userId}`,
          email: `user${userId}@example.com`,
          is_active: true
        };
      }
    }

    return null;

  } catch (error) {
    logger.error('Token validation error', {
      error: error.message,
      tokenLength: token ? token.length : 0
    });
    return null;
  }
}

/**
 * Middleware to check if user can access a specific call
 * @param {string} callId - Call ID
 * @param {string} userId - User ID
 * @returns {Promise<boolean>} True if user can access the call
 */
async function canAccessCall(callId, userId) {
  try {
    // If Laravel API is available, check with Laravel
    if (process.env.LARAVEL_API_URL) {
      const response = await axios.get(
        `${process.env.LARAVEL_API_URL}/calls/${callId}/status`,
        {
          headers: {
            'Authorization': `Bearer ${await getUserToken(userId)}`,
            'Accept': 'application/json'
          },
          timeout: 3000
        }
      );

      if (response.status === 200) {
        const callData = response.data.data;
        return callData.caller.id === userId || callData.receiver.id === userId;
      }
    }

    // Fallback: Allow access (you might want to implement your own logic here)
    return true;

  } catch (error) {
    logger.error('Error checking call access', {
      error: error.message,
      callId: callId,
      userId: userId
    });
    
    // In case of error, allow access to avoid blocking legitimate users
    return true;
  }
}

/**
 * Get user token for API calls (this would need to be implemented based on your token storage)
 * @param {string} userId - User ID
 * @returns {Promise<string|null>} User token or null
 */
async function getUserToken(userId) {
  // This is a placeholder - you would implement token storage/retrieval
  // based on your authentication system
  return null;
}

/**
 * Rate limiting middleware for socket events
 * @param {number} maxRequests - Maximum requests per window
 * @param {number} windowMs - Time window in milliseconds
 * @returns {Function} Middleware function
 */
function createRateLimiter(maxRequests = 100, windowMs = 60000) {
  const requests = new Map();

  return function rateLimitMiddleware(socket, next) {
    const userId = socket.userId;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean up old requests
    if (requests.has(userId)) {
      const userRequests = requests.get(userId);
      const validRequests = userRequests.filter(timestamp => timestamp > windowStart);
      requests.set(userId, validRequests);
    }

    // Check rate limit
    const userRequests = requests.get(userId) || [];
    if (userRequests.length >= maxRequests) {
      logger.warn('Rate limit exceeded', {
        userId: userId,
        requests: userRequests.length,
        maxRequests: maxRequests
      });
      return next(new Error('Rate limit exceeded'));
    }

    // Add current request
    userRequests.push(now);
    requests.set(userId, userRequests);

    next();
  };
}

/**
 * Validate user permissions for specific actions
 * @param {Object} socket - Socket.IO socket object
 * @param {string} action - Action being performed
 * @param {Object} data - Action data
 * @returns {boolean} True if user has permission
 */
function hasPermission(socket, action, data) {
  const user = socket.userInfo;
  
  if (!user || !user.is_active) {
    return false;
  }

  // Add your permission logic here
  switch (action) {
    case 'join-call':
    case 'webrtc-offer':
    case 'webrtc-answer':
    case 'ice-candidate':
    case 'connection-state':
      return true; // Basic permissions for call participation
      
    default:
      return true;
  }
}

module.exports = {
  authenticateSocket,
  canAccessCall,
  createRateLimiter,
  hasPermission,
  validateToken
};
