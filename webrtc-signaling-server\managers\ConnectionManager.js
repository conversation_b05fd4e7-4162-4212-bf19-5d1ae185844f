const logger = require('../utils/logger');

/**
 * Manages WebSocket connections for users
 * Handles mapping between user IDs and socket connections
 */
class ConnectionManager {
  constructor() {
    // Map of userId -> Set of socketIds (users can have multiple connections)
    this.userConnections = new Map();
    
    // Map of socketId -> { userId, socket }
    this.socketConnections = new Map();
    
    // Connection statistics
    this.stats = {
      totalConnections: 0,
      activeUsers: 0,
      connectionsToday: 0,
      lastReset: new Date().toDateString()
    };
  }

  /**
   * Add a new connection for a user
   * @param {string} userId - User ID
   * @param {string} socketId - Socket ID
   * @param {Object} socket - Socket.IO socket object
   */
  addConnection(userId, socketId, socket) {
    try {
      // Add to socket connections map
      this.socketConnections.set(socketId, {
        userId: userId,
        socket: socket,
        connectedAt: new Date(),
        lastActivity: new Date()
      });

      // Add to user connections map
      if (!this.userConnections.has(userId)) {
        this.userConnections.set(userId, new Set());
      }
      this.userConnections.get(userId).add(socketId);

      // Update statistics
      this.updateStats();

      logger.info('Connection added', {
        userId: userId,
        socketId: socketId,
        totalConnections: this.getConnectionCount(),
        activeUsers: this.getActiveUserCount()
      });

    } catch (error) {
      logger.error('Error adding connection', {
        error: error.message,
        userId: userId,
        socketId: socketId
      });
    }
  }

  /**
   * Remove a connection
   * @param {string} userId - User ID
   * @param {string} socketId - Socket ID
   */
  removeConnection(userId, socketId) {
    try {
      // Remove from socket connections map
      this.socketConnections.delete(socketId);

      // Remove from user connections map
      if (this.userConnections.has(userId)) {
        this.userConnections.get(userId).delete(socketId);
        
        // If user has no more connections, remove the user entry
        if (this.userConnections.get(userId).size === 0) {
          this.userConnections.delete(userId);
        }
      }

      // Update statistics
      this.updateStats();

      logger.info('Connection removed', {
        userId: userId,
        socketId: socketId,
        totalConnections: this.getConnectionCount(),
        activeUsers: this.getActiveUserCount()
      });

    } catch (error) {
      logger.error('Error removing connection', {
        error: error.message,
        userId: userId,
        socketId: socketId
      });
    }
  }

  /**
   * Get a user's primary socket connection
   * @param {string} userId - User ID
   * @returns {Object|null} Socket object or null if not found
   */
  getUserSocket(userId) {
    try {
      const userSockets = this.userConnections.get(userId);
      if (!userSockets || userSockets.size === 0) {
        return null;
      }

      // Get the first (most recent) socket for the user
      const socketId = userSockets.values().next().value;
      const connectionInfo = this.socketConnections.get(socketId);
      
      return connectionInfo ? connectionInfo.socket : null;

    } catch (error) {
      logger.error('Error getting user socket', {
        error: error.message,
        userId: userId
      });
      return null;
    }
  }

  /**
   * Get all socket connections for a user
   * @param {string} userId - User ID
   * @returns {Array} Array of socket objects
   */
  getUserSockets(userId) {
    try {
      const userSockets = this.userConnections.get(userId);
      if (!userSockets || userSockets.size === 0) {
        return [];
      }

      const sockets = [];
      for (const socketId of userSockets) {
        const connectionInfo = this.socketConnections.get(socketId);
        if (connectionInfo) {
          sockets.push(connectionInfo.socket);
        }
      }

      return sockets;

    } catch (error) {
      logger.error('Error getting user sockets', {
        error: error.message,
        userId: userId
      });
      return [];
    }
  }

  /**
   * Check if a user is connected
   * @param {string} userId - User ID
   * @returns {boolean} True if user is connected
   */
  isUserConnected(userId) {
    return this.userConnections.has(userId) && this.userConnections.get(userId).size > 0;
  }

  /**
   * Get user ID from socket ID
   * @param {string} socketId - Socket ID
   * @returns {string|null} User ID or null if not found
   */
  getUserIdFromSocket(socketId) {
    const connectionInfo = this.socketConnections.get(socketId);
    return connectionInfo ? connectionInfo.userId : null;
  }

  /**
   * Update last activity for a socket
   * @param {string} socketId - Socket ID
   */
  updateLastActivity(socketId) {
    const connectionInfo = this.socketConnections.get(socketId);
    if (connectionInfo) {
      connectionInfo.lastActivity = new Date();
    }
  }

  /**
   * Get total number of active connections
   * @returns {number} Number of connections
   */
  getConnectionCount() {
    return this.socketConnections.size;
  }

  /**
   * Get number of active users
   * @returns {number} Number of unique users
   */
  getActiveUserCount() {
    return this.userConnections.size;
  }

  /**
   * Get all connected user IDs
   * @returns {Array} Array of user IDs
   */
  getConnectedUserIds() {
    return Array.from(this.userConnections.keys());
  }

  /**
   * Get connection statistics
   * @returns {Object} Statistics object
   */
  getStats() {
    return {
      ...this.stats,
      currentConnections: this.getConnectionCount(),
      currentActiveUsers: this.getActiveUserCount(),
      averageConnectionsPerUser: this.getActiveUserCount() > 0 
        ? (this.getConnectionCount() / this.getActiveUserCount()).toFixed(2) 
        : 0
    };
  }

  /**
   * Get detailed connection information
   * @returns {Array} Array of connection details
   */
  getConnectionDetails() {
    const details = [];
    
    for (const [socketId, connectionInfo] of this.socketConnections) {
      details.push({
        socketId: socketId,
        userId: connectionInfo.userId,
        connectedAt: connectionInfo.connectedAt,
        lastActivity: connectionInfo.lastActivity,
        connectionDuration: Date.now() - connectionInfo.connectedAt.getTime()
      });
    }

    return details;
  }

  /**
   * Clean up stale connections
   * @param {number} timeoutMinutes - Timeout in minutes for inactive connections
   */
  cleanupStaleConnections(timeoutMinutes = 30) {
    const timeout = timeoutMinutes * 60 * 1000; // Convert to milliseconds
    const now = Date.now();
    const staleConnections = [];

    for (const [socketId, connectionInfo] of this.socketConnections) {
      const timeSinceActivity = now - connectionInfo.lastActivity.getTime();
      
      if (timeSinceActivity > timeout) {
        staleConnections.push({
          socketId: socketId,
          userId: connectionInfo.userId,
          timeSinceActivity: timeSinceActivity
        });
      }
    }

    // Disconnect stale connections
    staleConnections.forEach(({ socketId, userId }) => {
      const connectionInfo = this.socketConnections.get(socketId);
      if (connectionInfo && connectionInfo.socket) {
        connectionInfo.socket.disconnect(true);
        logger.info('Disconnected stale connection', {
          socketId: socketId,
          userId: userId
        });
      }
    });

    return staleConnections.length;
  }

  /**
   * Update internal statistics
   * @private
   */
  updateStats() {
    const today = new Date().toDateString();
    
    // Reset daily counter if it's a new day
    if (this.stats.lastReset !== today) {
      this.stats.connectionsToday = 0;
      this.stats.lastReset = today;
    }

    this.stats.totalConnections = this.getConnectionCount();
    this.stats.activeUsers = this.getActiveUserCount();
    this.stats.connectionsToday++;
  }

  /**
   * Reset all connections (for testing or maintenance)
   */
  reset() {
    this.userConnections.clear();
    this.socketConnections.clear();
    this.stats = {
      totalConnections: 0,
      activeUsers: 0,
      connectionsToday: 0,
      lastReset: new Date().toDateString()
    };
    
    logger.info('Connection manager reset');
  }
}

module.exports = ConnectionManager;
