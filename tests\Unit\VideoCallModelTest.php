<?php

namespace Tests\Unit;

use App\Models\User;
use App\Models\VideoCall;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class VideoCallModelTest extends TestCase
{
    use RefreshDatabase;

    protected $customer;
    protected $vendor;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->customer = User::factory()->create(['user_type' => 'customer']);
        $this->vendor = User::factory()->create(['user_type' => 'vendor']);
    }

    /** @test */
    public function it_generates_unique_call_id_on_creation()
    {
        $call1 = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
        ]);

        $call2 = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
        ]);

        $this->assertNotEquals($call1->call_id, $call2->call_id);
        $this->assertNotEmpty($call1->call_id);
        $this->assertNotEmpty($call2->call_id);
    }

    /** @test */
    public function it_calculates_duration_correctly()
    {
        $call = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'status' => 'ended',
            'connected_at' => Carbon::now()->subMinutes(10),
            'ended_at' => Carbon::now()->subMinutes(5),
        ]);

        // Duration should be 5 minutes (300 seconds)
        $this->assertEquals(300, $call->duration);
    }

    /** @test */
    public function it_returns_zero_duration_when_not_connected()
    {
        $call = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'status' => 'rejected',
            'connected_at' => null,
            'ended_at' => Carbon::now(),
        ]);

        $this->assertEquals(0, $call->duration);
    }

    /** @test */
    public function it_returns_zero_duration_when_not_ended()
    {
        $call = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'status' => 'connected',
            'connected_at' => Carbon::now()->subMinutes(5),
            'ended_at' => null,
        ]);

        $this->assertEquals(0, $call->duration);
    }

    /** @test */
    public function it_has_correct_relationships()
    {
        $call = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
        ]);

        // Test caller relationship
        $this->assertInstanceOf(User::class, $call->caller);
        $this->assertEquals($this->customer->id, $call->caller->id);

        // Test receiver relationship
        $this->assertInstanceOf(User::class, $call->receiver);
        $this->assertEquals($this->vendor->id, $call->receiver->id);
    }

    /** @test */
    public function it_casts_metadata_to_array()
    {
        $metadata = [
            'product_id' => 123,
            'chat_id' => 'chat_456',
            'source' => 'product_page',
        ];

        $call = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'metadata' => $metadata,
        ]);

        $this->assertIsArray($call->metadata);
        $this->assertEquals($metadata, $call->metadata);
    }

    /** @test */
    public function it_casts_timestamps_correctly()
    {
        $call = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'accepted_at' => Carbon::now(),
            'connected_at' => Carbon::now(),
            'ended_at' => Carbon::now(),
        ]);

        $this->assertInstanceOf(Carbon::class, $call->accepted_at);
        $this->assertInstanceOf(Carbon::class, $call->connected_at);
        $this->assertInstanceOf(Carbon::class, $call->ended_at);
    }

    /** @test */
    public function it_has_correct_fillable_attributes()
    {
        $call = new VideoCall();
        
        $expectedFillable = [
            'call_id',
            'caller_id',
            'receiver_id',
            'call_type',
            'status',
            'metadata',
            'accepted_at',
            'connected_at',
            'ended_at',
        ];

        $this->assertEquals($expectedFillable, $call->getFillable());
    }

    /** @test */
    public function it_scopes_active_calls_correctly()
    {
        // Create calls with different statuses
        VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'status' => 'initiated',
        ]);

        VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'status' => 'accepted',
        ]);

        VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'status' => 'connected',
        ]);

        VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'status' => 'ended',
        ]);

        VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'status' => 'rejected',
        ]);

        $activeCalls = VideoCall::active()->get();
        
        // Should return 3 active calls (initiated, accepted, connected)
        $this->assertCount(3, $activeCalls);
        
        $activeStatuses = $activeCalls->pluck('status')->toArray();
        $this->assertContains('initiated', $activeStatuses);
        $this->assertContains('accepted', $activeStatuses);
        $this->assertContains('connected', $activeStatuses);
        $this->assertNotContains('ended', $activeStatuses);
        $this->assertNotContains('rejected', $activeStatuses);
    }

    /** @test */
    public function it_scopes_for_user_correctly()
    {
        $otherUser = User::factory()->create(['user_type' => 'customer']);

        // Create calls involving the customer
        VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
        ]);

        VideoCall::factory()->create([
            'caller_id' => $this->vendor->id,
            'receiver_id' => $this->customer->id,
        ]);

        // Create call not involving the customer
        VideoCall::factory()->create([
            'caller_id' => $otherUser->id,
            'receiver_id' => $this->vendor->id,
        ]);

        $customerCalls = VideoCall::forUser($this->customer->id)->get();
        
        // Should return 2 calls involving the customer
        $this->assertCount(2, $customerCalls);
    }

    /** @test */
    public function it_determines_if_call_is_incoming_correctly()
    {
        $outgoingCall = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
        ]);

        $incomingCall = VideoCall::factory()->create([
            'caller_id' => $this->vendor->id,
            'receiver_id' => $this->customer->id,
        ]);

        $this->assertFalse($outgoingCall->isIncomingFor($this->customer->id));
        $this->assertTrue($outgoingCall->isIncomingFor($this->vendor->id));

        $this->assertTrue($incomingCall->isIncomingFor($this->customer->id));
        $this->assertFalse($incomingCall->isIncomingFor($this->vendor->id));
    }

    /** @test */
    public function it_determines_if_user_can_perform_actions()
    {
        $call = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'status' => 'initiated',
        ]);

        $otherUser = User::factory()->create(['user_type' => 'customer']);

        // Participants can perform actions
        $this->assertTrue($call->canUserPerformAction($this->customer->id));
        $this->assertTrue($call->canUserPerformAction($this->vendor->id));

        // Non-participants cannot perform actions
        $this->assertFalse($call->canUserPerformAction($otherUser->id));
    }

    /** @test */
    public function it_validates_status_transitions()
    {
        $call = VideoCall::factory()->create([
            'caller_id' => $this->customer->id,
            'receiver_id' => $this->vendor->id,
            'status' => 'initiated',
        ]);

        // Valid transitions
        $this->assertTrue($call->canTransitionTo('accepted'));
        $this->assertTrue($call->canTransitionTo('rejected'));
        $this->assertTrue($call->canTransitionTo('missed'));

        // Invalid transitions
        $this->assertFalse($call->canTransitionTo('connected')); // Must be accepted first

        // Update to accepted
        $call->update(['status' => 'accepted']);
        
        $this->assertTrue($call->canTransitionTo('connected'));
        $this->assertTrue($call->canTransitionTo('ended'));
        $this->assertFalse($call->canTransitionTo('initiated')); // Cannot go back

        // Update to ended
        $call->update(['status' => 'ended']);
        
        $this->assertFalse($call->canTransitionTo('connected')); // Cannot change from ended
        $this->assertFalse($call->canTransitionTo('accepted'));
    }
}
