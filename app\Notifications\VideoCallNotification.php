<?php

namespace App\Notifications;

use App\Models\VideoCall;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\DatabaseMessage;
use Illuminate\Notifications\Notification;

class VideoCallNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $videoCall;
    protected $type;

    /**
     * Create a new notification instance.
     */
    public function __construct(VideoCall $videoCall, string $type)
    {
        $this->videoCall = $videoCall;
        $this->type = $type;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $message = new MailMessage();

        switch ($this->type) {
            case 'incoming_call':
                $message->subject('Incoming Video Call')
                        ->line($this->videoCall->caller->name . ' is calling you.')
                        ->action('Answer Call', url('/calls/' . $this->videoCall->call_id))
                        ->line('This call will expire if not answered within 2 minutes.');
                break;

            case 'call_missed':
                $message->subject('Missed Call')
                        ->line('You missed a call from ' . $this->videoCall->caller->name . '.')
                        ->line('Call time: ' . $this->videoCall->initiated_at->format('M j, Y g:i A'))
                        ->action('Call Back', url('/users/' . $this->videoCall->caller->id));
                break;

            case 'call_ended':
                $otherParticipant = $this->videoCall->getOtherParticipant($notifiable->id);
                $message->subject('Call Ended')
                        ->line('Your call with ' . $otherParticipant->name . ' has ended.')
                        ->line('Duration: ' . $this->videoCall->formatted_duration);
                break;
        }

        return $message;
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        $data = [
            'call_id' => $this->videoCall->call_id,
            'type' => $this->type,
            'call_type' => $this->videoCall->call_type,
            'status' => $this->videoCall->status,
            'initiated_at' => $this->videoCall->initiated_at,
        ];

        switch ($this->type) {
            case 'incoming_call':
                $data['caller'] = [
                    'id' => $this->videoCall->caller->id,
                    'name' => $this->videoCall->caller->name,
                    'photo' => $this->videoCall->caller->photo,
                ];
                $data['message'] = $this->videoCall->caller->name . ' is calling you';
                $data['action_url'] = url('/calls/' . $this->videoCall->call_id);
                break;

            case 'call_missed':
                $data['caller'] = [
                    'id' => $this->videoCall->caller->id,
                    'name' => $this->videoCall->caller->name,
                    'photo' => $this->videoCall->caller->photo,
                ];
                $data['message'] = 'You missed a call from ' . $this->videoCall->caller->name;
                break;

            case 'call_ended':
                $otherParticipant = $this->videoCall->getOtherParticipant($notifiable->id);
                $data['other_participant'] = [
                    'id' => $otherParticipant->id,
                    'name' => $otherParticipant->name,
                    'photo' => $otherParticipant->photo,
                ];
                $data['message'] = 'Call with ' . $otherParticipant->name . ' ended';
                $data['duration'] = $this->videoCall->duration;
                $data['formatted_duration'] = $this->videoCall->formatted_duration;
                break;

            case 'call_accepted':
                $data['message'] = 'Call accepted';
                $data['accepted_at'] = $this->videoCall->accepted_at;
                break;

            case 'call_rejected':
                $data['message'] = 'Call rejected';
                $data['rejection_reason'] = $this->videoCall->rejection_reason;
                break;
        }

        return $data;
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): array
    {
        return $this->toArray($notifiable);
    }
}
