/// Test configuration and utilities for WebRTC video calling tests
/// 
/// This file contains common test configurations, mock data, and utility
/// functions used across all test files in the WebRTC video calling system.

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';

/// Test configuration constants
class TestConfig {
  static const String testSignalingServerUrl = 'ws://localhost:3001';
  static const String testApiBaseUrl = 'http://localhost:8000/api';
  static const String testAuthToken = 'test-auth-token-123';
  static const String testUserId = 'test-user-456';
  static const String testVendorId = 'test-vendor-789';
  static const String testCallId = 'test-call-abc123';
  
  /// Test vendor information
  static const Map<String, dynamic> testVendorInfo = {
    'id': testVendorId,
    'name': 'Test Vendor',
    'business_name': 'Test Business',
    'avatar': 'https://example.com/avatar.jpg',
    'phone': '+1234567890',
    'email': '<EMAIL>',
  };
  
  /// Test customer information
  static const Map<String, dynamic> testCustomerInfo = {
    'id': testUserId,
    'name': 'Test Customer',
    'avatar': 'https://example.com/customer-avatar.jpg',
    'phone': '+0987654321',
    'email': '<EMAIL>',
  };
}

/// Mock WebRTC session descriptions
class MockSessionDescriptions {
  static Map<String, dynamic> get mockOffer => {
    'type': 'offer',
    'sdp': '''v=0
o=- 123456789 2 IN IP4 127.0.0.1
s=-
t=0 0
a=group:BUNDLE 0 1
a=msid-semantic: WMS stream
m=audio 9 UDP/TLS/RTP/SAVPF 111
c=IN IP4 0.0.0.0
a=rtcp:9 IN IP4 0.0.0.0
a=ice-ufrag:test
a=ice-pwd:testpassword
a=fingerprint:sha-256 AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99
a=setup:actpass
a=mid:0
a=sendrecv
a=rtcp-mux
a=rtpmap:111 opus/48000/2
m=video 9 UDP/TLS/RTP/SAVPF 96
c=IN IP4 0.0.0.0
a=rtcp:9 IN IP4 0.0.0.0
a=ice-ufrag:test
a=ice-pwd:testpassword
a=fingerprint:sha-256 AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99:AA:BB:CC:DD:EE:FF:00:11:22:33:44:55:66:77:88:99
a=setup:actpass
a=mid:1
a=sendrecv
a=rtcp-mux
a=rtpmap:96 VP8/90000''',
  };
  
  static Map<String, dynamic> get mockAnswer => {
    'type': 'answer',
    'sdp': '''v=0
o=- 987654321 2 IN IP4 127.0.0.1
s=-
t=0 0
a=group:BUNDLE 0 1
a=msid-semantic: WMS stream
m=audio 9 UDP/TLS/RTP/SAVPF 111
c=IN IP4 0.0.0.0
a=rtcp:9 IN IP4 0.0.0.0
a=ice-ufrag:answer
a=ice-pwd:answerpassword
a=fingerprint:sha-256 FF:EE:DD:CC:BB:AA:99:88:77:66:55:44:33:22:11:00:FF:EE:DD:CC:BB:AA:99:88:77:66:55:44:33:22:11:00
a=setup:active
a=mid:0
a=sendrecv
a=rtcp-mux
a=rtpmap:111 opus/48000/2
m=video 9 UDP/TLS/RTP/SAVPF 96
c=IN IP4 0.0.0.0
a=rtcp:9 IN IP4 0.0.0.0
a=ice-ufrag:answer
a=ice-pwd:answerpassword
a=fingerprint:sha-256 FF:EE:DD:CC:BB:AA:99:88:77:66:55:44:33:22:11:00:FF:EE:DD:CC:BB:AA:99:88:77:66:55:44:33:22:11:00
a=setup:active
a=mid:1
a=sendrecv
a=rtcp-mux
a=rtpmap:96 VP8/90000''',
  };
}

/// Mock ICE candidates
class MockIceCandidates {
  static Map<String, dynamic> get hostCandidate => {
    'candidate': 'candidate:1 1 UDP 2130706431 ************* 54400 typ host',
    'sdpMid': '0',
    'sdpMLineIndex': 0,
  };
  
  static Map<String, dynamic> get srflxCandidate => {
    'candidate': 'candidate:2 1 UDP 1694498815 ************* 54401 typ srflx raddr ************* rport 54400',
    'sdpMid': '0',
    'sdpMLineIndex': 0,
  };
  
  static Map<String, dynamic> get relayCandidate => {
    'candidate': 'candidate:3 1 UDP 16777215 ************** 54402 typ relay raddr ************* rport 54401',
    'sdpMid': '0',
    'sdpMLineIndex': 0,
  };
}

/// Mock WebSocket messages
class MockWebSocketMessages {
  static Map<String, dynamic> incomingCall({
    String? callId,
    String? fromUserId,
    Map<String, dynamic>? vendorInfo,
  }) => {
    'type': 'incoming-call',
    'data': {
      'call_id': callId ?? TestConfig.testCallId,
      'from_user_id': fromUserId ?? TestConfig.testVendorId,
      'vendor_info': vendorInfo ?? TestConfig.testVendorInfo,
    },
  };
  
  static Map<String, dynamic> webrtcOffer({
    String? callId,
    String? fromUserId,
    Map<String, dynamic>? offer,
  }) => {
    'type': 'webrtc-offer-received',
    'data': {
      'call_id': callId ?? TestConfig.testCallId,
      'from_user_id': fromUserId ?? TestConfig.testVendorId,
      'offer': offer ?? MockSessionDescriptions.mockOffer,
    },
  };
  
  static Map<String, dynamic> webrtcAnswer({
    String? callId,
    String? fromUserId,
    Map<String, dynamic>? answer,
  }) => {
    'type': 'webrtc-answer-received',
    'data': {
      'call_id': callId ?? TestConfig.testCallId,
      'from_user_id': fromUserId ?? TestConfig.testVendorId,
      'answer': answer ?? MockSessionDescriptions.mockAnswer,
    },
  };
  
  static Map<String, dynamic> iceCandidate({
    String? callId,
    String? fromUserId,
    Map<String, dynamic>? candidate,
  }) => {
    'type': 'ice-candidate-received',
    'data': {
      'call_id': callId ?? TestConfig.testCallId,
      'from_user_id': fromUserId ?? TestConfig.testVendorId,
      'candidate': candidate ?? MockIceCandidates.hostCandidate,
    },
  };
  
  static Map<String, dynamic> userLeft({
    String? userId,
  }) => {
    'type': 'user-left',
    'data': {
      'user_id': userId ?? TestConfig.testVendorId,
    },
  };
  
  static Map<String, dynamic> callEnded({
    String? callId,
    String? reason,
  }) => {
    'type': 'call-ended',
    'data': {
      'call_id': callId ?? TestConfig.testCallId,
      'reason': reason ?? 'normal',
    },
  };
}

/// Mock API responses
class MockApiResponses {
  static Map<String, dynamic> get initiateCallSuccess => {
    'success': true,
    'data': {
      'call_id': TestConfig.testCallId,
      'vendor_id': TestConfig.testVendorId,
      'status': 'initiated',
    },
  };
  
  static Map<String, dynamic> get acceptCallSuccess => {
    'success': true,
    'data': {
      'call_id': TestConfig.testCallId,
      'status': 'accepted',
    },
  };
  
  static Map<String, dynamic> get rejectCallSuccess => {
    'success': true,
    'data': {
      'call_id': TestConfig.testCallId,
      'status': 'rejected',
    },
  };
  
  static Map<String, dynamic> get endCallSuccess => {
    'success': true,
    'data': {
      'call_id': TestConfig.testCallId,
      'status': 'ended',
    },
  };
}

/// Test utilities
class TestUtils {
  /// Creates a delay for async operations in tests
  static Future<void> delay([int milliseconds = 10]) async {
    await Future.delayed(Duration(milliseconds: milliseconds));
  }
  
  /// Pumps the widget tree multiple times to ensure animations complete
  static Future<void> pumpAndSettle(WidgetTester tester, [Duration? duration]) async {
    await tester.pumpAndSettle(duration ?? const Duration(milliseconds: 100));
  }
  
  /// Simulates a network delay
  static Future<void> networkDelay() async {
    await Future.delayed(const Duration(milliseconds: 100));
  }
}
