<?php

namespace App\Console\Commands;

use App\Models\VideoCall;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class CleanupWebRTCSignaling extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webrtc:cleanup-signaling 
                            {--dry-run : Show what would be cleaned without actually cleaning}
                            {--force : Force cleanup even if auto-cleanup is disabled}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired WebRTC signaling data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info("WebRTC Signaling Cleanup - " . ($dryRun ? 'DRY RUN MODE' : 'LIVE MODE'));

        // Check if auto-cleanup is enabled
        if (!config('webrtc.retention.auto_cleanup', true) && !$force) {
            $this->warn('Auto-cleanup is disabled. Use --force to override.');
            return;
        }

        $this->cleanupSignalingData($dryRun);
        $this->cleanupIceCandidates($dryRun);
        $this->cleanupConnectionStates($dryRun);

        $this->info('WebRTC signaling cleanup completed!');
    }

    /**
     * Clean up expired signaling data from database
     */
    private function cleanupSignalingData($dryRun = false)
    {
        $this->info("\n--- Cleaning Up Signaling Data ---");

        $ttl = config('webrtc.retention.signaling_data_ttl', 300); // 5 minutes
        $cutoffTime = Carbon::now()->subSeconds($ttl);

        // Find calls with expired signaling data
        $expiredCalls = VideoCall::whereNotNull('webrtc_data')
            ->where('updated_at', '<', $cutoffTime)
            ->whereIn('status', [VideoCall::STATUS_ENDED, VideoCall::STATUS_REJECTED, VideoCall::STATUS_CANCELLED, VideoCall::STATUS_MISSED])
            ->get();

        $this->info("Found {$expiredCalls->count()} calls with expired signaling data");

        if ($expiredCalls->count() > 0 && !$dryRun) {
            $cleanedCount = 0;
            
            foreach ($expiredCalls as $call) {
                $webrtcData = $call->webrtc_data;
                
                // Remove signaling data but keep connection state for analytics
                if (isset($webrtcData['offer'])) {
                    unset($webrtcData['offer']);
                }
                if (isset($webrtcData['answer'])) {
                    unset($webrtcData['answer']);
                }
                
                // Keep connection state and quality metrics
                $call->update(['webrtc_data' => $webrtcData]);
                $cleanedCount++;
            }

            $this->info("Cleaned signaling data from {$cleanedCount} calls");
            
            Log::info('WebRTC signaling data cleanup completed', [
                'cleaned_calls' => $cleanedCount,
                'ttl_seconds' => $ttl
            ]);
        } elseif ($dryRun && $expiredCalls->count() > 0) {
            $this->warn("DRY RUN: Would clean signaling data from {$expiredCalls->count()} calls");
        } else {
            $this->info("No expired signaling data found");
        }
    }

    /**
     * Clean up expired ICE candidates from cache
     */
    private function cleanupIceCandidates($dryRun = false)
    {
        $this->info("\n--- Cleaning Up ICE Candidates ---");

        // ICE candidates are stored in cache with TTL, so they auto-expire
        // But we can manually clean up for specific calls that have ended

        $endedCalls = VideoCall::whereIn('status', [
            VideoCall::STATUS_ENDED, 
            VideoCall::STATUS_REJECTED, 
            VideoCall::STATUS_CANCELLED, 
            VideoCall::STATUS_MISSED
        ])->where('updated_at', '<', Carbon::now()->subMinutes(5))
          ->pluck('call_id');

        $cleanedCount = 0;
        
        foreach ($endedCalls as $callId) {
            $cacheKey = "ice_candidates:{$callId}";
            
            if (Cache::has($cacheKey)) {
                if (!$dryRun) {
                    Cache::forget($cacheKey);
                }
                $cleanedCount++;
            }
        }

        if ($cleanedCount > 0) {
            if ($dryRun) {
                $this->warn("DRY RUN: Would clean ICE candidates for {$cleanedCount} calls");
            } else {
                $this->info("Cleaned ICE candidates for {$cleanedCount} calls");
                
                Log::info('WebRTC ICE candidates cleanup completed', [
                    'cleaned_calls' => $cleanedCount
                ]);
            }
        } else {
            $this->info("No ICE candidates to clean up");
        }
    }

    /**
     * Clean up old connection states
     */
    private function cleanupConnectionStates($dryRun = false)
    {
        $this->info("\n--- Cleaning Up Connection States ---");

        $ttl = config('webrtc.retention.connection_state_ttl', 1800); // 30 minutes
        $cutoffTime = Carbon::now()->subSeconds($ttl);

        // Find calls with old connection states that should be cleaned
        $oldCalls = VideoCall::whereNotNull('webrtc_data')
            ->where('updated_at', '<', $cutoffTime)
            ->whereIn('status', [VideoCall::STATUS_ENDED, VideoCall::STATUS_REJECTED, VideoCall::STATUS_CANCELLED, VideoCall::STATUS_MISSED])
            ->get();

        $cleanedCount = 0;

        foreach ($oldCalls as $call) {
            $webrtcData = $call->webrtc_data;
            
            if (isset($webrtcData['connection_state'])) {
                if (!$dryRun) {
                    // Keep final connection state but remove intermediate states
                    $finalState = $webrtcData['connection_state'];
                    if (is_array($finalState)) {
                        $webrtcData['connection_state'] = [
                            'final_state' => $finalState['connection_state'] ?? 'unknown',
                            'cleaned_at' => now()->toISOString()
                        ];
                        $call->update(['webrtc_data' => $webrtcData]);
                    }
                }
                $cleanedCount++;
            }
        }

        if ($cleanedCount > 0) {
            if ($dryRun) {
                $this->warn("DRY RUN: Would clean connection states for {$cleanedCount} calls");
            } else {
                $this->info("Cleaned connection states for {$cleanedCount} calls");
                
                Log::info('WebRTC connection states cleanup completed', [
                    'cleaned_calls' => $cleanedCount,
                    'ttl_seconds' => $ttl
                ]);
            }
        } else {
            $this->info("No connection states to clean up");
        }
    }

    /**
     * Get statistics about current signaling data
     */
    private function getSignalingStats()
    {
        $this->info("\n--- Signaling Data Statistics ---");

        $totalCalls = VideoCall::whereNotNull('webrtc_data')->count();
        $activeCalls = VideoCall::whereNotNull('webrtc_data')
            ->whereIn('status', [VideoCall::STATUS_PENDING, VideoCall::STATUS_ACCEPTED])
            ->count();
        
        $callsWithOffers = VideoCall::whereNotNull('webrtc_data')
            ->whereRaw("JSON_EXTRACT(webrtc_data, '$.offer') IS NOT NULL")
            ->count();
            
        $callsWithAnswers = VideoCall::whereNotNull('webrtc_data')
            ->whereRaw("JSON_EXTRACT(webrtc_data, '$.answer') IS NOT NULL")
            ->count();

        $this->table([
            'Metric', 'Count'
        ], [
            ['Total calls with WebRTC data', $totalCalls],
            ['Active calls', $activeCalls],
            ['Calls with offers', $callsWithOffers],
            ['Calls with answers', $callsWithAnswers],
        ]);
    }
}
