const logger = require('../utils/logger');

/**
 * Manages active video calls and their participants
 * Handles call state and participant management
 */
class CallManager {
  constructor() {
    // Map of callId -> { participants: Set, createdAt: Date, lastActivity: Date }
    this.activeCalls = new Map();
    
    // Map of userId -> Set of callIds (user can be in multiple calls)
    this.userCalls = new Map();
    
    // Call statistics
    this.stats = {
      totalCallsCreated: 0,
      totalCallsEnded: 0,
      peakConcurrentCalls: 0,
      averageCallDuration: 0,
      callsToday: 0,
      lastReset: new Date().toDateString()
    };
  }

  /**
   * Add a participant to a call
   * @param {string} callId - Call ID
   * @param {string} userId - User ID
   * @param {string} socketId - Socket ID
   */
  addParticipant(callId, userId, socketId) {
    try {
      // Initialize call if it doesn't exist
      if (!this.activeCalls.has(callId)) {
        this.activeCalls.set(callId, {
          participants: new Map(), // userId -> { socketId, joinedAt }
          createdAt: new Date(),
          lastActivity: new Date()
        });
        
        this.stats.totalCallsCreated++;
        this.updateStats();
        
        logger.info('New call created', { callId: callId });
      }

      const call = this.activeCalls.get(callId);
      
      // Add participant to call
      call.participants.set(userId, {
        socketId: socketId,
        joinedAt: new Date()
      });
      call.lastActivity = new Date();

      // Add call to user's call list
      if (!this.userCalls.has(userId)) {
        this.userCalls.set(userId, new Set());
      }
      this.userCalls.get(userId).add(callId);

      logger.info('Participant added to call', {
        callId: callId,
        userId: userId,
        socketId: socketId,
        participantCount: call.participants.size
      });

      // Update peak concurrent calls
      const currentCalls = this.activeCalls.size;
      if (currentCalls > this.stats.peakConcurrentCalls) {
        this.stats.peakConcurrentCalls = currentCalls;
      }

    } catch (error) {
      logger.error('Error adding participant to call', {
        error: error.message,
        callId: callId,
        userId: userId,
        socketId: socketId
      });
    }
  }

  /**
   * Remove a participant from a call
   * @param {string} callId - Call ID
   * @param {string} userId - User ID
   */
  removeParticipant(callId, userId) {
    try {
      const call = this.activeCalls.get(callId);
      if (!call) {
        logger.warn('Attempted to remove participant from non-existent call', {
          callId: callId,
          userId: userId
        });
        return;
      }

      // Remove participant from call
      call.participants.delete(userId);
      call.lastActivity = new Date();

      // Remove call from user's call list
      if (this.userCalls.has(userId)) {
        this.userCalls.get(userId).delete(callId);
        
        // Clean up empty user call sets
        if (this.userCalls.get(userId).size === 0) {
          this.userCalls.delete(userId);
        }
      }

      logger.info('Participant removed from call', {
        callId: callId,
        userId: userId,
        remainingParticipants: call.participants.size
      });

      // If no participants left, end the call
      if (call.participants.size === 0) {
        this.endCall(callId);
      }

    } catch (error) {
      logger.error('Error removing participant from call', {
        error: error.message,
        callId: callId,
        userId: userId
      });
    }
  }

  /**
   * End a call and clean up resources
   * @param {string} callId - Call ID
   */
  endCall(callId) {
    try {
      const call = this.activeCalls.get(callId);
      if (!call) {
        return;
      }

      // Calculate call duration
      const duration = Date.now() - call.createdAt.getTime();
      
      // Update average call duration
      this.updateAverageCallDuration(duration);

      // Remove all participants from their user call lists
      for (const userId of call.participants.keys()) {
        if (this.userCalls.has(userId)) {
          this.userCalls.get(userId).delete(callId);
          
          if (this.userCalls.get(userId).size === 0) {
            this.userCalls.delete(userId);
          }
        }
      }

      // Remove the call
      this.activeCalls.delete(callId);
      this.stats.totalCallsEnded++;

      logger.info('Call ended', {
        callId: callId,
        duration: duration,
        participantCount: call.participants.size
      });

    } catch (error) {
      logger.error('Error ending call', {
        error: error.message,
        callId: callId
      });
    }
  }

  /**
   * Check if a user is a participant in a call
   * @param {string} callId - Call ID
   * @param {string} userId - User ID
   * @returns {boolean} True if user is a participant
   */
  isParticipant(callId, userId) {
    const call = this.activeCalls.get(callId);
    return call ? call.participants.has(userId) : false;
  }

  /**
   * Get all participants in a call
   * @param {string} callId - Call ID
   * @returns {Array} Array of participant user IDs
   */
  getCallParticipants(callId) {
    const call = this.activeCalls.get(callId);
    return call ? Array.from(call.participants.keys()) : [];
  }

  /**
   * Get detailed participant information for a call
   * @param {string} callId - Call ID
   * @returns {Array} Array of participant details
   */
  getCallParticipantDetails(callId) {
    const call = this.activeCalls.get(callId);
    if (!call) {
      return [];
    }

    const participants = [];
    for (const [userId, participantInfo] of call.participants) {
      participants.push({
        userId: userId,
        socketId: participantInfo.socketId,
        joinedAt: participantInfo.joinedAt,
        duration: Date.now() - participantInfo.joinedAt.getTime()
      });
    }

    return participants;
  }

  /**
   * Get all calls a user is participating in
   * @param {string} userId - User ID
   * @returns {Array} Array of call IDs
   */
  getUserCalls(userId) {
    const userCallSet = this.userCalls.get(userId);
    return userCallSet ? Array.from(userCallSet) : [];
  }

  /**
   * Get call information
   * @param {string} callId - Call ID
   * @returns {Object|null} Call information or null if not found
   */
  getCallInfo(callId) {
    const call = this.activeCalls.get(callId);
    if (!call) {
      return null;
    }

    return {
      callId: callId,
      participantCount: call.participants.size,
      participants: Array.from(call.participants.keys()),
      createdAt: call.createdAt,
      lastActivity: call.lastActivity,
      duration: Date.now() - call.createdAt.getTime()
    };
  }

  /**
   * Get number of active calls
   * @returns {number} Number of active calls
   */
  getActiveCallCount() {
    return this.activeCalls.size;
  }

  /**
   * Get all active call IDs
   * @returns {Array} Array of call IDs
   */
  getActiveCallIds() {
    return Array.from(this.activeCalls.keys());
  }

  /**
   * Get call statistics
   * @returns {Object} Statistics object
   */
  getStats() {
    return {
      ...this.stats,
      currentActiveCalls: this.getActiveCallCount(),
      totalActiveParticipants: this.getTotalActiveParticipants()
    };
  }

  /**
   * Get total number of active participants across all calls
   * @returns {number} Total participants
   */
  getTotalActiveParticipants() {
    let total = 0;
    for (const call of this.activeCalls.values()) {
      total += call.participants.size;
    }
    return total;
  }

  /**
   * Get detailed information about all active calls
   * @returns {Array} Array of call details
   */
  getAllCallDetails() {
    const details = [];
    
    for (const [callId, call] of this.activeCalls) {
      details.push({
        callId: callId,
        participantCount: call.participants.size,
        participants: this.getCallParticipantDetails(callId),
        createdAt: call.createdAt,
        lastActivity: call.lastActivity,
        duration: Date.now() - call.createdAt.getTime()
      });
    }

    return details;
  }

  /**
   * Clean up inactive calls
   * @param {number} timeoutMinutes - Timeout in minutes for inactive calls
   */
  cleanupInactiveCalls(timeoutMinutes = 60) {
    const timeout = timeoutMinutes * 60 * 1000; // Convert to milliseconds
    const now = Date.now();
    const inactiveCalls = [];

    for (const [callId, call] of this.activeCalls) {
      const timeSinceActivity = now - call.lastActivity.getTime();
      
      if (timeSinceActivity > timeout) {
        inactiveCalls.push({
          callId: callId,
          timeSinceActivity: timeSinceActivity,
          participantCount: call.participants.size
        });
      }
    }

    // End inactive calls
    inactiveCalls.forEach(({ callId }) => {
      this.endCall(callId);
      logger.info('Cleaned up inactive call', { callId: callId });
    });

    return inactiveCalls.length;
  }

  /**
   * Update average call duration
   * @param {number} duration - Call duration in milliseconds
   * @private
   */
  updateAverageCallDuration(duration) {
    const totalCalls = this.stats.totalCallsEnded;
    if (totalCalls === 1) {
      this.stats.averageCallDuration = duration;
    } else {
      // Calculate running average
      this.stats.averageCallDuration = 
        ((this.stats.averageCallDuration * (totalCalls - 1)) + duration) / totalCalls;
    }
  }

  /**
   * Update internal statistics
   * @private
   */
  updateStats() {
    const today = new Date().toDateString();
    
    // Reset daily counter if it's a new day
    if (this.stats.lastReset !== today) {
      this.stats.callsToday = 0;
      this.stats.lastReset = today;
    }

    this.stats.callsToday++;
  }

  /**
   * Reset all calls (for testing or maintenance)
   */
  reset() {
    this.activeCalls.clear();
    this.userCalls.clear();
    this.stats = {
      totalCallsCreated: 0,
      totalCallsEnded: 0,
      peakConcurrentCalls: 0,
      averageCallDuration: 0,
      callsToday: 0,
      lastReset: new Date().toDateString()
    };
    
    logger.info('Call manager reset');
  }
}

module.exports = CallManager;
