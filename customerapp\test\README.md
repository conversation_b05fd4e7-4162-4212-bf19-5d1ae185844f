# WebRTC Video Calling Tests

This directory contains comprehensive tests for the WebRTC video calling functionality in the customer app.

## Test Structure

```
test/
├── services/
│   ├── call_manager_test.dart          # CallManager integration tests
│   ├── call_manager_test.mocks.dart    # Generated mocks for CallManager tests
│   ├── webrtc_service_test.dart        # WebRTCService unit tests
│   └── webrtc_service_test.mocks.dart  # Generated mocks for WebRTCService tests
├── widgets/
│   └── video_call_button_test.dart     # VideoCallButton widget tests
├── test_config.dart                    # Test configuration and utilities
└── README.md                           # This file
```

## Test Categories

### 1. Service Tests (`services/`)

#### CallManager Tests (`call_manager_test.dart`)
- **Initialization**: Service initialization and failure handling
- **Outgoing Call Flow**: Complete call initiation process
- **Incoming Call Flow**: Handling incoming calls, accept/reject
- **WebRTC Signaling**: Offer/answer/ICE candidate handling
- **Call State Management**: State transitions and callbacks
- **Media Controls**: Video/audio toggle, camera switching
- **Call Termination**: Ending calls and cleanup
- **Error Scenarios**: WebRTC, WebSocket, and API error handling
- **Network Loss**: Connection loss and recovery testing

#### WebRTCService Tests (`webrtc_service_test.dart`)
- **Initialization**: WebRTC service setup and configuration
- **User Media**: Camera/microphone access and permissions
- **Offer/Answer Creation**: SDP generation and handling
- **Remote Description**: Setting remote session descriptions
- **ICE Candidates**: ICE candidate exchange and handling
- **Media Controls**: Video/audio toggle functionality
- **Connection State**: Connection state monitoring
- **Error Handling**: WebRTC error scenarios
- **Cleanup**: Resource disposal and memory management
- **Edge Cases**: Pre-initialization and post-disposal operations

### 2. Widget Tests (`widgets/`)

#### VideoCallButton Tests (`video_call_button_test.dart`)
- **Loading States**: Loading indicator display
- **Call States**: Different button states (idle, connecting, ringing, connected)
- **Availability**: Available vs unavailable button states
- **User Interaction**: Button tap handling and callbacks
- **Tooltips**: Correct tooltip text for each state
- **Accessibility**: Accessibility properties and compliance
- **State Changes**: Rapid state change handling
- **Sizing**: Consistent button sizing across states

## Test Configuration

### Dependencies
The tests use the following packages:
- `flutter_test`: Flutter testing framework
- `mockito`: Mock object generation
- `build_runner`: Code generation for mocks

### Mock Generation
Mocks are automatically generated using Mockito. To regenerate mocks:

```bash
dart run build_runner build
```

### Test Configuration (`test_config.dart`)
Contains:
- Test constants and configuration
- Mock data for WebRTC sessions
- Mock WebSocket messages
- Mock API responses
- Test utilities and helpers

## Running Tests

### Run All Tests
```bash
flutter test
```

### Run Specific Test File
```bash
flutter test test/services/call_manager_test.dart
```

### Run Tests with Coverage
```bash
flutter test --coverage
```

### Run Tests with Detailed Output
```bash
flutter test --reporter=expanded
```

### Using the Test Runner
```bash
dart run test_runner.dart
```

## Test Scenarios Covered

### Happy Path Scenarios
- ✅ Successful call initiation
- ✅ Successful call acceptance
- ✅ Successful call rejection
- ✅ Successful call termination
- ✅ Media controls (video/audio toggle)
- ✅ Camera switching
- ✅ WebRTC signaling exchange

### Error Scenarios
- ✅ Network connectivity issues
- ✅ WebRTC initialization failures
- ✅ API call failures
- ✅ WebSocket connection errors
- ✅ Invalid signaling data
- ✅ Permission denied scenarios
- ✅ Resource cleanup failures

### Edge Cases
- ✅ Rapid state changes
- ✅ Multiple simultaneous calls
- ✅ Operations before initialization
- ✅ Operations after disposal
- ✅ Null callback handling
- ✅ Invalid input data

## Test Data

### Mock Vendor Information
```dart
{
  'id': 'test-vendor-789',
  'name': 'Test Vendor',
  'business_name': 'Test Business',
  'avatar': 'https://example.com/avatar.jpg',
  'phone': '+1234567890',
  'email': '<EMAIL>',
}
```

### Mock WebRTC Session Descriptions
- Complete SDP offer with audio/video tracks
- Complete SDP answer with audio/video tracks
- Various ICE candidate types (host, srflx, relay)

### Mock WebSocket Messages
- Incoming call notifications
- WebRTC offer/answer messages
- ICE candidate messages
- User left notifications
- Call ended messages

## Coverage Goals

The tests aim for:
- **Line Coverage**: >90%
- **Function Coverage**: >95%
- **Branch Coverage**: >85%

## Continuous Integration

These tests are designed to run in CI/CD pipelines and include:
- Deterministic test execution
- Proper resource cleanup
- Clear error reporting
- Performance benchmarks

## Contributing

When adding new tests:
1. Follow the existing test structure
2. Use descriptive test names
3. Include both positive and negative test cases
4. Add appropriate mock data to `test_config.dart`
5. Update this README if adding new test categories

## Troubleshooting

### Common Issues

#### Mock Generation Fails
```bash
# Clean and regenerate
dart run build_runner clean
dart run build_runner build
```

#### Tests Timeout
- Increase timeout in test configuration
- Check for infinite loops in async operations
- Ensure proper resource cleanup

#### Flaky Tests
- Add appropriate delays for async operations
- Use `pumpAndSettle()` for widget tests
- Mock time-dependent operations

For more information, see the main project documentation.
