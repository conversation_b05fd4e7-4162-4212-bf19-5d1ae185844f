import 'package:flutter/material.dart';
import '../services/call_manager.dart';

class VideoCallButton extends StatelessWidget {
  final bool isLoading;
  final bool isAvailable;
  final CallState callState;
  final VoidCallback onPressed;

  const VideoCallButton({
    Key? key,
    required this.isLoading,
    required this.isAvailable,
    required this.callState,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: _buildButton(),
    );
  }

  Widget _buildButton() {
    // Show loading indicator
    if (isLoading) {
      return Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.blue.withOpacity(0.1),
          shape: BoxShape.circle,
        ),
        child: const Center(
          child: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
          ),
        ),
      );
    }

    // Show call state specific button
    switch (callState) {
      case CallState.initiating:
      case CallState.connecting:
        return _buildCallStateButton(
          icon: Icons.phone_callback,
          color: Colors.orange,
          tooltip: 'Connecting...',
          onPressed: onPressed,
        );
      
      case CallState.ringing:
        return _buildCallStateButton(
          icon: Icons.phone_in_talk,
          color: Colors.green,
          tooltip: 'Incoming call',
          onPressed: onPressed,
          isAnimated: true,
        );
      
      case CallState.connected:
        return _buildCallStateButton(
          icon: Icons.videocam,
          color: Colors.green,
          tooltip: 'In call - Tap to return',
          onPressed: onPressed,
        );
      
      case CallState.idle:
      case CallState.ended:
      default:
        // Show regular video call button
        return _buildCallStateButton(
          icon: Icons.videocam,
          color: isAvailable ? Colors.blue : Colors.grey,
          tooltip: isAvailable ? 'Start video call' : 'Unavailable',
          onPressed: isAvailable ? onPressed : null,
        );
    }
  }

  Widget _buildCallStateButton({
    required IconData icon,
    required Color color,
    required String tooltip,
    VoidCallback? onPressed,
    bool isAnimated = false,
  }) {
    Widget button = Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        shape: BoxShape.circle,
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: IconButton(
        icon: Icon(icon, color: color, size: 20),
        onPressed: onPressed,
        tooltip: tooltip,
        padding: EdgeInsets.zero,
      ),
    );

    if (isAnimated) {
      return _AnimatedCallButton(child: button);
    }

    return button;
  }
}

class _AnimatedCallButton extends StatefulWidget {
  final Widget child;

  const _AnimatedCallButton({required this.child});

  @override
  State<_AnimatedCallButton> createState() => _AnimatedCallButtonState();
}

class _AnimatedCallButtonState extends State<_AnimatedCallButton>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: widget.child,
        );
      },
    );
  }
}
