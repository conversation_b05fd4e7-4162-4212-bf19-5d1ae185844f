<component name="libraryTable">
  <library name="Gradle: org.robolectric:sandbox:4.3.1">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.robolectric/sandbox/4.3.1/4b1b82b661ebc747a6e4ddcfdbfc939e9cd24886/sandbox-4.3.1.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.robolectric/sandbox/4.3.1/2868cebb6909a4478713e0dff8f9d6267f0687d3/sandbox-4.3.1-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.robolectric/sandbox/4.3.1/70191589c741fe41f48a7dc353a755a096b553a7/sandbox-4.3.1-sources.jar!/" />
    </SOURCES>
  </library>
</component>