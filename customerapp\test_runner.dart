#!/usr/bin/env dart

/// Test runner for WebRTC video calling functionality
/// 
/// This script runs all tests for the WebRTC video calling system
/// and provides detailed reporting on test results.

import 'dart:io';

void main(List<String> args) async {
  print('🧪 Running WebRTC Video Calling Tests...\n');
  
  // Check if flutter is available
  final flutterResult = await Process.run('flutter', ['--version']);
  if (flutterResult.exitCode != 0) {
    print('❌ Flutter is not installed or not in PATH');
    exit(1);
  }
  
  print('✅ Flutter detected');
  
  // Run tests with coverage
  final testArgs = [
    'test',
    '--coverage',
    '--reporter=expanded',
    ...args, // Pass through any additional arguments
  ];
  
  print('📋 Running tests with command: flutter ${testArgs.join(' ')}\n');
  
  final testProcess = await Process.start(
    'flutter',
    testArgs,
    mode: ProcessStartMode.inheritStdio,
  );
  
  final exitCode = await testProcess.exitCode;
  
  if (exitCode == 0) {
    print('\n✅ All tests passed!');
    print('📊 Coverage report generated in coverage/lcov.info');
    print('💡 To view coverage report, run: genhtml coverage/lcov.info -o coverage/html');
  } else {
    print('\n❌ Some tests failed (exit code: $exitCode)');
    exit(exitCode);
  }
}
