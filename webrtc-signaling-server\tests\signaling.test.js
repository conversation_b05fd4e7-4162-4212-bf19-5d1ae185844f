const { createServer } = require('http');
const { Server } = require('socket.io');
const Client = require('socket.io-client');

describe('WebRTC Signaling Server', () => {
  let io, serverSocket, clientSocket1, clientSocket2;
  let httpServer;

  beforeAll((done) => {
    httpServer = createServer();
    io = new Server(httpServer);
    
    httpServer.listen(() => {
      const port = httpServer.address().port;
      
      // Mock authentication middleware
      io.use((socket, next) => {
        const token = socket.handshake.auth.token;
        if (token) {
          socket.userId = token; // Simple mock - use token as user ID
          socket.userInfo = {
            id: token,
            name: `User ${token}`,
            is_active: true
          };
          next();
        } else {
          next(new Error('Authentication failed'));
        }
      });

      // Set up basic event handlers
      io.on('connection', (socket) => {
        serverSocket = socket;
        
        socket.on('join-call', (data) => {
          socket.join(`call:${data.call_id}`);
          socket.emit('call-joined', {
            call_id: data.call_id,
            participants: [socket.userId]
          });
        });

        socket.on('webrtc-offer', (data) => {
          socket.to(`call:${data.call_id}`).emit('webrtc-offer-received', {
            call_id: data.call_id,
            from_user_id: socket.userId,
            offer: data.offer
          });
        });

        socket.on('webrtc-answer', (data) => {
          socket.to(`call:${data.call_id}`).emit('webrtc-answer-received', {
            call_id: data.call_id,
            from_user_id: socket.userId,
            answer: data.answer
          });
        });

        socket.on('ice-candidate', (data) => {
          socket.to(`call:${data.call_id}`).emit('ice-candidate-received', {
            call_id: data.call_id,
            from_user_id: socket.userId,
            candidate: data.candidate
          });
        });
      });

      done();
    });
  });

  afterAll(() => {
    io.close();
    httpServer.close();
  });

  beforeEach((done) => {
    const port = httpServer.address().port;
    
    // Create two client connections
    clientSocket1 = new Client(`http://localhost:${port}`, {
      auth: { token: 'user1' }
    });
    
    clientSocket2 = new Client(`http://localhost:${port}`, {
      auth: { token: 'user2' }
    });

    let connectedCount = 0;
    const onConnect = () => {
      connectedCount++;
      if (connectedCount === 2) {
        done();
      }
    };

    clientSocket1.on('connect', onConnect);
    clientSocket2.on('connect', onConnect);
  });

  afterEach(() => {
    if (clientSocket1.connected) {
      clientSocket1.disconnect();
    }
    if (clientSocket2.connected) {
      clientSocket2.disconnect();
    }
  });

  test('should authenticate users successfully', (done) => {
    expect(clientSocket1.connected).toBe(true);
    expect(clientSocket2.connected).toBe(true);
    done();
  });

  test('should allow users to join calls', (done) => {
    const callId = 'test_call_123';
    
    clientSocket1.emit('join-call', { call_id: callId });
    
    clientSocket1.on('call-joined', (data) => {
      expect(data.call_id).toBe(callId);
      expect(data.participants).toContain('user1');
      done();
    });
  });

  test('should relay WebRTC offers between users', (done) => {
    const callId = 'test_call_456';
    const testOffer = {
      type: 'offer',
      sdp: 'v=0\r\no=- 123456789 2 IN IP4 127.0.0.1\r\n...'
    };

    // Both users join the call
    clientSocket1.emit('join-call', { call_id: callId });
    clientSocket2.emit('join-call', { call_id: callId });

    // Set up listener for offer on client 2
    clientSocket2.on('webrtc-offer-received', (data) => {
      expect(data.call_id).toBe(callId);
      expect(data.from_user_id).toBe('user1');
      expect(data.offer).toEqual(testOffer);
      done();
    });

    // Send offer from client 1
    setTimeout(() => {
      clientSocket1.emit('webrtc-offer', {
        call_id: callId,
        target_user_id: 'user2',
        offer: testOffer
      });
    }, 100);
  });

  test('should relay WebRTC answers between users', (done) => {
    const callId = 'test_call_789';
    const testAnswer = {
      type: 'answer',
      sdp: 'v=0\r\no=- 987654321 2 IN IP4 127.0.0.1\r\n...'
    };

    // Both users join the call
    clientSocket1.emit('join-call', { call_id: callId });
    clientSocket2.emit('join-call', { call_id: callId });

    // Set up listener for answer on client 1
    clientSocket1.on('webrtc-answer-received', (data) => {
      expect(data.call_id).toBe(callId);
      expect(data.from_user_id).toBe('user2');
      expect(data.answer).toEqual(testAnswer);
      done();
    });

    // Send answer from client 2
    setTimeout(() => {
      clientSocket2.emit('webrtc-answer', {
        call_id: callId,
        target_user_id: 'user1',
        answer: testAnswer
      });
    }, 100);
  });

  test('should relay ICE candidates between users', (done) => {
    const callId = 'test_call_ice';
    const testCandidate = {
      candidate: 'candidate:1 1 UDP 2130706431 ************* 54400 typ host',
      sdpMid: '0',
      sdpMLineIndex: 0
    };

    // Both users join the call
    clientSocket1.emit('join-call', { call_id: callId });
    clientSocket2.emit('join-call', { call_id: callId });

    // Set up listener for ICE candidate on client 2
    clientSocket2.on('ice-candidate-received', (data) => {
      expect(data.call_id).toBe(callId);
      expect(data.from_user_id).toBe('user1');
      expect(data.candidate).toEqual(testCandidate);
      done();
    });

    // Send ICE candidate from client 1
    setTimeout(() => {
      clientSocket1.emit('ice-candidate', {
        call_id: callId,
        target_user_id: 'user2',
        candidate: testCandidate
      });
    }, 100);
  });

  test('should handle multiple users in the same call', (done) => {
    const callId = 'test_call_multi';
    let joinedCount = 0;

    const onCallJoined = () => {
      joinedCount++;
      if (joinedCount === 2) {
        // Both users have joined, test message relay
        clientSocket1.emit('ice-candidate', {
          call_id: callId,
          target_user_id: 'user2',
          candidate: {
            candidate: 'test-candidate',
            sdpMid: '0',
            sdpMLineIndex: 0
          }
        });
      }
    };

    clientSocket1.on('call-joined', onCallJoined);
    clientSocket2.on('call-joined', onCallJoined);

    clientSocket2.on('ice-candidate-received', (data) => {
      expect(data.from_user_id).toBe('user1');
      done();
    });

    // Both users join the same call
    clientSocket1.emit('join-call', { call_id: callId });
    clientSocket2.emit('join-call', { call_id: callId });
  });

  test('should reject unauthenticated connections', (done) => {
    const unauthenticatedClient = new Client(`http://localhost:${httpServer.address().port}`);
    
    unauthenticatedClient.on('connect_error', (error) => {
      expect(error.message).toContain('Authentication failed');
      done();
    });

    unauthenticatedClient.on('connect', () => {
      // Should not reach here
      expect(true).toBe(false);
      done();
    });
  });

  test('should handle user disconnection gracefully', (done) => {
    const callId = 'test_call_disconnect';
    
    // User 1 joins call
    clientSocket1.emit('join-call', { call_id: callId });
    
    clientSocket1.on('call-joined', () => {
      // Disconnect user 1
      clientSocket1.disconnect();
      
      // Wait a bit and verify server is still responsive
      setTimeout(() => {
        expect(clientSocket2.connected).toBe(true);
        done();
      }, 100);
    });
  });
});
