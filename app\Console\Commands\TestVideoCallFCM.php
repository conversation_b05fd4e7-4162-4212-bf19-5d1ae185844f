<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\UserToken;
use App\Models\VideoCall;
use App\Services\VideoCallNotificationService;
use App\Services\FCMService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestVideoCallFCM extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fcm:test-video-call 
                            {--user-id= : User ID to send test notification to}
                            {--token= : Specific FCM token to test}
                            {--type=incoming : Type of notification (incoming, accepted, rejected, ended)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test FCM notifications for video calls';

    protected $fcmService;
    protected $notificationService;

    /**
     * Create a new command instance.
     */
    public function __construct(FCMService $fcmService, VideoCallNotificationService $notificationService)
    {
        parent::__construct();
        $this->fcmService = $fcmService;
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Video Call FCM Notifications');
        $this->info('=====================================');

        $userId = $this->option('user-id');
        $token = $this->option('token');
        $type = $this->option('type');

        if ($token) {
            $this->testSpecificToken($token, $type);
        } elseif ($userId) {
            $this->testUserNotification($userId, $type);
        } else {
            $this->testWithRandomUsers($type);
        }
    }

    /**
     * Test notification with specific FCM token
     */
    private function testSpecificToken(string $token, string $type)
    {
        $this->info("Testing with specific token: " . substr($token, 0, 20) . "...");

        $success = $this->fcmService->sendCallNotification(
            [$token],
            'Test Video Call',
            'This is a test notification for video call',
            [
                'type' => 'test_' . $type,
                'call_id' => 'test_call_' . time(),
                'caller_name' => 'Test Caller',
                'test_mode' => true
            ],
            $type === 'incoming'
        );

        if ($success) {
            $this->info('✅ Test notification sent successfully');
        } else {
            $this->error('❌ Failed to send test notification');
        }
    }

    /**
     * Test notification for specific user
     */
    private function testUserNotification(int $userId, string $type)
    {
        $user = User::find($userId);
        
        if (!$user) {
            $this->error("User with ID {$userId} not found");
            return;
        }

        $this->info("Testing notification for user: {$user->name} (ID: {$user->id})");

        $tokens = UserToken::where('user_id', $user->id)
            ->whereNotNull('token')
            ->pluck('token')
            ->toArray();

        if (empty($tokens)) {
            $this->warn("No FCM tokens found for user {$user->name}");
            return;
        }

        $this->info("Found " . count($tokens) . " FCM token(s) for user");

        // Create a test video call
        $testCall = $this->createTestVideoCall($user, $type);

        switch ($type) {
            case 'incoming':
                $success = $this->notificationService->sendIncomingCallNotification($testCall);
                break;
            case 'accepted':
                $testCall->status = VideoCall::STATUS_ACCEPTED;
                $success = $this->notificationService->sendCallStatusNotification($testCall, VideoCall::STATUS_PENDING);
                break;
            case 'rejected':
                $testCall->status = VideoCall::STATUS_REJECTED;
                $testCall->rejection_reason = 'Test rejection';
                $success = $this->notificationService->sendCallStatusNotification($testCall, VideoCall::STATUS_PENDING);
                break;
            case 'ended':
                $testCall->status = VideoCall::STATUS_ENDED;
                $testCall->duration = 120;
                $success = $this->notificationService->sendCallEndedNotification($testCall);
                break;
            default:
                $this->error("Unknown notification type: {$type}");
                return;
        }

        if ($success) {
            $this->info("✅ {$type} notification sent successfully");
        } else {
            $this->error("❌ Failed to send {$type} notification");
        }
    }

    /**
     * Test with random users
     */
    private function testWithRandomUsers(string $type)
    {
        $this->info("Testing with random users...");

        $users = User::whereHas('userTokens', function ($query) {
            $query->whereNotNull('token');
        })->take(2)->get();

        if ($users->count() < 2) {
            $this->error("Need at least 2 users with FCM tokens for testing");
            return;
        }

        $caller = $users->first();
        $receiver = $users->last();

        $this->info("Caller: {$caller->name} (ID: {$caller->id})");
        $this->info("Receiver: {$receiver->name} (ID: {$receiver->id})");

        $testCall = VideoCall::make([
            'call_id' => 'test_call_' . time(),
            'caller_id' => $caller->id,
            'receiver_id' => $receiver->id,
            'status' => VideoCall::STATUS_PENDING,
            'call_type' => VideoCall::TYPE_VIDEO,
            'initiated_at' => now(),
        ]);

        $testCall->setRelation('caller', $caller);
        $testCall->setRelation('receiver', $receiver);

        $success = $this->notificationService->sendIncomingCallNotification($testCall);

        if ($success) {
            $this->info('✅ Test incoming call notification sent successfully');
        } else {
            $this->error('❌ Failed to send test notification');
        }
    }

    /**
     * Create a test video call object
     */
    private function createTestVideoCall(User $user, string $type): VideoCall
    {
        // Create a dummy caller if testing for receiver
        $caller = User::where('id', '!=', $user->id)->first() ?? User::factory()->make([
            'id' => 999,
            'name' => 'Test Caller',
            'email' => '<EMAIL>'
        ]);

        $call = VideoCall::make([
            'call_id' => 'test_call_' . time(),
            'caller_id' => $caller->id,
            'receiver_id' => $user->id,
            'status' => VideoCall::STATUS_PENDING,
            'call_type' => VideoCall::TYPE_VIDEO,
            'initiated_at' => now(),
        ]);

        $call->setRelation('caller', $caller);
        $call->setRelation('receiver', $user);

        return $call;
    }
}
