<?php

namespace App\Services;

use App\Models\VideoCall;
use App\Models\User;
use App\Models\UserToken;
use App\Traits\FirebaseMessagingTrait;
use App\Traits\FirebaseNotificationValidateTrait;
use Illuminate\Support\Facades\Log;
use Exception;

class VideoCallNotificationService
{
    use FirebaseMessagingTrait, FirebaseNotificationValidateTrait;

    protected $fcmService;

    public function __construct(FCMService $fcmService)
    {
        $this->fcmService = $fcmService;
    }

    /**
     * Send incoming call notification to receiver
     */
    public function sendIncomingCallNotification(VideoCall $videoCall): bool
    {
        try {
            $receiver = $videoCall->receiver;
            $caller = $videoCall->caller;

            // Get receiver's FCM tokens
            $fcmTokens = $this->getReceiverFCMTokens($receiver);

            if (empty($fcmTokens)) {
                Log::warning('No FCM tokens found for receiver', [
                    'receiver_id' => $receiver->id,
                    'call_id' => $videoCall->call_id
                ]);
                return false;
            }

            // Prepare notification data
            $notificationData = $this->prepareIncomingCallData($videoCall);
            
            // Send FCM notification using enhanced service
            $this->fcmService->sendCallNotification(
                $fcmTokens,
                $notificationData['title'],
                $notificationData['body'],
                $notificationData['data'],
                true // isIncomingCall
            );

            Log::info('Incoming call notification sent successfully', [
                'call_id' => $videoCall->call_id,
                'receiver_id' => $receiver->id,
                'caller_id' => $caller->id,
                'tokens_count' => count($fcmTokens)
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Failed to send incoming call notification', [
                'call_id' => $videoCall->call_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Send call status change notification
     */
    public function sendCallStatusNotification(VideoCall $videoCall, string $previousStatus): bool
    {
        try {
            $notificationData = $this->prepareStatusChangeData($videoCall, $previousStatus);
            
            if (!$notificationData) {
                return true; // No notification needed for this status change
            }

            // Determine who to notify based on status
            $targetUser = $this->getTargetUserForStatusNotification($videoCall, $previousStatus);
            
            if (!$targetUser) {
                return true; // No user to notify
            }

            $fcmTokens = $this->getReceiverFCMTokens($targetUser);

            if (empty($fcmTokens)) {
                Log::warning('No FCM tokens found for status notification', [
                    'user_id' => $targetUser->id,
                    'call_id' => $videoCall->call_id,
                    'status' => $videoCall->status
                ]);
                return false;
            }

            $this->fcmService->sendCallNotification(
                $fcmTokens,
                $notificationData['title'],
                $notificationData['body'],
                $notificationData['data'],
                false // isIncomingCall
            );

            Log::info('Call status notification sent successfully', [
                'call_id' => $videoCall->call_id,
                'user_id' => $targetUser->id,
                'status' => $videoCall->status,
                'previous_status' => $previousStatus
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Failed to send call status notification', [
                'call_id' => $videoCall->call_id,
                'status' => $videoCall->status,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send call ended notification with duration
     */
    public function sendCallEndedNotification(VideoCall $videoCall): bool
    {
        try {
            // Notify both participants
            $participants = [$videoCall->caller, $videoCall->receiver];
            $success = true;

            foreach ($participants as $participant) {
                $fcmTokens = $this->getReceiverFCMTokens($participant);
                
                if (empty($fcmTokens)) {
                    continue;
                }

                $otherParticipant = $videoCall->getOtherParticipant($participant->id);
                $notificationData = $this->prepareCallEndedData($videoCall, $otherParticipant);

                $this->fcmService->sendCallNotification(
                    $fcmTokens,
                    $notificationData['title'],
                    $notificationData['body'],
                    $notificationData['data'],
                    false // isIncomingCall
                );
            }

            return $success;

        } catch (Exception $e) {
            Log::error('Failed to send call ended notification', [
                'call_id' => $videoCall->call_id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get FCM tokens for a user
     */
    private function getReceiverFCMTokens(User $user): array
    {
        return UserToken::where('user_id', $user->id)
            ->whereNotNull('token')
            ->pluck('token')
            ->filter()
            ->unique()
            ->values()
            ->toArray();
    }

    /**
     * Prepare incoming call notification data
     */
    private function prepareIncomingCallData(VideoCall $videoCall): array
    {
        $caller = $videoCall->caller;
        $callType = ucfirst($videoCall->call_type);

        return [
            'title' => "Incoming {$callType} Call",
            'body' => "{$caller->name} is calling you",
            'data' => [
                'type' => 'incoming_call',
                'call_id' => $videoCall->call_id,
                'caller_id' => $caller->id,
                'caller_name' => $caller->name,
                'caller_photo' => $caller->photo,
                'call_type' => $videoCall->call_type,
                'room_id' => $videoCall->room_id,
                'initiated_at' => $videoCall->initiated_at->toISOString(),
                'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                'sound' => 'call_ringtone.mp3'
            ],
            'android' => [
                'channel_id' => 'video_call_channel',
                'priority' => 'high',
                'sound' => 'call_ringtone',
                'category' => 'call',
                'ongoing' => true,
                'auto_cancel' => false,
                'full_screen_intent' => true,
                'actions' => [
                    [
                        'action' => 'ACCEPT_CALL',
                        'title' => 'Accept',
                        'icon' => 'ic_call_accept'
                    ],
                    [
                        'action' => 'REJECT_CALL',
                        'title' => 'Decline',
                        'icon' => 'ic_call_decline'
                    ]
                ]
            ],
            'ios' => [
                'sound' => 'call_ringtone.caf',
                'category' => 'CALL_CATEGORY',
                'thread_id' => $videoCall->call_id,
                'interruption_level' => 'critical',
                'content_available' => true
            ]
        ];
    }

    /**
     * Prepare status change notification data
     */
    private function prepareStatusChangeData(VideoCall $videoCall, string $previousStatus): ?array
    {
        $status = $videoCall->status;
        
        switch ($status) {
            case VideoCall::STATUS_ACCEPTED:
                return [
                    'title' => 'Call Accepted',
                    'body' => 'Your call was accepted',
                    'data' => [
                        'type' => 'call_accepted',
                        'call_id' => $videoCall->call_id,
                        'status' => $status,
                        'accepted_at' => $videoCall->accepted_at->toISOString()
                    ],
                    'android' => ['channel_id' => 'call_status_channel'],
                    'ios' => ['sound' => 'default']
                ];

            case VideoCall::STATUS_REJECTED:
                return [
                    'title' => 'Call Declined',
                    'body' => $videoCall->rejection_reason ?: 'Your call was declined',
                    'data' => [
                        'type' => 'call_rejected',
                        'call_id' => $videoCall->call_id,
                        'status' => $status,
                        'rejection_reason' => $videoCall->rejection_reason
                    ],
                    'android' => ['channel_id' => 'call_status_channel'],
                    'ios' => ['sound' => 'default']
                ];

            case VideoCall::STATUS_MISSED:
                return [
                    'title' => 'Missed Call',
                    'body' => "You missed a call from {$videoCall->caller->name}",
                    'data' => [
                        'type' => 'call_missed',
                        'call_id' => $videoCall->call_id,
                        'caller_name' => $videoCall->caller->name,
                        'caller_photo' => $videoCall->caller->photo,
                        'initiated_at' => $videoCall->initiated_at->toISOString()
                    ],
                    'android' => ['channel_id' => 'call_status_channel'],
                    'ios' => ['sound' => 'default']
                ];

            default:
                return null; // No notification for other statuses
        }
    }

    /**
     * Prepare call ended notification data
     */
    private function prepareCallEndedData(VideoCall $videoCall, User $otherParticipant): array
    {
        $duration = $videoCall->formatted_duration ?: '00:00';
        
        return [
            'title' => 'Call Ended',
            'body' => "Call with {$otherParticipant->name} ended ({$duration})",
            'data' => [
                'type' => 'call_ended',
                'call_id' => $videoCall->call_id,
                'other_participant_name' => $otherParticipant->name,
                'duration' => $videoCall->duration,
                'formatted_duration' => $duration,
                'ended_at' => $videoCall->ended_at->toISOString()
            ],
            'android' => ['channel_id' => 'call_status_channel'],
            'ios' => ['sound' => 'default']
        ];
    }

    /**
     * Get target user for status notification
     */
    private function getTargetUserForStatusNotification(VideoCall $videoCall, string $previousStatus): ?User
    {
        switch ($videoCall->status) {
            case VideoCall::STATUS_ACCEPTED:
            case VideoCall::STATUS_REJECTED:
                return $videoCall->caller; // Notify caller
                
            case VideoCall::STATUS_MISSED:
                return $videoCall->caller; // Notify caller about missed call
                
            default:
                return null;
        }
    }


}
