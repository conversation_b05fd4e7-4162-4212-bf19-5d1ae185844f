import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../services/call_manager.dart' as call_mgr;
import '../widgets/video_call_button.dart';
import '../widgets/call_status_indicator.dart';
import '../widgets/incoming_call_notification.dart';
import '../screens/video_call_screen.dart' as video_call;

class ChatScreen extends StatefulWidget {
  final String vendorId;
  final Map<String, dynamic> vendorInfo;
  final String chatId;

  const ChatScreen({
    Key? key,
    required this.vendorId,
    required this.vendorInfo,
    required this.chatId,
  }) : super(key: key);

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final Logger _logger = Logger();
  final call_mgr.CallManager _callManager = call_mgr.CallManager();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<Map<String, dynamic>> _messages = [];
  bool _isLoading = false;
  String? _currentCallId;
  call_mgr.CallState _callState = call_mgr.CallState.idle;
  OverlayEntry? _incomingCallOverlay;
  OverlayEntry? _callStatusOverlay;

  @override
  void initState() {
    super.initState();
    _initializeCallManager();
    _loadMessages();
  }

  @override
  void dispose() {
    _removeOverlays();
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _initializeCallManager() async {
    try {
      // Set up call manager callbacks
      _callManager.onCallStateChanged = (state) {
        setState(() {
          _callState = state;
        });
        _updateCallStatusOverlay();
      };

      _callManager.onIncomingCall = (callId, vendorId, vendorInfo) {
        if (vendorId == widget.vendorId) {
          _currentCallId = callId;
          _showIncomingCallNotification(callId, vendorId, vendorInfo);
        }
      };

      _callManager.onError = (error) {
        _showErrorSnackBar(error);
      };

    } catch (e) {
      _logger.e('Failed to initialize call manager: $e');
    }
  }

  Future<void> _loadMessages() async {
    // Load chat messages from your existing chat service
    // This is a placeholder - replace with your actual chat loading logic
    setState(() {
      _messages = [
        {
          'id': '1',
          'message': 'Hello! I\'m interested in your products.',
          'sender_id': 'customer_id',
          'sender_type': 'customer',
          'created_at': DateTime.now().subtract(const Duration(minutes: 5)).toIso8601String(),
        },
        {
          'id': '2',
          'message': 'Hi! I\'d be happy to show you our latest collection. Would you like to have a video call?',
          'sender_id': widget.vendorId,
          'sender_type': 'vendor',
          'created_at': DateTime.now().subtract(const Duration(minutes: 3)).toIso8601String(),
        },
      ];
    });
  }

  Future<void> _startVideoCall() async {
    try {
      setState(() {
        _isLoading = true;
      });

      _logger.i('Customer starting video call with vendor: ${widget.vendorId}');
      
      // Start the call
      await _callManager.startCall(widget.vendorId);
      _currentCallId = _callManager.currentCallId;

      setState(() {
        _isLoading = false;
      });

      // Navigate to video call screen
      if (mounted && _currentCallId != null) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => video_call.VideoCallScreen(
              vendorId: widget.vendorId,
              callId: _currentCallId,
              isIncomingCall: false,
              vendorInfo: widget.vendorInfo,
            ),
          ),
        );
      }

    } catch (e) {
      _logger.e('Failed to start video call: $e');
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Failed to start video call: $e');
    }
  }

  void _showIncomingCallNotification(String callId, String vendorId, Map<String, dynamic> vendorInfo) {
    _removeIncomingCallOverlay();
    
    _incomingCallOverlay = OverlayEntry(
      builder: (context) => IncomingCallNotification(
        callId: callId,
        vendorId: vendorId,
        vendorInfo: vendorInfo,
        onDismiss: _removeIncomingCallOverlay,
        onAccept: () {
          _removeIncomingCallOverlay();
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => video_call.VideoCallScreen(
                vendorId: vendorId,
                callId: callId,
                isIncomingCall: true,
                vendorInfo: vendorInfo,
              ),
            ),
          );
        },
      ),
    );
    
    Overlay.of(context).insert(_incomingCallOverlay!);
  }

  void _updateCallStatusOverlay() {
    if (_callState == call_mgr.CallState.idle || _callState == call_mgr.CallState.ended) {
      _removeCallStatusOverlay();
      return;
    }

    _removeCallStatusOverlay();
    
    _callStatusOverlay = OverlayEntry(
      builder: (context) => CallStatusIndicator(
        callState: _callState,
        vendorName: widget.vendorInfo['name'] ?? 'Vendor',
        onTap: () {
          if (_currentCallId != null) {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => video_call.VideoCallScreen(
                  vendorId: widget.vendorId,
                  callId: _currentCallId,
                  isIncomingCall: false,
                  vendorInfo: widget.vendorInfo,
                ),
              ),
            );
          }
        },
      ),
    );
    
    Overlay.of(context).insert(_callStatusOverlay!);
  }

  void _removeOverlays() {
    _removeIncomingCallOverlay();
    _removeCallStatusOverlay();
  }

  void _removeIncomingCallOverlay() {
    _incomingCallOverlay?.remove();
    _incomingCallOverlay = null;
  }

  void _removeCallStatusOverlay() {
    _callStatusOverlay?.remove();
    _callStatusOverlay = null;
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    // Add message to local list immediately
    final newMessage = {
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'message': message,
      'sender_id': 'customer_id', // Replace with actual customer ID
      'sender_type': 'customer',
      'created_at': DateTime.now().toIso8601String(),
    };

    setState(() {
      _messages.add(newMessage);
      _messageController.clear();
    });

    // Scroll to bottom
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    });

    // Send message to your chat service
    // This is a placeholder - replace with your actual message sending logic
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            CircleAvatar(
              radius: 18,
              backgroundImage: widget.vendorInfo['avatar'] != null
                  ? NetworkImage(widget.vendorInfo['avatar'])
                  : null,
              child: widget.vendorInfo['avatar'] == null
                  ? const Icon(Icons.store, size: 18)
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.vendorInfo['name'] ?? 'Vendor',
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  if (widget.vendorInfo['is_online'] == true)
                    const Text(
                      'Online',
                      style: TextStyle(fontSize: 12, color: Colors.green),
                    ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          VideoCallButton(
            isLoading: _isLoading,
            isAvailable: widget.vendorInfo['is_available'] == true,
            callState: _callState,
            onPressed: _startVideoCall,
          ),
        ],
      ),
      body: Column(
        children: [
          // Messages list
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                final message = _messages[index];
                return _buildMessageBubble(message);
              },
            ),
          ),
          
          // Message input
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(Map<String, dynamic> message) {
    final isCustomer = message['sender_type'] == 'customer';
    final timestamp = DateTime.parse(message['created_at']);
    
    return Align(
      alignment: isCustomer ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        decoration: BoxDecoration(
          color: isCustomer ? Colors.blue : Colors.grey[300],
          borderRadius: BorderRadius.circular(18),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message['message'],
              style: TextStyle(
                color: isCustomer ? Colors.white : Colors.black87,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _formatTime(timestamp),
              style: TextStyle(
                color: isCustomer ? Colors.white70 : Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: 'Type a message...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              maxLines: null,
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _sendMessage(),
            ),
          ),
          const SizedBox(width: 8),
          CircleAvatar(
            backgroundColor: Colors.blue,
            child: IconButton(
              icon: const Icon(Icons.send, color: Colors.white),
              onPressed: _sendMessage,
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
