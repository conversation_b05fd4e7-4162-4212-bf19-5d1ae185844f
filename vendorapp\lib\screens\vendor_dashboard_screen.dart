import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../services/call_manager.dart';
import '../services/api_service.dart';
import '../widgets/incoming_call_notification.dart';


class VendorDashboardScreen extends StatefulWidget {
  const VendorDashboardScreen({Key? key}) : super(key: key);

  @override
  State<VendorDashboardScreen> createState() => _VendorDashboardScreenState();
}

class _VendorDashboardScreenState extends State<VendorDashboardScreen> {
  final Logger _logger = Logger();
  final CallManager _callManager = CallManager();
  final ApiService _apiService = ApiService();
  
  bool _isAvailable = false;
  bool _isInitializing = true;
  List<Map<String, dynamic>> _recentCalls = [];
  OverlayEntry? _incomingCallOverlay;

  @override
  void initState() {
    super.initState();
    _initializeCallManager();
    _loadRecentCalls();
  }

  @override
  void dispose() {
    _removeIncomingCallOverlay();
    super.dispose();
  }

  Future<void> _initializeCallManager() async {
    try {
      setState(() {
        _isInitializing = true;
      });

      // Initialize call manager with your configuration
      await _callManager.initialize(
        signalingServerUrl: 'ws://your-signaling-server:3001',
        apiBaseUrl: 'http://your-api-server:8000/api',
        authToken: 'your-auth-token', // Get from your auth service
        userId: 'current-vendor-id', // Get from your auth service
      );

      // Set up incoming call listener
      _callManager.onIncomingCall = (callId, customerId, customerInfo) {
        _showIncomingCallNotification(callId, customerId, customerInfo);
      };

      _callManager.onError = (error) {
        _showErrorSnackBar(error);
      };

      setState(() {
        _isInitializing = false;
      });

    } catch (e) {
      _logger.e('Failed to initialize call manager: $e');
      setState(() {
        _isInitializing = false;
      });
      _showErrorSnackBar('Failed to initialize video calling: $e');
    }
  }

  Future<void> _loadRecentCalls() async {
    try {
      final calls = await _apiService.getCallHistory(perPage: 5);
      
      setState(() {
        _recentCalls = calls;
      });

    } catch (e) {
      _logger.e('Failed to load recent calls: $e');
    }
  }

  Future<void> _toggleAvailability() async {
    try {
      final newAvailability = !_isAvailable;
      
      // Update availability via API
      await _apiService.updateAvailability(isAvailable: newAvailability);
      
      setState(() {
        _isAvailable = newAvailability;
      });

      _showSuccessSnackBar(
        newAvailability 
            ? 'You are now available for video calls'
            : 'You are now unavailable for video calls'
      );

    } catch (e) {
      _logger.e('Failed to update availability: $e');
      _showErrorSnackBar('Failed to update availability: $e');
    }
  }

  void _showIncomingCallNotification(String callId, String customerId, Map<String, dynamic> customerInfo) {
    _removeIncomingCallOverlay();
    
    _incomingCallOverlay = OverlayEntry(
      builder: (context) => IncomingCallNotification(
        callId: callId,
        customerId: customerId,
        customerInfo: customerInfo,
        onDismiss: _removeIncomingCallOverlay,
      ),
    );
    
    Overlay.of(context).insert(_incomingCallOverlay!);
  }

  void _removeIncomingCallOverlay() {
    _incomingCallOverlay?.remove();
    _incomingCallOverlay = null;
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Vendor Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadRecentCalls,
          ),
        ],
      ),
      body: _isInitializing
          ? const Center(child: CircularProgressIndicator())
          : _buildBody(),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Availability Status Card
          _buildAvailabilityCard(),
          
          const SizedBox(height: 24),
          
          // Call Statistics
          _buildCallStatistics(),
          
          const SizedBox(height: 24),
          
          // Recent Calls
          _buildRecentCalls(),
        ],
      ),
    );
  }

  Widget _buildAvailabilityCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  _isAvailable ? Icons.videocam : Icons.videocam_off,
                  size: 32,
                  color: _isAvailable ? Colors.green : Colors.grey,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Video Call Availability',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _isAvailable 
                            ? 'You are available for video calls'
                            : 'You are currently unavailable',
                        style: TextStyle(
                          color: _isAvailable ? Colors.green : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _toggleAvailability,
                icon: Icon(_isAvailable ? Icons.pause : Icons.play_arrow),
                label: Text(_isAvailable ? 'Go Offline' : 'Go Online'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isAvailable ? Colors.orange : Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCallStatistics() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Today\'s Statistics',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Calls',
                    '12',
                    Icons.call,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Answered',
                    '10',
                    Icons.call_received,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Missed',
                    '2',
                    Icons.call_missed,
                    Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildRecentCalls() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Calls',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                TextButton(
                  onPressed: () {
                    // Navigate to full call history
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_recentCalls.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'No recent calls',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            else
              ...(_recentCalls.take(3).map((call) => _buildCallItem(call))),
          ],
        ),
      ),
    );
  }

  Widget _buildCallItem(Map<String, dynamic> call) {
    final customerName = call['customer_name'] ?? 'Unknown Customer';
    final duration = call['duration'] ?? 0;
    final status = call['status'] ?? 'unknown';
    final createdAt = call['created_at'] ?? '';
    
    IconData statusIcon;
    Color statusColor;
    
    switch (status) {
      case 'completed':
        statusIcon = Icons.call;
        statusColor = Colors.green;
        break;
      case 'missed':
        statusIcon = Icons.call_missed;
        statusColor = Colors.red;
        break;
      case 'rejected':
        statusIcon = Icons.call_end;
        statusColor = Colors.orange;
        break;
      default:
        statusIcon = Icons.call;
        statusColor = Colors.grey;
    }
    
    return ListTile(
      leading: Icon(statusIcon, color: statusColor),
      title: Text(customerName),
      subtitle: Text(
        duration > 0 
            ? '${(duration / 60).floor()}:${(duration % 60).toString().padLeft(2, '0')}'
            : 'No duration',
      ),
      trailing: Text(
        _formatTime(createdAt),
        style: const TextStyle(fontSize: 12, color: Colors.grey),
      ),
    );
  }

  String _formatTime(String timestamp) {
    try {
      final dateTime = DateTime.parse(timestamp);
      final now = DateTime.now();
      final difference = now.difference(dateTime);
      
      if (difference.inDays > 0) {
        return '${difference.inDays}d ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours}h ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}m ago';
      } else {
        return 'Just now';
      }
    } catch (e) {
      return '';
    }
  }
}
