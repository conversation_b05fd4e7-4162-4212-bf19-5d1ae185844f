name: fuodz
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.7.70+86

environment:
  sdk: ^3.7.2

dependencies:
  adaptive_theme: 3.3.0
  animated_bottom_navigation_bar: ^1.2.0
  awesome_notifications: ^0.10.1
  banner_carousel: ^1.2.1
  basic_utils: ^3.9.0
  cached_network_image: ^3.3.1
  carousel_slider: ^5.0.0
  chewie: ^1.5.0
  cloud_firestore: ^4.14.0
  contained_tab_bar_view: ^0.8.0
  country_picker: ^2.0.26
  cupertino_icons: ^1.0.5
  currency_formatter: ^2.0.1
  custom_clippers: ^2.0.0
  custom_faqs:
    git:
      url: https://github.com/ambrosethebuild/custom_faqs
      ref: main
  custom_sliding_segmented_control: 1.7.5
  dartx: ^1.1.0
  device_info_plus: ^9.1.2
  dio: ^5.8.0+1
  dio_http_cache_lts: ^0.4.2
  dotted_border: ^2.0.0+2
  dotted_line: ^3.1.0
  double_back_to_close: ^2.0.0
  dynamic_height_grid_view: ^0.0.3
  easy_refresh: ^3.4.0
  equatable: ^2.0.3
  eva_icons_flutter: ^3.1.0
  expand_widget: ^2.1.0
  firebase_auth: 5.3.4
  firebase_crashlytics: ^4.3.5
  firebase_dynamic_links: ^6.0.7
  firebase_messaging: ^15.2.5
  firestore_chat: ^0.1.2
  flag: ^7.0.0
  flutter:
    sdk: flutter
  flutter_dropdown_alert: ^1.0.7
  flutter_facebook_auth: ^7.1.1
  flutter_form_builder: ^10.0.1
  flutter_icons:
    git:
      url: https://github.com/imclerran/flutter-icons.git
      ref: dart-3
  flutter_inappwebview: ^6.1.5
  flutter_localizations:
    sdk: flutter
  flutter_luban: ^0.1.15
  flutter_overboard: ^3.1.1
  flutter_polyline_points: ^1.0.0
  flutter_rating_bar: ^4.0.1
  flutter_signin_button: ^2.0.0
  flutter_staggered_animations: ^1.0.0
  flutter_svg: ^2.0.9
  flutter_typeahead: ^5.2.0
  form_builder_extra_fields: ^12.0.0
  geohash_plus: ^1.1.1
  glass: ^1.0.1+1
  glass_kit: 3.0.0
  google_fonts: ^6.2.1
  google_maps_flutter: ^2.12.1
  google_nav_bar: ^5.0.6
  google_places_flutter: ^2.1.0
  google_sign_in: ^5.3.3
  group_radio_button: 1.2.0
  http: ^1.0.0
  hugeicons: ^0.0.7
  image_picker: ^1.1.2
  in_app_review: ^2.0.10
  inspection: ^0.0.13
  jiffy: ^6.4.3
  keyboard_dismisser: ^3.0.0
  laravel_echo_null: ^0.1.1
  localize_and_translate: ^4.1.1
  location: ^8.0.0
  map_launcher: ^3.5.0
  map_location_picker: ^1.3.4
  maps_toolkit: ^3.1.0
  masonry_grid: ^1.0.0
  measure_size: ^3.0.1
  permission_handler: ^11.0.1
  pin_code_fields: ^8.0.1
  pinch_zoom: ^1.0.0
  pretty_dio_logger: ^1.2.0-beta-1
  pull_to_refresh_flutter3: ^2.0.2
  pusher_client_socket: ^0.0.5
  qr_code_scanner_plus: ^2.0.10+1
  qr_flutter: ^4.0.0
  quickalert: ^1.1.0
  rocket_singleton: ^0.0.4
  rx_shared_preferences: ^3.0.0
  rxdart: ^0.27.7
  share_plus: ^11.0.0
  shared_preferences: ^2.3.2
  sign_in_with_apple: ^7.0.1
  singleton: ^0.0.3-nullsafety
  slide_countdown: ^0.2.8
  sliding_up_panel: ^2.0.0+1
  sliver_snap: ^1.2.0
  smooth_page_indicator: ^1.0.0+2
  stacked: 3.4.0
  sticky_headers: ^0.3.0+2
  supercharged: ^2.1.1
  synchronized: ^3.0.1
  timelines_plus: ^1.0.4
  upgrader: ^11.3.1
  url_launcher: ^6.3.0
  velocity_x: ^4.3.1
  what3words: ^3.1.0

  # WebRTC Core
  flutter_webrtc: 0.9.48

  # WebSocket Communication
  socket_io_client: ^3.1.2

  # Audio/Video Controls
  wakelock_plus: ^1.1.1

  # Logging
  logger: ^2.0.2+1

  # Utilities
  uuid: ^4.1.0

  # Local Notifications
  flutter_local_notifications: ^17.2.2

dependency_overrides:
  archive: ^3.6.1
  cloud_firestore: ^5.4.2
  flutter_widget_from_html: ^0.10.1
  firebase_core: ^3.5.0
  geolocator: ^14.0.0
  intl: ^0.18.1
  http: ^0.13.0
  # win32: ^5.0.0
  # win32: ^5.5.4
  package_info_plus: ^8.3.0
  webview_flutter_android: ^4.4.2
  url_launcher_ios: ^6.3.1
  flutter_inappwebview_ios:
    git:
      url: https://github.com/andychucs/flutter_inappwebview.git
      ref: master
      path: flutter_inappwebview_ios

dev_dependencies:
  flutter_application_id: ^2.0.0-dev
  flutter_launcher_icons: ^0.14.3
  flutter_native_splash: ^2.4.6
  flutter_test:
    sdk: flutter
  spider: ^4.2.2
  translator: ^0.1.7

flutter_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/app_icon.png"
  adaptive_icon_foreground: "assets/images/app_icon.png"
  adaptive_icon_background: "#5e17eb"

flutter:
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/anim/
    - assets/images/icons/
    - assets/json/
    - assets/lang/
    - assets/
