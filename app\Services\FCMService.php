<?php

namespace App\Services;

use App\Traits\FirebaseMessagingTrait;
use App\Traits\FirebaseNotificationValidateTrait;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;
use Kreait\Firebase\Messaging\AndroidConfig;
use Kreait\Firebase\Messaging\ApnsConfig;
use Exception;

class FCMService
{
    use FirebaseMessagingTrait, FirebaseNotificationValidateTrait;

    /**
     * Send enhanced FCM notification with platform-specific configurations
     */
    public function sendEnhancedNotification(
        array $tokens,
        string $title,
        string $body,
        array $data = [],
        array $androidConfig = [],
        array $iosConfig = []
    ): bool {
        try {
            if (empty($tokens)) {
                Log::warning('No FCM tokens provided for notification');
                return false;
            }

            // Skip in local environment unless explicitly enabled
            if (app()->environment('local') && !config('fcm.send_in_local', false)) {
                Log::info('FCM notification skipped in local environment', [
                    'title' => $title,
                    'body' => $body
                ]);
                return true;
            }

            // Check if notification was already sent (using existing validation)
            if ($this->validateTokenNotification($tokens, $title, $body, $data)) {
                Log::info('FCM notification already sent, skipping duplicate');
                return true;
            }

            $messaging = $this->getFirebaseMessaging();
            $messages = [];

            foreach ($tokens as $token) {
                $message = $this->buildCloudMessage($token, $title, $body, $data, $androidConfig, $iosConfig);
                $messages[] = $message;
            }

            // Send messages in batches
            $batchSize = 500; // FCM limit
            $batches = array_chunk($messages, $batchSize);
            $totalSent = 0;
            $totalFailed = 0;

            foreach ($batches as $batch) {
                try {
                    $response = $messaging->sendAll($batch);
                    $totalSent += $response->successes()->count();
                    $totalFailed += $response->failures()->count();

                    // Log failed tokens for cleanup
                    foreach ($response->failures() as $failure) {
                        Log::warning('FCM token failed', [
                            'token' => $failure->target()->value(),
                            'error' => $failure->error()->getMessage()
                        ]);
                    }
                } catch (Exception $e) {
                    Log::error('FCM batch send failed', [
                        'batch_size' => count($batch),
                        'error' => $e->getMessage()
                    ]);
                    $totalFailed += count($batch);
                }
            }

            Log::info('FCM notification batch completed', [
                'title' => $title,
                'total_tokens' => count($tokens),
                'sent' => $totalSent,
                'failed' => $totalFailed
            ]);

            return $totalSent > 0;

        } catch (Exception $e) {
            Log::error('FCM notification failed', [
                'title' => $title,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Build CloudMessage with platform-specific configurations
     */
    private function buildCloudMessage(
        string $token,
        string $title,
        string $body,
        array $data = [],
        array $androidConfig = [],
        array $iosConfig = []
    ): CloudMessage {
        $message = CloudMessage::withTarget('token', $token)
            ->withNotification(Notification::create($title, $body));

        // Add data payload
        if (!empty($data)) {
            $message = $message->withData($data);
        }

        // Add Android-specific configuration
        if (!empty($androidConfig)) {
            $androidConfiguration = $this->buildAndroidConfig($androidConfig);
            $message = $message->withAndroidConfig($androidConfiguration);
        }

        // Add iOS-specific configuration
        if (!empty($iosConfig)) {
            $apnsConfiguration = $this->buildApnsConfig($iosConfig);
            $message = $message->withApnsConfig($apnsConfiguration);
        }

        return $message;
    }

    /**
     * Build Android-specific configuration
     */
    private function buildAndroidConfig(array $config): AndroidConfig
    {
        $androidConfig = AndroidConfig::fromArray([
            'priority' => $config['priority'] ?? 'high',
            'notification' => [
                'channel_id' => $config['channel_id'] ?? 'default',
                'sound' => $config['sound'] ?? 'default',
                'color' => $config['color'] ?? '#FF0000',
                'icon' => $config['icon'] ?? 'ic_notification',
                'tag' => $config['tag'] ?? null,
                'click_action' => $config['click_action'] ?? null,
                'body_loc_key' => $config['body_loc_key'] ?? null,
                'body_loc_args' => $config['body_loc_args'] ?? null,
                'title_loc_key' => $config['title_loc_key'] ?? null,
                'title_loc_args' => $config['title_loc_args'] ?? null,
            ]
        ]);

        return $androidConfig;
    }

    /**
     * Build APNS (iOS) configuration
     */
    private function buildApnsConfig(array $config): ApnsConfig
    {
        $payload = [
            'aps' => [
                'alert' => [
                    'title' => $config['title'] ?? null,
                    'body' => $config['body'] ?? null,
                ],
                'sound' => $config['sound'] ?? 'default',
                'badge' => $config['badge'] ?? null,
                'category' => $config['category'] ?? null,
                'thread-id' => $config['thread_id'] ?? null,
                'content-available' => $config['content_available'] ?? false,
                'mutable-content' => $config['mutable_content'] ?? false,
            ]
        ];

        // Add interruption level for iOS 15+
        if (isset($config['interruption_level'])) {
            $payload['aps']['interruption-level'] = $config['interruption_level'];
        }

        // Add relevance score for iOS 15+
        if (isset($config['relevance_score'])) {
            $payload['aps']['relevance-score'] = $config['relevance_score'];
        }

        return ApnsConfig::fromArray([
            'headers' => [
                'apns-priority' => $config['priority'] ?? '10',
                'apns-push-type' => $config['push_type'] ?? 'alert',
                'apns-expiration' => $config['expiration'] ?? '0',
                'apns-topic' => $config['topic'] ?? null,
                'apns-collapse-id' => $config['collapse_id'] ?? null,
            ],
            'payload' => $payload
        ]);
    }

    /**
     * Send call notification with call-specific configurations
     */
    public function sendCallNotification(
        array $tokens,
        string $title,
        string $body,
        array $data = [],
        bool $isIncomingCall = false
    ): bool {
        $androidConfig = [
            'channel_id' => $isIncomingCall ? 'incoming_call_channel' : 'call_status_channel',
            'priority' => 'high',
            'sound' => $isIncomingCall ? 'call_ringtone' : 'default',
            'color' => '#4CAF50',
            'icon' => $isIncomingCall ? 'ic_call' : 'ic_notification',
            'tag' => $data['call_id'] ?? null,
            'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
        ];

        $iosConfig = [
            'sound' => $isIncomingCall ? 'call_ringtone.caf' : 'default',
            'category' => $isIncomingCall ? 'CALL_CATEGORY' : 'DEFAULT',
            'thread_id' => $data['call_id'] ?? null,
            'interruption_level' => $isIncomingCall ? 'critical' : 'active',
            'content_available' => true,
            'priority' => '10'
        ];

        return $this->sendEnhancedNotification(
            $tokens,
            $title,
            $body,
            $data,
            $androidConfig,
            $iosConfig
        );
    }

    /**
     * Send silent data notification
     */
    public function sendSilentNotification(array $tokens, array $data): bool
    {
        try {
            if (empty($tokens)) {
                return false;
            }

            $messaging = $this->getFirebaseMessaging();
            $messages = [];

            foreach ($tokens as $token) {
                $message = CloudMessage::withTarget('token', $token)
                    ->withData($data)
                    ->withAndroidConfig(AndroidConfig::fromArray([
                        'priority' => 'high'
                    ]))
                    ->withApnsConfig(ApnsConfig::fromArray([
                        'headers' => ['apns-priority' => '5'],
                        'payload' => [
                            'aps' => [
                                'content-available' => true
                            ]
                        ]
                    ]));

                $messages[] = $message;
            }

            $response = $messaging->sendAll($messages);
            
            Log::info('Silent notification sent', [
                'tokens_count' => count($tokens),
                'sent' => $response->successes()->count(),
                'failed' => $response->failures()->count()
            ]);

            return $response->successes()->count() > 0;

        } catch (Exception $e) {
            Log::error('Silent notification failed', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Clean up invalid FCM tokens
     */
    public function cleanupInvalidTokens(array $invalidTokens): void
    {
        if (empty($invalidTokens)) {
            return;
        }

        try {
            // Remove invalid tokens from database
            \App\Models\UserToken::whereIn('token', $invalidTokens)->delete();
            
            Log::info('Cleaned up invalid FCM tokens', [
                'count' => count($invalidTokens)
            ]);
        } catch (Exception $e) {
            Log::error('Failed to cleanup invalid tokens', [
                'error' => $e->getMessage()
            ]);
        }
    }
}
