# Video Call Management System

This Laravel application provides a comprehensive video call management system with WebRTC signaling support for customer-vendor video calls.

## Features

- ✅ **Call Initiation**: Users can initiate video/audio calls
- ✅ **Call Management**: Accept, reject, end, and cancel calls
- ✅ **Real-time Events**: WebSocket broadcasting for call events
- ✅ **Call History**: Track and view past calls
- ✅ **Authentication**: Secure API with Laravel Sanctum
- ✅ **Authorization**: Role-based permissions for call actions
- ✅ **WebRTC Support**: Store and manage WebRTC signaling data
- ✅ **Call Quality Metrics**: Track call performance data
- ✅ **Notifications**: Push notifications for call events
- ✅ **Auto Cleanup**: Automatic cleanup of old call records

## Installation & Setup

### 1. Run Database Migration

```bash
php artisan migrate
```

### 2. Seed Sample Data (Optional)

```bash
php artisan db:seed --class=VideoCallSeeder
```

### 3. Configure Broadcasting (Required for Real-time Features)

Update your `.env` file:

```env
BROADCAST_DRIVER=pusher
# OR use Laravel Reverb for Laravel 11+
BROADCAST_DRIVER=reverb

# If using Pusher
PUSHER_APP_ID=your-app-id
PUSHER_APP_KEY=your-app-key
PUSHER_APP_SECRET=your-app-secret
PUSHER_APP_CLUSTER=your-cluster

# If using Laravel Reverb
REVERB_APP_ID=your-app-id
REVERB_APP_KEY=your-app-key
REVERB_APP_SECRET=your-app-secret
```

### 4. Start Broadcasting Server

For Laravel Reverb:
```bash
php artisan reverb:start
```

For Pusher: No additional setup needed (cloud service)

### 5. Configure Queue Worker (For Notifications)

```bash
php artisan queue:work
```

## API Endpoints

All endpoints require authentication via Laravel Sanctum.

### Base URL: `/api/calls`

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/initiate` | Initiate a new call |
| POST | `/{call_id}/accept` | Accept a pending call |
| POST | `/{call_id}/reject` | Reject a pending call |
| POST | `/{call_id}/end` | End an active call |
| POST | `/{call_id}/cancel` | Cancel a pending call |
| GET | `/{call_id}/status` | Get call status |
| GET | `/history` | Get call history |
| GET | `/active` | Get active calls |

## Usage Examples

### 1. Initiate a Call

```bash
curl -X POST /api/calls/initiate \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "receiver_id": 123,
    "call_type": "video",
    "room_id": "room_abc123"
  }'
```

### 2. Accept a Call

```bash
curl -X POST /api/calls/{call_id}/accept \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "webrtc_data": {
      "answer": "...",
      "ice_candidates": []
    }
  }'
```

### 3. End a Call

```bash
curl -X POST /api/calls/{call_id}/end \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "reason": "Call completed",
    "call_quality_metrics": {
      "bandwidth": 1024,
      "latency": 50,
      "packet_loss": 0.5,
      "audio_quality": 5,
      "video_quality": 4
    }
  }'
```

## Real-time Events

The system broadcasts the following events:

### 1. Call Initiated
- **Channel**: `user.{receiver_id}`
- **Event**: `call.initiated`
- **Triggered**: When a new call is initiated

### 2. Call Status Changed
- **Channel**: `user.{caller_id}` and `user.{receiver_id}`
- **Event**: `call.status.changed`
- **Triggered**: When call status changes (accepted, rejected, ended, etc.)

## Frontend Integration

### JavaScript Example with Laravel Echo

```javascript
import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Pusher = Pusher;

window.Echo = new Echo({
    broadcaster: 'pusher',
    key: process.env.MIX_PUSHER_APP_KEY,
    cluster: process.env.MIX_PUSHER_APP_CLUSTER,
    forceTLS: true
});

// Listen for incoming calls
Echo.private(`user.${userId}`)
    .listen('call.initiated', (e) => {
        console.log('Incoming call from:', e.caller.name);
        // Show incoming call UI
        showIncomingCallModal(e);
    })
    .listen('call.status.changed', (e) => {
        console.log('Call status changed:', e.status);
        // Update call UI based on status
        updateCallStatus(e);
    });
```

## Maintenance Commands

### Clean Up Old Call Records

```bash
# Clean up calls older than 30 days (default)
php artisan calls:cleanup

# Clean up calls older than 7 days
php artisan calls:cleanup --days=7

# Mark pending calls as missed and clean up
php artisan calls:cleanup --mark-missed

# Dry run to see what would be deleted
php artisan calls:cleanup --dry-run
```

### Schedule Automatic Cleanup

Add to `app/Console/Kernel.php`:

```php
protected function schedule(Schedule $schedule)
{
    // Clean up old call records daily
    $schedule->command('calls:cleanup --mark-missed')
             ->daily()
             ->at('02:00');
}
```

## Testing

Run the test suite:

```bash
php artisan test --filter VideoCallApiTest
```

## Security Considerations

1. **Authentication**: All endpoints require valid Sanctum tokens
2. **Authorization**: Users can only manage calls they participate in
3. **Rate Limiting**: Consider adding rate limiting for call initiation
4. **Input Validation**: All inputs are validated using Form Requests
5. **Active User Check**: Only active users can initiate calls

## Database Schema

The `video_calls` table includes:

- `call_id`: Unique identifier for the call
- `caller_id` & `receiver_id`: User IDs of participants
- `status`: Current call status (pending, accepted, rejected, ended, etc.)
- `call_type`: Audio or video call
- `webrtc_data`: JSON field for WebRTC signaling data
- `room_id`: WebRTC room identifier
- `duration`: Call duration in seconds
- `call_quality_metrics`: JSON field for call quality data
- Timestamps for initiated, accepted, and ended times

## Troubleshooting

### Common Issues

1. **Broadcasting not working**: Ensure your broadcasting driver is properly configured
2. **Notifications not sent**: Make sure queue workers are running
3. **Authorization errors**: Check user roles and permissions
4. **WebRTC connection issues**: Verify STUN/TURN server configuration

### Debug Mode

Enable debug logging in `.env`:

```env
LOG_LEVEL=debug
```

Check logs for detailed error information:

```bash
tail -f storage/logs/laravel.log
```

## Contributing

1. Follow PSR-12 coding standards
2. Write tests for new features
3. Update documentation for API changes
4. Use proper commit messages

## License

This video call management system is part of your Laravel application and follows the same license terms.
