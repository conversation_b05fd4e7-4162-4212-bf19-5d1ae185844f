[{"family": "MaterialIcons", "fonts": [{"asset": "fonts/MaterialIcons-Regular.otf"}]}, {"family": "packages/flutter_icons/Ionicons", "fonts": [{"asset": "packages/flutter_icons/fonts/Ionicons.ttf"}]}, {"family": "packages/flutter_icons/AntDesign", "fonts": [{"asset": "packages/flutter_icons/fonts/AntDesign.ttf"}]}, {"family": "packages/flutter_icons/FontAwesome", "fonts": [{"asset": "packages/flutter_icons/fonts/FontAwesome.ttf"}]}, {"family": "packages/flutter_icons/MaterialIcons", "fonts": [{"asset": "packages/flutter_icons/fonts/MaterialIcons.ttf"}]}, {"family": "packages/flutter_icons/Entypo", "fonts": [{"asset": "packages/flutter_icons/fonts/Entypo.ttf"}]}, {"family": "packages/flutter_icons/EvilIcons", "fonts": [{"asset": "packages/flutter_icons/fonts/EvilIcons.ttf"}]}, {"family": "packages/flutter_icons/Feather", "fonts": [{"asset": "packages/flutter_icons/fonts/Feather.ttf"}]}, {"family": "packages/flutter_icons/Foundation", "fonts": [{"asset": "packages/flutter_icons/fonts/Foundation.ttf"}]}, {"family": "packages/flutter_icons/MaterialCommunityIcons", "fonts": [{"asset": "packages/flutter_icons/fonts/MaterialCommunityIcons.ttf"}]}, {"family": "packages/flutter_icons/Octicons", "fonts": [{"asset": "packages/flutter_icons/fonts/Octicons.ttf"}]}, {"family": "packages/flutter_icons/SimpleLineIcons", "fonts": [{"asset": "packages/flutter_icons/fonts/SimpleLineIcons.ttf"}]}, {"family": "packages/flutter_icons/Zocial", "fonts": [{"asset": "packages/flutter_icons/fonts/Zocial.ttf"}]}, {"family": "packages/flutter_icons/FontAwesome5", "fonts": [{"asset": "packages/flutter_icons/fonts/FontAwesome5_Regular.ttf"}]}, {"family": "packages/flutter_icons/FontAwesome5_Brands", "fonts": [{"asset": "packages/flutter_icons/fonts/FontAwesome5_Brands.ttf"}]}, {"family": "packages/flutter_icons/FontAwesome5_Solid", "fonts": [{"asset": "packages/flutter_icons/fonts/FontAwesome5_Solid.ttf"}]}, {"family": "packages/flutter_icons/WeatherIcons", "fonts": [{"asset": "packages/flutter_icons/fonts/weathericons.ttf"}]}, {"family": "packages/cupertino_icons/CupertinoIcons", "fonts": [{"asset": "packages/cupertino_icons/assets/CupertinoIcons.ttf"}]}, {"family": "packages/eva_icons_flutter/EvaIcons", "fonts": [{"asset": "packages/eva_icons_flutter/lib/fonts/Eva-Icons.ttf"}]}, {"family": "packages/font_awesome_flutter/FontAwesomeBrands", "fonts": [{"weight": 400, "asset": "packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf"}]}, {"family": "packages/font_awesome_flutter/FontAwesomeRegular", "fonts": [{"weight": 400, "asset": "packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf"}]}, {"family": "packages/font_awesome_flutter/FontAwesomeSolid", "fonts": [{"weight": 900, "asset": "packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf"}]}, {"family": "packages/hugeicons/HgiStrokeRounded", "fonts": [{"asset": "packages/hugeicons/lib/fonts/hugeicons-stroke-rounded.ttf"}]}, {"family": "packages/ionicons/Ionicons", "fonts": [{"asset": "packages/ionicons/assets/fonts/Ionicons.ttf"}]}, {"family": "packages/line_icons/Awesome Line Icons 1.3.0", "fonts": [{"asset": "packages/line_icons/lib/assets/fonts/LineIcons.ttf"}]}]