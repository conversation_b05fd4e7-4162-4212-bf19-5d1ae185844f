name: fuodz
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.7.70+76

environment:
  sdk: ^3.7.2

dependencies:
  adaptive_theme: 3.3.0
  args: ^2.3.0
  assets_audio_player_plus: ^3.1.2
  awesome_notifications: ^0.10.1
  basic_utils: ^3.9.0
  bg_launcher: ^0.1.0
  cached_network_image: ^3.3.1
  cloud_firestore: ^4.14.0
  contained_tab_bar_view: ^0.8.0
  country_picker: ^2.0.26
  cupertino_icons: ^1.0.5
  currency_formatter: ^2.0.1
  custom_clippers: ^2.0.0
  custom_faqs:
    git:
      url: https://github.com/ambrosethebuild/custom_faqs
      ref: main
  dartx: ^1.1.0
  device_info_plus: ^9.1.2
  dio: ^4.0.6
  dio_http_cache_lts: ^0.4.0
  dotted_border: ^2.0.0+2
  dotted_line: ^3.1.0
  double_back_to_close: ^2.0.0
  eva_icons_flutter: ^3.1.0
  file_sizes: ^1.0.6
  firebase_auth: 5.3.4
  firebase_crashlytics: ^4.3.5
  firebase_messaging: ^15.2.5
  firestore_chat: ^0.1.2
  fl_chart: ^0.71.0
  flag: ^7.0.0
  flutter:
    sdk: flutter
  flutter_dropdown_alert: ^1.0.7
  flutter_esc_pos_utils: ^1.0.1
  flutter_form_builder: 9.3.0
  flutter_icons:
    git:
      url: https://github.com/imclerran/flutter-icons.git
      ref: dart-3
  flutter_inappwebview: ^6.1.5
  flutter_lifecycle_detector: ^0.0.8
  flutter_localizations:
    sdk: flutter
  flutter_luban: ^0.1.15
  flutter_overboard: ^3.1.1
  flutter_staggered_animations: ^1.0.0
  flutter_svg: ^2.1.0
  flutter_typeahead: ^5.2.0
  google_fonts: ^6.2.1
  google_nav_bar: ^5.0.3
  image_picker: ^1.1.2
  in_app_review: ^2.0.10
  inspection: ^0.0.13
  intl: ^0.18.1
  jiffy: ^6.4.3
  kat_html_editor: ^0.0.5
  laravel_echo_null: ^0.1.1
  line_icons: ^2.0.1
  localize_and_translate: ^4.1.1
  map_launcher: ^3.5.0
  masonry_grid: ^1.0.0
  numeral: ^2.0.1
  open_file: ^3.5.10
  package_info_plus: ^4.0.2
  path_provider: ^2.1.2
  pdf: ^3.11.0

  pin_code_fields: ^8.0.1
  pinch_zoom: ^1.0.0
  pretty_dio_logger: ^1.1.1
  print_bluetooth_thermal: ^1.1.4
  pull_to_refresh_flutter3: ^2.0.1
  pusher_client_socket: ^0.0.5
  qr_code_scanner_plus: ^2.0.10+1
  quickalert: ^1.1.0
  random_string: ^2.3.1
  rx_shared_preferences: ^3.0.0
  screenshot: ^3.0.0
  share_plus: ^10.0.2
  shared_preferences: ^2.3.2
  sim_card_code: ^0.0.3
  singleton: ^0.0.3-nullsafety
  stacked: ^3.4.3
  supercharged: ^2.1.1
  syncfusion_flutter_datagrid: ^27.1.48
  upgrader: ^11.1.0
  url_launcher: ^6.3.0
  velocity_x: ^4.3.1

  # WebRTC Core
  flutter_webrtc: ^0.9.48

  # WebSocket Communication
  socket_io_client: ^3.1.2

  # Permissions
  permission_handler: ^11.0.1

  # Audio/Video Controls
  wakelock_plus: ^1.1.1

  # Logging
  logger: ^2.5.0

dependency_overrides:
  cloud_firestore: ^5.4.2
  # flutter_widget_from_html: ^0.10.1
  firebase_core: ^3.5.0
  file_picker: ^10.1.2
  intl: ^0.18.1
  http: ^0.13.0
  # win32: ^5.0.0
  flutter_widget_from_html_core: ^0.15.2
  webview_flutter_android: ^4.4.2
  url_launcher_ios: ^6.3.1
  flutter_inappwebview_ios:
    git:
      url: https://github.com/andychucs/flutter_inappwebview.git
      ref: master
      path: flutter_inappwebview_ios
  uuid: ^4.3.3

dev_dependencies:
  flutter_application_id: ^2.0.0-dev
  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.2.4
  flutter_test:
    sdk: flutter
  translator: ^0.1.7

flutter_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/app_icon.png"
  adaptive_icon_foreground: "assets/images/app_icon.png"
  adaptive_icon_background: "#5e17eb"

flutter:
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/audio/
    - assets/lang/
