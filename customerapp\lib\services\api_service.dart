import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  final Logger _logger = Logger();
  final Dio _dio = Dio();
  
  String? _baseUrl;
  String? _authToken;

  /// Initialize API service
  void initialize({
    required String baseUrl,
    Duration? connectTimeout,
    Duration? receiveTimeout,
  }) {
    _baseUrl = baseUrl;
    
    _dio.options = BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: connectTimeout ?? const Duration(seconds: 10),
      receiveTimeout: receiveTimeout ?? const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    );

    // Add interceptors
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          if (_authToken != null) {
            options.headers['Authorization'] = 'Bearer $_authToken';
          }
          _logger.d('Customer API Request: ${options.method} ${options.path}');
          handler.next(options);
        },
        onResponse: (response, handler) {
          _logger.d('Customer API Response: ${response.statusCode}');
          handler.next(response);
        },
        onError: (error, handler) {
          _logger.e('Customer API Error: ${error.message}');
          handler.next(error);
        },
      ),
    );
  }

  /// Set authentication token
  Future<void> setAuthToken(String token) async {
    _authToken = token;
  }

  /// Initiate a video call with vendor
  Future<Map<String, dynamic>> initiateCall({
    required String vendorId,
    String? callType = 'video',
  }) async {
    try {
      final response = await _dio.post('/calls/initiate', data: {
        'receiver_id': vendorId,
        'call_type': callType,
      });

      if (response.data['success'] == true) {
        _logger.i('Customer initiated call with vendor: $vendorId');
        return response.data['data'];
      } else {
        throw Exception(response.data['message'] ?? 'Failed to initiate call');
      }
    } on DioException catch (e) {
      _logger.e('Failed to initiate call: ${e.message}');
      throw _handleDioError(e);
    }
  }

  /// Accept incoming call from vendor
  Future<Map<String, dynamic>> acceptCall(String callId) async {
    try {
      final response = await _dio.post('/calls/$callId/accept');

      if (response.data['success'] == true) {
        _logger.i('Customer accepted call: $callId');
        return response.data['data'];
      } else {
        throw Exception(response.data['message'] ?? 'Failed to accept call');
      }
    } on DioException catch (e) {
      _logger.e('Failed to accept call: ${e.message}');
      throw _handleDioError(e);
    }
  }

  /// Reject incoming call from vendor
  Future<Map<String, dynamic>> rejectCall(String callId) async {
    try {
      final response = await _dio.post('/calls/$callId/reject');

      if (response.data['success'] == true) {
        _logger.i('Customer rejected call: $callId');
        return response.data['data'];
      } else {
        throw Exception(response.data['message'] ?? 'Failed to reject call');
      }
    } on DioException catch (e) {
      _logger.e('Failed to reject call: ${e.message}');
      throw _handleDioError(e);
    }
  }

  /// End active call
  Future<Map<String, dynamic>> endCall(String callId) async {
    try {
      final response = await _dio.post('/calls/$callId/end');

      if (response.data['success'] == true) {
        _logger.i('Customer ended call: $callId');
        return response.data['data'];
      } else {
        throw Exception(response.data['message'] ?? 'Failed to end call');
      }
    } on DioException catch (e) {
      _logger.e('Failed to end call: ${e.message}');
      throw _handleDioError(e);
    }
  }

  /// Get call details
  Future<Map<String, dynamic>> getCallDetails(String callId) async {
    try {
      final response = await _dio.get('/calls/$callId');

      if (response.data['success'] == true) {
        return response.data['data'];
      } else {
        throw Exception(response.data['message'] ?? 'Failed to get call details');
      }
    } on DioException catch (e) {
      _logger.e('Failed to get call details: ${e.message}');
      throw _handleDioError(e);
    }
  }

  /// Get call history
  Future<List<Map<String, dynamic>>> getCallHistory({
    int page = 1,
    int perPage = 20,
  }) async {
    try {
      final response = await _dio.get('/calls/history', queryParameters: {
        'page': page,
        'per_page': perPage,
      });

      if (response.data['success'] == true) {
        return List<Map<String, dynamic>>.from(response.data['data']['data']);
      } else {
        throw Exception(response.data['message'] ?? 'Failed to get call history');
      }
    } on DioException catch (e) {
      _logger.e('Failed to get call history: ${e.message}');
      throw _handleDioError(e);
    }
  }

  /// Get vendors list
  Future<List<Map<String, dynamic>>> getVendors({
    String? search,
    int page = 1,
    int perPage = 20,
  }) async {
    try {
      final response = await _dio.get('/vendors', queryParameters: {
        if (search != null) 'search': search,
        'page': page,
        'per_page': perPage,
      });

      if (response.data['success'] == true) {
        return List<Map<String, dynamic>>.from(response.data['data']['data']);
      } else {
        throw Exception(response.data['message'] ?? 'Failed to get vendors');
      }
    } on DioException catch (e) {
      _logger.e('Failed to get vendors: ${e.message}');
      throw _handleDioError(e);
    }
  }

  /// Handle Dio errors
  Exception _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return Exception('Connection timeout. Please check your internet connection.');
      case DioExceptionType.sendTimeout:
        return Exception('Request timeout. Please try again.');
      case DioExceptionType.receiveTimeout:
        return Exception('Server response timeout. Please try again.');
      case DioExceptionType.badResponse:
        final statusCode = error.response?.statusCode;
        final message = error.response?.data?['message'];
        
        if (statusCode == 401) {
          return Exception('Authentication failed. Please login again.');
        } else if (statusCode == 403) {
          return Exception('Access denied.');
        } else if (statusCode == 404) {
          return Exception('Resource not found.');
        } else if (statusCode == 422) {
          final errors = error.response?.data?['errors'];
          if (errors is Map) {
            final firstError = errors.values.first;
            if (firstError is List && firstError.isNotEmpty) {
              return Exception(firstError.first.toString());
            }
          }
          return Exception(message ?? 'Validation failed.');
        } else if (statusCode != null && statusCode >= 500) {
          return Exception('Server error. Please try again later.');
        }
        return Exception(message ?? 'Request failed.');
      case DioExceptionType.cancel:
        return Exception('Request cancelled.');
      case DioExceptionType.connectionError:
        return Exception('Connection error. Please check your internet connection.');
      default:
        return Exception('An unexpected error occurred.');
    }
  }

  // Getters
  String? get baseUrl => _baseUrl;
  bool get isAuthenticated => _authToken != null;
}
