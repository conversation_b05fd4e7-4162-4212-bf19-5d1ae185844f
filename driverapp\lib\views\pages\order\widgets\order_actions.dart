import 'package:flutter/material.dart';
// import 'package:flutter_icons/flutter_icons.dart';
// import 'package:fuodz/constants/app_colors.dart';
// import 'package:fuodz/constants/sizes.dart';
import 'package:fuodz/models/order.dart';
// import 'package:fuodz/utils/utils.dart';
import 'package:fuodz/widgets/busy_indicator.dart';
// import 'package:localize_and_translate/localize_and_translate.dart';
import 'package:velocity_x/velocity_x.dart';

class OrderActions extends StatefulWidget {
  const OrderActions({
    this.canChatCustomer = false,
    this.busy = false,
    required this.order,
    required this.processOrderCompletion,
    required this.processOrderEnroute,
    Key? key,
  }) : super(key: key);

  final bool canChatCustomer;
  final bool busy;
  final Order order;
  final Function processOrderCompletion;
  final Function processOrderEnroute;

  @override
  State<OrderActions> createState() => _OrderActionsState();
}

class _OrderActionsState extends State<OrderActions> {
  ObjectKey viewKey = new ObjectKey(DateTime.now());

  @override
  Widget build(BuildContext context) {
    return (!["failed", "delivered", "cancelled"].contains(widget.order.status))
        ? SafeArea(
              child:
                  widget.busy
                      ? BusyIndicator().centered().wh(Vx.dp40, Vx.dp40)
                      : Builder(
                        builder: (context) {
                          return "Ton do".text.make();
                          /*
                          bool isNotEnroute = widget.order.status != "enroute";
                          String actionText = "Long Press To Start".tr();
                          Function action = widget.processOrderEnroute;
                          Color textColor = Utils.textColorByTheme();
                          if (!isNotEnroute) {
                            action = widget.processOrderCompletion;
                            actionText = "Long Press To Complete".tr();
                          }

                          return SwipeButtonWidget(
                            key: viewKey,
                            acceptPoitTransition: 0.7,
                            margin: const EdgeInsets.all(0),
                            padding: const EdgeInsets.all(0),
                            boxShadow: [],
                            borderRadius: BorderRadius.circular(0),
                            colorBeforeSwipe: AppColor.primaryColor,
                            colorAfterSwiped: AppColor.primaryColor,
                            height: 50,
                            childBeforeSwipe: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(0),
                                color: AppColor.primaryColor,
                              ),
                              width: 100,
                              height: double.infinity,
                              child: Center(
                                child: Icon(
                                  FlutterIcons.chevrons_right_fea,
                                  color: textColor,
                                  size: 34,
                                ),
                              ),
                            ),
                            childAfterSwiped: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(0),
                                color: AppColor.primaryColor,
                              ),
                              width: 70,
                              height: double.infinity,
                              child: Center(
                                child: Icon(
                                  FlutterIcons.check_ant,
                                  color: textColor,
                                ),
                              ),
                            ),
                            leftChildren: [
                              Align(
                                alignment: Alignment(0.9, 0),
                                child:
                                    actionText.text.bold
                                        .size(Sizes.fontSizeLarge)
                                        .color(textColor)
                                        .make(),
                              ),
                            ],
                            onHorizontalDragUpdate: (e) {},
                            onHorizontalDragRight: (e) async {
                              action();
                              return true;
                            },
                            onHorizontalDragleft: (e) async {
                              return false;
                            },
                          );
                          */
                        },
                      ),
            ).box.p20.outerShadow2Xl.shadow
            .color(context.cardColor)
            .make()
            .wFull(context)
        : 0.squareBox;
  }
}
