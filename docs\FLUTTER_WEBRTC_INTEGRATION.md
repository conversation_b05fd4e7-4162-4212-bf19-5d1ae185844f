# Flutter WebRTC Integration Guide

This guide shows how to integrate Flutter apps with the Laravel WebRTC signaling system for video calls.

## Architecture Overview

```
Flutter App A ←→ Laravel API ←→ Laravel Reverb ←→ Flutter App B
     ↓                                              ↑
     └────────── Direct WebRTC P2P Connection ─────┘
```

## Integration Approaches

### Approach 1: Hybrid Signaling (Recommended)

**Use Laravel API for:**
- Initial offer/answer exchange
- Fallback when WebSocket fails
- Debugging and persistence

**Use WebSocket for:**
- ICE candidate exchange
- Real-time connection state updates
- Low-latency signaling

### Approach 2: Direct WebSocket Signaling

**Use WebSocket only for:**
- All signaling data exchange
- Real-time communication
- Minimal Lara<PERSON> involvement

## Flutter Implementation

### 1. Dependencies

Add to `pubspec.yaml`:

```yaml
dependencies:
  flutter_webrtc: ^0.9.48
  laravel_echo_null: ^1.0.0
  pusher_client_socket: ^2.0.0
  http: ^1.1.0
  permission_handler: ^11.0.1
```

### 2. WebRTC Service

```dart
// services/webrtc_service.dart
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class WebRTCService {
  RTCPeerConnection? _peerConnection;
  MediaStream? _localStream;
  MediaStream? _remoteStream;
  String? _callId;
  
  // Callbacks
  Function(MediaStream)? onLocalStream;
  Function(MediaStream)? onRemoteStream;
  Function(RTCPeerConnectionState)? onConnectionStateChange;

  // ICE servers configuration
  final Map<String, dynamic> _iceServers = {
    'iceServers': [
      {'urls': 'stun:stun.l.google.com:19302'},
      // Add TURN servers if needed
    ]
  };

  Future<void> initializeWebRTC() async {
    // Create peer connection
    _peerConnection = await createPeerConnection(_iceServers);
    
    // Set up event handlers
    _peerConnection!.onIceCandidate = _onIceCandidate;
    _peerConnection!.onAddStream = _onAddStream;
    _peerConnection!.onConnectionState = _onConnectionStateChange;
    _peerConnection!.onIceConnectionState = _onIceConnectionStateChange;
  }

  Future<void> startCall(String callId) async {
    _callId = callId;
    
    // Get user media
    _localStream = await getUserMedia();
    _peerConnection!.addStream(_localStream!);
    
    if (onLocalStream != null) {
      onLocalStream!(_localStream!);
    }

    // Create and send offer
    RTCSessionDescription offer = await _peerConnection!.createOffer();
    await _peerConnection!.setLocalDescription(offer);
    
    // Send offer to Laravel API
    await _sendOfferToServer(offer);
  }

  Future<void> answerCall(String callId) async {
    _callId = callId;
    
    // Get user media
    _localStream = await getUserMedia();
    _peerConnection!.addStream(_localStream!);
    
    if (onLocalStream != null) {
      onLocalStream!(_localStream!);
    }

    // Get offer from server
    final offerData = await _getSignalingDataFromServer(callId);
    if (offerData['offer'] != null) {
      RTCSessionDescription offer = RTCSessionDescription(
        offerData['offer']['sdp'],
        offerData['offer']['type'],
      );
      
      await _peerConnection!.setRemoteDescription(offer);
      
      // Create and send answer
      RTCSessionDescription answer = await _peerConnection!.createAnswer();
      await _peerConnection!.setLocalDescription(answer);
      
      // Send answer to Laravel API
      await _sendAnswerToServer(answer);
    }
  }

  Future<MediaStream> getUserMedia() async {
    final Map<String, dynamic> constraints = {
      'audio': true,
      'video': {
        'facingMode': 'user',
        'width': {'ideal': 1280},
        'height': {'ideal': 720},
      }
    };

    return await navigator.mediaDevices.getUserMedia(constraints);
  }

  void _onIceCandidate(RTCIceCandidate candidate) {
    // Send ICE candidate via WebSocket (real-time)
    _sendIceCandidateViaWebSocket(candidate);
    
    // Fallback: Also store in Laravel API
    _sendIceCandidateToServer(candidate);
  }

  void _onAddStream(MediaStream stream) {
    _remoteStream = stream;
    if (onRemoteStream != null) {
      onRemoteStream!(stream);
    }
  }

  void _onConnectionStateChange(RTCPeerConnectionState state) {
    print('Connection state: $state');
    
    // Update connection state on server
    _updateConnectionStateOnServer(state);
    
    if (onConnectionStateChange != null) {
      onConnectionStateChange!(state);
    }
  }

  void _onIceConnectionStateChange(RTCIceConnectionState state) {
    print('ICE connection state: $state');
  }

  // API Communication Methods
  Future<void> _sendOfferToServer(RTCSessionDescription offer) async {
    final response = await http.post(
      Uri.parse('${ApiConfig.baseUrl}/calls/$_callId/signaling/offer'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${await AuthService.getToken()}',
      },
      body: jsonEncode({
        'type': offer.type,
        'sdp': offer.sdp,
      }),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to send offer: ${response.body}');
    }
  }

  Future<void> _sendAnswerToServer(RTCSessionDescription answer) async {
    final response = await http.post(
      Uri.parse('${ApiConfig.baseUrl}/calls/$_callId/signaling/answer'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${await AuthService.getToken()}',
      },
      body: jsonEncode({
        'type': answer.type,
        'sdp': answer.sdp,
      }),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to send answer: ${response.body}');
    }
  }

  Future<void> _sendIceCandidateToServer(RTCIceCandidate candidate) async {
    final response = await http.post(
      Uri.parse('${ApiConfig.baseUrl}/calls/$_callId/signaling/ice-candidate'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${await AuthService.getToken()}',
      },
      body: jsonEncode({
        'candidate': candidate.candidate,
        'sdpMid': candidate.sdpMid,
        'sdpMLineIndex': candidate.sdpMLineIndex,
      }),
    );

    if (response.statusCode != 200) {
      print('Failed to send ICE candidate: ${response.body}');
    }
  }

  Future<Map<String, dynamic>> _getSignalingDataFromServer(String callId) async {
    final response = await http.get(
      Uri.parse('${ApiConfig.baseUrl}/calls/$callId/signaling'),
      headers: {
        'Authorization': 'Bearer ${await AuthService.getToken()}',
      },
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['data'];
    } else {
      throw Exception('Failed to get signaling data: ${response.body}');
    }
  }

  Future<void> _updateConnectionStateOnServer(RTCPeerConnectionState state) async {
    final response = await http.post(
      Uri.parse('${ApiConfig.baseUrl}/calls/$_callId/signaling/connection-state'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${await AuthService.getToken()}',
      },
      body: jsonEncode({
        'connection_state': state.toString().split('.').last,
      }),
    );

    if (response.statusCode != 200) {
      print('Failed to update connection state: ${response.body}');
    }
  }

  // WebSocket Methods (using existing WebsocketService)
  void _sendIceCandidateViaWebSocket(RTCIceCandidate candidate) {
    // Use your existing WebsocketService to send ICE candidates
    WebsocketService().sendMessage('call.$_callId', 'webrtc.ice-candidate', {
      'candidate': candidate.candidate,
      'sdpMid': candidate.sdpMid,
      'sdpMLineIndex': candidate.sdpMLineIndex,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  void handleWebSocketSignaling(Map<String, dynamic> data) async {
    switch (data['type']) {
      case 'offer':
        await _handleRemoteOffer(data['data']);
        break;
      case 'answer':
        await _handleRemoteAnswer(data['data']);
        break;
      case 'ice-candidate':
        await _handleRemoteIceCandidate(data['data']);
        break;
    }
  }

  Future<void> _handleRemoteOffer(Map<String, dynamic> offerData) async {
    RTCSessionDescription offer = RTCSessionDescription(
      offerData['sdp'],
      offerData['type'],
    );
    await _peerConnection!.setRemoteDescription(offer);
  }

  Future<void> _handleRemoteAnswer(Map<String, dynamic> answerData) async {
    RTCSessionDescription answer = RTCSessionDescription(
      answerData['sdp'],
      answerData['type'],
    );
    await _peerConnection!.setRemoteDescription(answer);
  }

  Future<void> _handleRemoteIceCandidate(Map<String, dynamic> candidateData) async {
    RTCIceCandidate candidate = RTCIceCandidate(
      candidateData['candidate'],
      candidateData['sdpMid'],
      candidateData['sdpMLineIndex'],
    );
    await _peerConnection!.addCandidate(candidate);
  }

  Future<void> endCall() async {
    _localStream?.dispose();
    _remoteStream?.dispose();
    await _peerConnection?.close();
    _peerConnection = null;
    _callId = null;
  }

  void dispose() {
    endCall();
  }
}
```

### 3. Video Call Screen

```dart
// screens/video_call_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';

class VideoCallScreen extends StatefulWidget {
  final String callId;
  final bool isIncoming;

  const VideoCallScreen({
    Key? key,
    required this.callId,
    required this.isIncoming,
  }) : super(key: key);

  @override
  _VideoCallScreenState createState() => _VideoCallScreenState();
}

class _VideoCallScreenState extends State<VideoCallScreen> {
  final WebRTCService _webrtcService = WebRTCService();
  final RTCVideoRenderer _localRenderer = RTCVideoRenderer();
  final RTCVideoRenderer _remoteRenderer = RTCVideoRenderer();
  
  bool _isConnected = false;
  bool _isMuted = false;
  bool _isVideoEnabled = true;

  @override
  void initState() {
    super.initState();
    _initializeCall();
  }

  Future<void> _initializeCall() async {
    await _localRenderer.initialize();
    await _remoteRenderer.initialize();
    
    await _webrtcService.initializeWebRTC();
    
    _webrtcService.onLocalStream = (stream) {
      _localRenderer.srcObject = stream;
      setState(() {});
    };
    
    _webrtcService.onRemoteStream = (stream) {
      _remoteRenderer.srcObject = stream;
      setState(() {});
    };
    
    _webrtcService.onConnectionStateChange = (state) {
      setState(() {
        _isConnected = state == RTCPeerConnectionState.RTCPeerConnectionStateConnected;
      });
    };

    // Setup WebSocket listener for signaling
    _setupWebSocketListener();

    if (widget.isIncoming) {
      await _webrtcService.answerCall(widget.callId);
    } else {
      await _webrtcService.startCall(widget.callId);
    }
  }

  void _setupWebSocketListener() {
    WebsocketService().listenToChannel('call.${widget.callId}', (data) {
      if (data['type']?.startsWith('webrtc.') == true) {
        _webrtcService.handleWebSocketSignaling(data);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Remote video (full screen)
          Positioned.fill(
            child: _remoteRenderer.srcObject != null
                ? RTCVideoView(_remoteRenderer, mirror: false)
                : Container(
                    color: Colors.black,
                    child: Center(
                      child: Text(
                        _isConnected ? 'Connected' : 'Connecting...',
                        style: TextStyle(color: Colors.white, fontSize: 18),
                      ),
                    ),
                  ),
          ),
          
          // Local video (picture-in-picture)
          Positioned(
            top: 50,
            right: 20,
            width: 120,
            height: 160,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: _localRenderer.srcObject != null
                  ? RTCVideoView(_localRenderer, mirror: true)
                  : Container(color: Colors.grey),
            ),
          ),
          
          // Call controls
          Positioned(
            bottom: 50,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildControlButton(
                  icon: _isMuted ? Icons.mic_off : Icons.mic,
                  onPressed: _toggleMute,
                  backgroundColor: _isMuted ? Colors.red : Colors.grey,
                ),
                _buildControlButton(
                  icon: Icons.call_end,
                  onPressed: _endCall,
                  backgroundColor: Colors.red,
                ),
                _buildControlButton(
                  icon: _isVideoEnabled ? Icons.videocam : Icons.videocam_off,
                  onPressed: _toggleVideo,
                  backgroundColor: _isVideoEnabled ? Colors.grey : Colors.red,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required Color backgroundColor,
  }) {
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        color: backgroundColor,
        shape: BoxShape.circle,
      ),
      child: IconButton(
        icon: Icon(icon, color: Colors.white),
        onPressed: onPressed,
      ),
    );
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
    });
    // Implement mute functionality
  }

  void _toggleVideo() {
    setState(() {
      _isVideoEnabled = !_isVideoEnabled;
    });
    // Implement video toggle functionality
  }

  void _endCall() async {
    await _webrtcService.endCall();
    
    // Call Laravel API to end the call
    await CallService.endCall(widget.callId);
    
    Navigator.of(context).pop();
  }

  @override
  void dispose() {
    _localRenderer.dispose();
    _remoteRenderer.dispose();
    _webrtcService.dispose();
    super.dispose();
  }
}
```

### 4. Integration with Existing WebSocket Service

Update your existing `WebsocketService` to handle WebRTC signaling:

```dart
// Add to existing WebsocketService
void listenToWebRTCSignaling(String callId, Function(Map<String, dynamic>) onSignalingData) {
  privateChannel = echoClient?.private('call.$callId');
  
  privateChannel?.listen('webrtc.offer', (data) {
    onSignalingData({'type': 'offer', 'data': data});
  });
  
  privateChannel?.listen('webrtc.answer', (data) {
    onSignalingData({'type': 'answer', 'data': data});
  });
  
  privateChannel?.listen('webrtc.ice-candidate', (data) {
    onSignalingData({'type': 'ice-candidate', 'data': data});
  });
}

void sendMessage(String channel, String event, Map<String, dynamic> data) {
  // Implementation depends on your WebSocket setup
  // This would send data via the WebSocket connection
}
```

## Configuration

### Environment Variables

Add to your Flutter app configuration:

```dart
// config/api_config.dart
class ApiConfig {
  static const String baseUrl = 'https://your-api.com/api';
  static const String wsHost = 'your-websocket-host.com';
  static const String wsPort = '443';
  static const String wsAppKey = 'your-reverb-app-key';
}
```

## Error Handling and Fallbacks

### 1. WebSocket Fallback

```dart
class SignalingManager {
  bool _useWebSocket = true;
  
  Future<void> sendSignalingData(String type, Map<String, dynamic> data) async {
    if (_useWebSocket) {
      try {
        await _sendViaWebSocket(type, data);
      } catch (e) {
        print('WebSocket failed, falling back to HTTP: $e');
        _useWebSocket = false;
        await _sendViaHTTP(type, data);
      }
    } else {
      await _sendViaHTTP(type, data);
    }
  }
  
  Future<void> _sendViaWebSocket(String type, Map<String, dynamic> data) async {
    // Send via WebSocket
  }
  
  Future<void> _sendViaHTTP(String type, Map<String, dynamic> data) async {
    // Send via Laravel API
  }
}
```

### 2. Connection Recovery

```dart
void _handleConnectionFailure() {
  // Attempt to reconnect
  Timer.periodic(Duration(seconds: 5), (timer) async {
    try {
      await _webrtcService.initializeWebRTC();
      timer.cancel();
    } catch (e) {
      print('Reconnection attempt failed: $e');
    }
  });
}
```

This integration provides a robust WebRTC implementation that leverages both Laravel's persistence capabilities and WebSocket's real-time features for optimal performance and reliability.
