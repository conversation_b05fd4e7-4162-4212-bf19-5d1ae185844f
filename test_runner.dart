#!/usr/bin/env dart

import 'dart:io';
import 'dart:convert';

/// Test runner script for video call functionality
/// 
/// Usage:
/// dart test_runner.dart [options]
/// 
/// Options:
/// --unit          Run only unit tests
/// --integration   Run only integration tests
/// --widget        Run only widget tests
/// --coverage      Generate coverage report
/// --verbose       Verbose output
/// --help          Show this help message

void main(List<String> arguments) async {
  final config = TestRunnerConfig.fromArguments(arguments);
  
  if (config.showHelp) {
    _printHelp();
    return;
  }

  final runner = TestRunner(config);
  await runner.run();
}

class TestRunnerConfig {
  final bool runUnit;
  final bool runIntegration;
  final bool runWidget;
  final bool generateCoverage;
  final bool verbose;
  final bool showHelp;

  TestRunnerConfig({
    this.runUnit = true,
    this.runIntegration = true,
    this.runWidget = true,
    this.generateCoverage = false,
    this.verbose = false,
    this.showHelp = false,
  });

  factory TestRunnerConfig.fromArguments(List<String> arguments) {
    var runUnit = false;
    var runIntegration = false;
    var runWidget = false;
    var generateCoverage = false;
    var verbose = false;
    var showHelp = false;

    for (final arg in arguments) {
      switch (arg) {
        case '--unit':
          runUnit = true;
          break;
        case '--integration':
          runIntegration = true;
          break;
        case '--widget':
          runWidget = true;
          break;
        case '--coverage':
          generateCoverage = true;
          break;
        case '--verbose':
          verbose = true;
          break;
        case '--help':
          showHelp = true;
          break;
      }
    }

    // If no specific test type is specified, run all
    if (!runUnit && !runIntegration && !runWidget && !showHelp) {
      runUnit = true;
      runIntegration = true;
      runWidget = true;
    }

    return TestRunnerConfig(
      runUnit: runUnit,
      runIntegration: runIntegration,
      runWidget: runWidget,
      generateCoverage: generateCoverage,
      verbose: verbose,
      showHelp: showHelp,
    );
  }
}

class TestRunner {
  final TestRunnerConfig config;
  
  TestRunner(this.config);

  Future<void> run() async {
    print('🧪 Running Video Call Tests');
    print('=' * 50);

    var allTestsPassed = true;
    final results = <String, bool>{};

    if (config.runUnit) {
      print('\n📋 Running Unit Tests...');
      final unitResult = await _runUnitTests();
      results['Unit Tests'] = unitResult;
      allTestsPassed = allTestsPassed && unitResult;
    }

    if (config.runIntegration) {
      print('\n🔗 Running Integration Tests...');
      final integrationResult = await _runIntegrationTests();
      results['Integration Tests'] = integrationResult;
      allTestsPassed = allTestsPassed && integrationResult;
    }

    if (config.runWidget) {
      print('\n🎨 Running Widget Tests...');
      final widgetResult = await _runWidgetTests();
      results['Widget Tests'] = widgetResult;
      allTestsPassed = allTestsPassed && widgetResult;
    }

    if (config.generateCoverage) {
      print('\n📊 Generating Coverage Report...');
      await _generateCoverageReport();
    }

    _printSummary(results, allTestsPassed);
    
    exit(allTestsPassed ? 0 : 1);
  }

  Future<bool> _runUnitTests() async {
    final testFiles = [
      'test/services/webrtc_service_test.dart',
      'test/services/websocket_service_test.dart',
      'test/services/api_service_test.dart',
    ];

    return await _runTestFiles(testFiles, 'Unit');
  }

  Future<bool> _runIntegrationTests() async {
    final testFiles = [
      'test/services/call_manager_test.dart',
      'test/integration/call_flow_test.dart',
      'test/integration/network_loss_test.dart',
    ];

    return await _runTestFiles(testFiles, 'Integration');
  }

  Future<bool> _runWidgetTests() async {
    final testFiles = [
      'test/widgets/video_call_button_test.dart',
      'test/widgets/call_status_indicator_test.dart',
      'test/widgets/incoming_call_notification_test.dart',
      'test/screens/video_call_screen_test.dart',
      'test/screens/chat_screen_test.dart',
    ];

    return await _runTestFiles(testFiles, 'Widget');
  }

  Future<bool> _runTestFiles(List<String> testFiles, String category) async {
    var allPassed = true;

    for (final testFile in testFiles) {
      final file = File(testFile);
      if (!file.existsSync()) {
        print('⚠️  Test file not found: $testFile');
        continue;
      }

      print('  Running: ${testFile.split('/').last}');
      
      final result = await _runSingleTest(testFile);
      if (!result) {
        print('  ❌ Failed: ${testFile.split('/').last}');
        allPassed = false;
      } else {
        print('  ✅ Passed: ${testFile.split('/').last}');
      }
    }

    return allPassed;
  }

  Future<bool> _runSingleTest(String testFile) async {
    try {
      final args = ['test', testFile];
      if (config.verbose) {
        args.add('--verbose');
      }

      final result = await Process.run('flutter', args);
      
      if (config.verbose) {
        print(result.stdout);
        if (result.stderr.isNotEmpty) {
          print('STDERR: ${result.stderr}');
        }
      }

      return result.exitCode == 0;
    } catch (e) {
      print('Error running test $testFile: $e');
      return false;
    }
  }

  Future<void> _generateCoverageReport() async {
    try {
      // Run tests with coverage
      final result = await Process.run('flutter', [
        'test',
        '--coverage',
        'test/',
      ]);

      if (result.exitCode == 0) {
        // Generate HTML coverage report
        final lcovResult = await Process.run('genhtml', [
          'coverage/lcov.info',
          '-o',
          'coverage/html',
        ]);

        if (lcovResult.exitCode == 0) {
          print('✅ Coverage report generated: coverage/html/index.html');
        } else {
          print('⚠️  Failed to generate HTML coverage report');
          print('   Make sure lcov is installed: brew install lcov (macOS) or apt-get install lcov (Ubuntu)');
        }
      } else {
        print('❌ Failed to run tests with coverage');
      }
    } catch (e) {
      print('Error generating coverage: $e');
    }
  }

  void _printSummary(Map<String, bool> results, bool allTestsPassed) {
    print('\n' + '=' * 50);
    print('📊 Test Summary');
    print('=' * 50);

    for (final entry in results.entries) {
      final status = entry.value ? '✅ PASSED' : '❌ FAILED';
      print('${entry.key}: $status');
    }

    print('\n' + '=' * 50);
    if (allTestsPassed) {
      print('🎉 All tests passed!');
    } else {
      print('💥 Some tests failed!');
    }
    print('=' * 50);
  }
}

void _printHelp() {
  print('''
🧪 Video Call Test Runner

Usage: dart test_runner.dart [options]

Options:
  --unit          Run only unit tests
  --integration   Run only integration tests  
  --widget        Run only widget tests
  --coverage      Generate coverage report
  --verbose       Verbose output
  --help          Show this help message

Examples:
  dart test_runner.dart                    # Run all tests
  dart test_runner.dart --unit             # Run only unit tests
  dart test_runner.dart --coverage         # Run all tests with coverage
  dart test_runner.dart --unit --verbose   # Run unit tests with verbose output

Test Categories:
  Unit Tests:        Test individual services and utilities
  Integration Tests: Test complete call flows and interactions
  Widget Tests:      Test UI components and user interactions

Coverage:
  Use --coverage to generate a coverage report. Requires lcov to be installed
  for HTML report generation.
''');
}
