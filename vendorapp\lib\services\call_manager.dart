import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:logger/logger.dart';
import 'dart:async';

import 'webrtc_service.dart';
import 'websocket_service.dart';
import 'api_service.dart';

enum CallState {
  idle,
  initiating,
  ringing,
  connecting,
  connected,
  ended,
  error,
}

enum CallType {
  outgoing,
  incoming,
}

class CallManager {
  static final CallManager _instance = CallManager._internal();
  factory CallManager() => _instance;
  CallManager._internal();

  final Logger _logger = Logger();
  final WebRTCService _webrtcService = WebRTCService();
  final WebSocketService _webSocketService = WebSocketService();
  final ApiService _apiService = ApiService();

  CallState _callState = CallState.idle;
  CallType? _callType;
  String? _currentCallId;
  String? _customerId;
  String? _vendorId;

  // Callbacks
  Function(CallState state)? onCallStateChanged;
  Function(MediaStream stream)? onLocalStream;
  Function(MediaStream stream)? onRemoteStream;
  Function(String error)? onError;
  Function(String callId, String customerId, Map<String, dynamic> customerInfo)? onIncomingCall;

  /// Initialize call manager
  Future<void> initialize({
    required String signalingServerUrl,
    required String apiBaseUrl,
    required String authToken,
    required String userId,
  }) async {
    try {
      _vendorId = userId;
      
      // Initialize services
      _apiService.initialize(baseUrl: apiBaseUrl);
      await _apiService.setAuthToken(authToken);
      
      await _webrtcService.initialize();
      await _webSocketService.connect(
        serverUrl: signalingServerUrl,
        authToken: authToken,
        userId: userId,
      );

      _setupWebRTCCallbacks();
      _setupSignalingCallbacks();

      _logger.i('Vendor call manager initialized');
    } catch (e) {
      _logger.e('Failed to initialize vendor call manager: $e');
      _updateCallState(CallState.error);
      onError?.call('Failed to initialize: $e');
      rethrow;
    }
  }

  /// Set up WebRTC service callbacks
  void _setupWebRTCCallbacks() {
    _webrtcService.onLocalStream = (stream) {
      _logger.i('Vendor local stream received');
      onLocalStream?.call(stream);
    };

    _webrtcService.onRemoteStream = (stream) {
      _logger.i('Customer stream received');
      onRemoteStream?.call(stream);
      _updateCallState(CallState.connected);
    };

    _webrtcService.onConnectionStateChange = (state) {
      _logger.i('Vendor WebRTC connection state: $state');
      
      switch (state) {
        case RTCPeerConnectionState.RTCPeerConnectionStateConnecting:
          _updateCallState(CallState.connecting);
          break;
        case RTCPeerConnectionState.RTCPeerConnectionStateConnected:
          _updateCallState(CallState.connected);
          break;
        case RTCPeerConnectionState.RTCPeerConnectionStateFailed:
        case RTCPeerConnectionState.RTCPeerConnectionStateDisconnected:
          _updateCallState(CallState.error);
          onError?.call('Connection with customer failed');
          break;
        case RTCPeerConnectionState.RTCPeerConnectionStateClosed:
          _updateCallState(CallState.ended);
          break;
        default:
          break;
      }
    };

    _webrtcService.onIceCandidate = (candidate) {
      if (_currentCallId != null && _customerId != null) {
        _webSocketService.sendIceCandidate(
          callId: _currentCallId!,
          customerId: _customerId!,
          candidate: candidate,
        );
      }
    };

    _webrtcService.onError = (error) {
      _logger.e('Vendor WebRTC error: $error');
      _updateCallState(CallState.error);
      onError?.call(error);
    };
  }

  /// Set up signaling service callbacks
  void _setupSignalingCallbacks() {
    _webSocketService.messageStream.listen((message) {
      _handleSignalingMessage(message);
    });

    _webSocketService.errorStream.listen((error) {
      _logger.e('Vendor signaling error: $error');
      onError?.call('Signaling error: $error');
    });
  }

  /// Handle incoming signaling messages
  void _handleSignalingMessage(Map<String, dynamic> message) async {
    final type = message['type'] as String;
    final data = message['data'] as Map<String, dynamic>;

    switch (type) {
      case 'webrtc-offer-received':
        await _handleIncomingOffer(data);
        break;
      case 'webrtc-answer-received':
        await _handleIncomingAnswer(data);
        break;
      case 'ice-candidate-received':
        await _handleIncomingIceCandidate(data);
        break;
      case 'user-joined':
        _handleUserJoined(data);
        break;
      case 'user-left':
        _handleUserLeft(data);
        break;
      case 'incoming-call':
        await _handleIncomingCallNotification(data);
        break;
    }
  }

  /// Start an outgoing call to customer
  Future<void> startCall(String customerId) async {
    try {
      if (_callState != CallState.idle) {
        throw Exception('Already in a call');
      }

      _updateCallState(CallState.initiating);
      _callType = CallType.outgoing;
      _customerId = customerId;

      // Step 1: Initiate call via Laravel API to get call_id
      final callData = await _apiService.initiateCall(customerId: customerId);
      _currentCallId = callData['call_id'];

      _logger.i('Vendor initiated call with customer: $customerId, call_id: $_currentCallId');

      // Step 2: Join signaling room
      await _webSocketService.joinCall(_currentCallId!);

      // Step 3: Get user media (with back camera for showing products)
      await _webrtcService.getUserMedia(useBackCamera: true);

      // Step 4: Create and send WebRTC offer via WebSocket
      final offer = await _webrtcService.createOffer();
      await _webSocketService.sendOffer(
        callId: _currentCallId!,
        customerId: customerId,
        offer: offer,
      );

      _updateCallState(CallState.connecting);
      _logger.i('Vendor sent offer to customer via WebSocket');

    } catch (e) {
      _logger.e('Failed to start vendor call: $e');
      _updateCallState(CallState.error);
      onError?.call('Failed to start call: $e');
      await _cleanup();
      rethrow;
    }
  }

  /// Handle incoming call notification
  Future<void> _handleIncomingCallNotification(Map<String, dynamic> data) async {
    try {
      final callId = data['call_id'] as String;
      final fromCustomerId = data['from_user_id'] as String;
      final customerInfo = data['customer_info'] as Map<String, dynamic>? ?? {};

      if (_callState == CallState.idle) {
        _currentCallId = callId;
        _customerId = fromCustomerId;
        _callType = CallType.incoming;
        _updateCallState(CallState.ringing);

        _logger.i('Vendor received incoming call from customer: $fromCustomerId');
        onIncomingCall?.call(callId, fromCustomerId, customerInfo);
      }
    } catch (e) {
      _logger.e('Failed to handle incoming call notification: $e');
    }
  }

  /// Handle incoming WebRTC offer
  Future<void> _handleIncomingOffer(Map<String, dynamic> data) async {
    try {
      final callId = data['call_id'] as String;
      final fromCustomerId = data['from_user_id'] as String;
      final offerData = data['offer'] as Map<String, dynamic>;
      
      final offer = RTCSessionDescription(
        offerData['sdp'] as String,
        offerData['type'] as String,
      );
      
      _logger.i('Vendor received WebRTC offer from customer: $fromCustomerId');

      // Get user media (with back camera for showing products)
      await _webrtcService.getUserMedia(useBackCamera: true);

      // Set remote description
      await _webrtcService.setRemoteDescription(offer);

      // Create and send answer
      final answer = await _webrtcService.createAnswer();
      await _webSocketService.sendAnswer(
        callId: callId,
        customerId: fromCustomerId,
        answer: answer,
      );

      _updateCallState(CallState.connecting);

    } catch (e) {
      _logger.e('Failed to handle customer offer: $e');
      _updateCallState(CallState.error);
      onError?.call('Failed to answer call: $e');
      await _cleanup();
    }
  }

  /// Handle incoming WebRTC answer
  Future<void> _handleIncomingAnswer(Map<String, dynamic> data) async {
    try {
      final answerData = data['answer'] as Map<String, dynamic>;
      
      final answer = RTCSessionDescription(
        answerData['sdp'] as String,
        answerData['type'] as String,
      );
      
      _logger.i('Vendor received WebRTC answer from customer');
      await _webrtcService.setRemoteDescription(answer);

    } catch (e) {
      _logger.e('Failed to handle customer answer: $e');
      onError?.call('Failed to process customer response: $e');
    }
  }

  /// Handle incoming ICE candidate
  Future<void> _handleIncomingIceCandidate(Map<String, dynamic> data) async {
    try {
      final candidateData = data['candidate'] as Map<String, dynamic>;
      
      final candidate = RTCIceCandidate(
        candidateData['candidate'] as String,
        candidateData['sdpMid'] as String?,
        candidateData['sdpMLineIndex'] as int?,
      );
      
      _logger.d('Vendor received ICE candidate from customer');
      await _webrtcService.addIceCandidate(candidate);

    } catch (e) {
      _logger.e('Failed to handle ICE candidate: $e');
      // Don't show error for ICE candidate failures
    }
  }

  /// Handle user joined
  void _handleUserJoined(Map<String, dynamic> data) {
    final userId = data['user_id'] as String;
    _logger.i('Customer joined call: $userId');
  }

  /// Handle user left
  void _handleUserLeft(Map<String, dynamic> data) {
    final userId = data['user_id'] as String;
    if (userId == _customerId) {
      _logger.i('Customer left call: $userId');
      _updateCallState(CallState.ended);
      _cleanup();
    }
  }

  /// Accept an incoming call
  Future<void> acceptCall() async {
    try {
      if (_callState != CallState.ringing || _callType != CallType.incoming) {
        throw Exception('No incoming call to accept');
      }

      // Accept call via Laravel API
      await _apiService.acceptCall(_currentCallId!);

      // Join signaling room
      await _webSocketService.joinCall(_currentCallId!);

      _logger.i('Vendor accepted call: $_currentCallId');

    } catch (e) {
      _logger.e('Failed to accept call: $e');
      onError?.call('Failed to accept call: $e');
      await _cleanup();
    }
  }

  /// Reject an incoming call
  Future<void> rejectCall() async {
    try {
      if (_callState != CallState.ringing || _callType != CallType.incoming) {
        throw Exception('No incoming call to reject');
      }

      // Reject call via Laravel API
      await _apiService.rejectCall(_currentCallId!);

      _updateCallState(CallState.ended);
      await _cleanup();

      _logger.i('Vendor rejected call: $_currentCallId');

    } catch (e) {
      _logger.e('Failed to reject call: $e');
      onError?.call('Failed to reject call: $e');
      await _cleanup();
    }
  }

  /// End the current call
  Future<void> endCall() async {
    try {
      if (_callState == CallState.idle || _callState == CallState.ended) {
        return;
      }

      // End call via Laravel API
      if (_currentCallId != null) {
        await _apiService.endCall(_currentCallId!);
      }

      // Send leave message via WebSocket
      if (_currentCallId != null) {
        await _webSocketService.leaveCall(_currentCallId!);
      }

      _updateCallState(CallState.ended);
      await _cleanup();

      _logger.i('Vendor ended call: $_currentCallId');

    } catch (e) {
      _logger.e('Failed to end call: $e');
      onError?.call('Failed to end call: $e');
      await _cleanup();
    }
  }

  /// Toggle video
  Future<bool> toggleVideo() async {
    return await _webrtcService.toggleVideo();
  }

  /// Toggle audio
  Future<bool> toggleAudio() async {
    return await _webrtcService.toggleAudio();
  }

  /// Switch camera
  Future<void> switchCamera() async {
    await _webrtcService.switchCamera();
  }

  /// Clean up resources
  Future<void> _cleanup() async {
    try {
      await _webrtcService.dispose();
      
      _currentCallId = null;
      _customerId = null;
      _callType = null;
      
      _logger.i('Vendor call resources cleaned up');
    } catch (e) {
      _logger.e('Error during vendor cleanup: $e');
    }
  }

  /// Update call state and notify listeners
  void _updateCallState(CallState newState) {
    if (_callState != newState) {
      _callState = newState;
      onCallStateChanged?.call(_callState);
      _logger.i('Vendor call state changed to: $newState');
    }
  }

  /// Dispose call manager
  Future<void> dispose() async {
    await _cleanup();
    await _webSocketService.disconnect();
    _updateCallState(CallState.idle);
  }

  // Getters
  CallState get callState => _callState;
  CallType? get callType => _callType;
  String? get currentCallId => _currentCallId;
  String? get customerId => _customerId;
  String? get vendorId => _vendorId;
  bool get isInCall => _callState != CallState.idle && _callState != CallState.ended;
  bool get isVideoEnabled => _webrtcService.isVideoEnabled;
  bool get isAudioEnabled => _webrtcService.isAudioEnabled;
  MediaStream? get localStream => _webrtcService.localStream;
  MediaStream? get remoteStream => _webrtcService.remoteStream;
}
