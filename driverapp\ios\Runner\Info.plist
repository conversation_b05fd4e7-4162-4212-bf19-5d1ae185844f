<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string><PERSON> Driver</string>
		<key>CFBundleDisplayName</key>
		<string><PERSON> Driver</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.977502122572-9eef517oe3ontec05l5memgakcgvvbr8</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>comgooglemaps</string>
			<string>baidumap</string>
			<string>iosamap</string>
			<string>waze</string>
			<string>yandexmaps</string>
			<string>yandexnavi</string>
			<string>citymapper</string>
			<string>mapswithme</string>
			<string>osmandmaps</string>
			<string>dgis</string>
			<string>qqmap</string>
			<string>https</string>
			<string>http</string>
			<string>tel</string>
			<string>mailto</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSCameraUsageDescription</key>
		<string>We need access to your camera for profile photo update and scanning for order verification code.</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>We need access to your location to show nearby vendors and share your location with customer when delivering their order.</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>We need access to your location to show nearby vendors and share your location with customer when delivering their order.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>We need access to your location to show nearby vendors and share your location with customer when delivering their order.</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>We need access to your gallery for profile photo.</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>We need acces to your microphone for audio call</string>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>location</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
	</dict>
</plist>
